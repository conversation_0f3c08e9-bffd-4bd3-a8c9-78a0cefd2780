using System;
using System.Drawing;
using System.Windows.Forms;
using InventoryApp.Services;
using InventoryApp.Utils;

namespace InventoryApp.Forms
{
    /// <summary>
    /// Formulaire principal de l'application d'inventaire
    /// </summary>
    public partial class MainForm : Form
    {
#if USE_XML_DATABASE
        private readonly InventoryServiceSimple _inventoryService;
#else
        private readonly InventoryService _inventoryService;
#endif
        private readonly BarcodeService _barcodeService;

        /// <summary>
        /// Constructeur du formulaire principal
        /// </summary>
        public MainForm()
        {
            InitializeComponent();

#if USE_XML_DATABASE
            _inventoryService = new InventoryServiceSimple();
#else
            _inventoryService = new InventoryService();
#endif
            _barcodeService = new BarcodeService();
            
            InitializeServices();
            LoadStatistics();
        }

        /// <summary>
        /// Initialise les services
        /// </summary>
        private void InitializeServices()
        {
            // Activer le scanner de codes-barres
            _barcodeService.EnableScanner();
            
            // S'abonner aux événements du scanner
            _barcodeService.BarcodeScanned += OnBarcodeScanned;
        }

        /// <summary>
        /// Charge les statistiques sur l'écran principal
        /// </summary>
        private void LoadStatistics()
        {
            try
            {
                int totalArticles = _inventoryService.GetTotalArticlesCount();
                decimal totalValue = _inventoryService.GetTotalStockValue();
                int outOfStock = _inventoryService.GetOutOfStockArticles().Count;
                int lowStock = _inventoryService.GetLowStockArticles().Count;

                lblTotalArticles.Text = totalArticles.ToString();
                lblTotalValue.Text = CurrencyHelper.FormatCurrency(totalValue);
                lblOutOfStock.Text = outOfStock.ToString();
                lblLowStock.Text = lowStock.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erreur lors du chargement des statistiques: " + ex.Message,
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Exclamation,
                    MessageBoxDefaultButton.Button1);
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour la lecture d'un code-barres
        /// </summary>
        /// <param name="sender">Expéditeur</param>
        /// <param name="e">Arguments de l'événement</param>
        private void OnBarcodeScanned(object sender, BarcodeScannedEventArgs e)
        {
            // Afficher le code-barres scanné
            lblLastBarcode.Text = e.Barcode;
            lblScanTime.Text = e.Timestamp.ToString("HH:mm:ss");
            
            // Rechercher l'article correspondant
            var article = _inventoryService.GetArticleByBarcode(e.Barcode);
            if (article != null)
            {
                lblArticleInfo.Text = string.Format("{0} - Stock: {1}", article.Name, article.StockQuantity);
            }
            else
            {
                lblArticleInfo.Text = "Article non trouvé";
            }
        }

        #region Gestionnaires d'événements des boutons

        /// <summary>
        /// Ouvre le formulaire de gestion des articles
        /// </summary>
        private void btnArticles_Click(object sender, EventArgs e)
        {
            using (ArticleForm articleForm = new ArticleForm())
            {
                articleForm.ShowDialog();
                LoadStatistics(); // Recharger les statistiques après modification
            }
        }

        /// <summary>
        /// Ouvre le formulaire d'inventaire
        /// </summary>
        private void btnInventory_Click(object sender, EventArgs e)
        {
            using (InventoryFormSimple inventoryForm = new InventoryFormSimple())
            {
                inventoryForm.ShowDialog();
                LoadStatistics(); // Recharger les statistiques après inventaire
            }
        }

        /// <summary>
        /// Ouvre le formulaire de rapports
        /// </summary>
        private void btnReports_Click(object sender, EventArgs e)
        {
            using (ReportsForm reportsForm = new ReportsForm())
            {
                reportsForm.ShowDialog();
            }
        }

        /// <summary>
        /// Actualise les données affichées
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadStatistics();
            MessageBox.Show("Données actualisées", "Information",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1);
        }

        /// <summary>
        /// Quitte l'application
        /// </summary>
        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Voulez-vous vraiment quitter l'application ?", "Confirmation",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        #endregion

        #region Gestion des événements du formulaire

        /// <summary>
        /// Événement de chargement du formulaire
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // Configurer le formulaire pour l'écran du MC2100
            this.WindowState = FormWindowState.Maximized;
            this.Text = "Inventaire - " + DateTime.Now.ToString("dd/MM/yyyy");
        }

        /// <summary>
        /// Événement de fermeture du formulaire
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Désactiver le scanner avant de fermer
            if (_barcodeService != null)
            {
                _barcodeService.DisableScanner();
            }
        }

        /// <summary>
        /// Gestion des touches du clavier (pour le scanner)
        /// </summary>
        private void MainForm_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Le scanner envoie généralement les données suivies d'un Enter
            if (e.KeyChar == (char)Keys.Enter)
            {
                // Traiter l'entrée du scanner si nécessaire
                // (dans ce cas, l'événement BarcodeScanned est déjà géré)
            }
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Affiche un message d'information
        /// </summary>
        /// <param name="message">Message à afficher</param>
        private void ShowInfo(string message)
        {
            MessageBox.Show(message, "Information",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1);
        }

        /// <summary>
        /// Affiche un message d'erreur
        /// </summary>
        /// <param name="message">Message d'erreur</param>
        private void ShowError(string message)
        {
            MessageBox.Show(message, "Erreur",
                MessageBoxButtons.OK, MessageBoxIcon.Exclamation,
                MessageBoxDefaultButton.Button1);
        }

        #endregion
    }
}
