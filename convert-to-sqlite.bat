@echo off
REM Script pour convertir l'application vers SQLite (compatible MC2100)

echo ========================================
echo  CONVERSION VERS SQLITE
echo ========================================
echo.

echo Creation de la version SQLite...

REM Créer le dossier de la version SQLite
if not exist "InventoryApp-SQLite" mkdir "InventoryApp-SQLite"
if not exist "InventoryApp-SQLite\Models" mkdir "InventoryApp-SQLite\Models"
if not exist "InventoryApp-SQLite\Data" mkdir "InventoryApp-SQLite\Data"
if not exist "InventoryApp-SQLite\Services" mkdir "InventoryApp-SQLite\Services"
if not exist "InventoryApp-SQLite\Forms" mkdir "InventoryApp-SQLite\Forms"
if not exist "InventoryApp-SQLite\Properties" mkdir "InventoryApp-SQLite\Properties"

REM Copier les fichiers de base
copy "Program.cs" "InventoryApp-SQLite\"
copy "Models\*.*" "InventoryApp-SQLite\Models\"
copy "Services\*.*" "InventoryApp-SQLite\Services\"
copy "Forms\*.*" "InventoryApp-SQLite\Forms\"
copy "Properties\*.*" "InventoryApp-SQLite\Properties\"

echo Fichiers copies.
echo.
echo ATTENTION: Vous devez maintenant:
echo 1. Modifier Data\DatabaseHelper.cs pour utiliser SQLite
echo 2. Remplacer SqlCeConnection par SQLiteConnection
echo 3. Adapter les requetes SQL pour SQLite
echo 4. Tester sur le MC2100
echo.
echo Consultez COMPILATION_GUIDE.md pour plus de details.
echo.
pause
