@echo off
REM Verification rapide que SQLite est pret

echo ========================================
echo  VERIFICATION SQLITE
echo ========================================
echo.

echo 1. Verification du compilateur...
csc /? >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Compilateur C# disponible
) else (
    echo [ERREUR] Compilateur non disponible
    echo Solution: Ouvrir "Developer Command Prompt for VS 2022"
    goto :end
)

echo.
echo 2. Verification des fichiers SQLite...
if exist "Data\DatabaseHelper-SQLite.cs" (
    echo [OK] DatabaseHelper-SQLite.cs
) else (
    echo [MANQUE] Data\DatabaseHelper-SQLite.cs
    goto :end
)

if exist "Services\InventoryService-SQLite.cs" (
    echo [OK] InventoryService-SQLite.cs
) else (
    echo [MANQUE] Services\InventoryService-SQLite.cs
    goto :end
)

if exist "lib\System.Data.SQLite.dll" (
    echo [OK] System.Data.SQLite.dll
    for %%I in ("lib\System.Data.SQLite.dll") do echo      Taille: %%~zI octets
) else (
    echo [MANQUE] lib\System.Data.SQLite.dll
    echo Solution: Executer install-sqlite.bat
    goto :end
)

echo.
echo 3. Verification des autres fichiers...
set MISSING=0

if not exist "Program.cs" (
    echo [MANQUE] Program.cs
    set /a MISSING+=1
)
if not exist "Models\Article.cs" (
    echo [MANQUE] Models\Article.cs
    set /a MISSING+=1
)
if not exist "Utils\CurrencyHelper.cs" (
    echo [MANQUE] Utils\CurrencyHelper.cs
    set /a MISSING+=1
)

if %MISSING% gtr 0 (
    echo [ERREUR] %MISSING% fichiers manquants
    goto :end
)

echo [OK] Tous les fichiers presents
echo.

echo ========================================
echo  SQLITE PRET POUR COMPILATION !
echo ========================================
echo.
echo PROCHAINES ETAPES:
echo 1. build-sqlite-fixed.bat (recommande)
echo 2. OU build-sqlite-multiline.bat
echo.
echo FONCTIONNALITES DISPONIBLES:
echo • Base de donnees SQLite locale
echo • Devise Dinar Tunisien (DT)
echo • Gestion articles complete
echo • Rapports detailles
echo • Compatible laptop
echo.
goto :success

:end
echo.
echo ========================================
echo  CONFIGURATION INCOMPLETE
echo ========================================
echo.
echo Actions requises:
echo 1. Ouvrir "Developer Command Prompt for VS 2022"
echo 2. Executer install-sqlite.bat si SQLite manque
echo 3. Verifier que tous les fichiers sont presents
echo.
pause
exit /b 1

:success
echo Tout est pret pour la compilation SQLite !
echo.
pause
