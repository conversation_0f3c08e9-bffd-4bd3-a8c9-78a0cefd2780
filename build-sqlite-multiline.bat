@echo off
REM Script de compilation SQLite avec syntaxe multi-lignes

echo ========================================
echo  BUILD SQLITE MULTI-LIGNES
echo ========================================
echo.

REM Test de la commande csc
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

REM Vérifier SQLite
if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo Executer: install-sqlite.bat
    pause
    exit /b 1
)

echo Compilation SQLite en cours...
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

REM Compilation avec continuation de ligne Windows
csc /target:winexe ^
    /out:bin\Release\InventoryApp-SQLite.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /reference:lib\System.Data.SQLite.dll ^
    /define:USE_SQLITE_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    Properties\AssemblyInfo.cs ^
    Models\Article.cs ^
    Models\InventoryItem.cs ^
    Data\DatabaseHelper-SQLite.cs ^
    Services\BarcodeService.cs ^
    Services\InventoryService-SQLite.cs ^
    Utils\CurrencyHelper.cs ^
    Forms\MainForm.cs ^
    Forms\MainForm.Designer.cs ^
    Forms\ArticleForm.cs ^
    Forms\ArticleForm.Designer.cs ^
    Forms\InventoryForm-Simple.cs ^
    Forms\ReportsForm.cs ^
    Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo.
    echo [SUCCES] Compilation SQLite reussie !
    echo.
    
    REM Copier la DLL SQLite
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier: bin\Release\InventoryApp-SQLite.exe
    echo.
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" (
        start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe"
    )
) else (
    echo.
    echo [ERREUR] Compilation echouee
    echo Code: %ERRORLEVEL%
)

echo.
pause
