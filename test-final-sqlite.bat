@echo off
REM Test final de compilation SQLite avec toutes les corrections

echo ========================================
echo  TEST FINAL SQLITE - TOUTES CORRECTIONS
echo ========================================
echo.

echo Verification des prerequis...
echo.

REM Vérifier le compilateur
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Compilateur C# non disponible
    echo Solution: Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
) else (
    echo [OK] Compilateur C# disponible
)

REM Vérifier SQLite
if exist "lib\System.Data.SQLite.dll" (
    echo [OK] System.Data.SQLite.dll present
    for %%I in ("lib\System.Data.SQLite.dll") do echo      Taille: %%~zI octets
) else (
    echo [ERREUR] System.Data.SQLite.dll manquant
    echo.
    echo TELECHARGEMENT REQUIS:
    echo 1. https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Extraire et copier System.Data.SQLite.dll dans lib\
    pause
    exit /b 1
)

echo.
echo Verification des fichiers sources...

set MISSING=0

if not exist "Data\DatabaseHelper-SQLite-Simple.cs" (
    echo [MANQUE] DatabaseHelper-SQLite-Simple.cs
    set /a MISSING+=1
) else (
    echo [OK] DatabaseHelper-SQLite-Simple.cs
)

if not exist "Services\InventoryService-SQLite.cs" (
    echo [MANQUE] InventoryService-SQLite.cs
    set /a MISSING+=1
) else (
    echo [OK] InventoryService-SQLite.cs
)

if not exist "Forms\MainForm.cs" (
    echo [MANQUE] MainForm.cs
    set /a MISSING+=1
) else (
    echo [OK] MainForm.cs
)

if not exist "Forms\ArticleForm.cs" (
    echo [MANQUE] ArticleForm.cs
    set /a MISSING+=1
) else (
    echo [OK] ArticleForm.cs
)

if not exist "Forms\ReportsForm.cs" (
    echo [MANQUE] ReportsForm.cs
    set /a MISSING+=1
) else (
    echo [OK] ReportsForm.cs
)

if %MISSING% gtr 0 (
    echo.
    echo [ERREUR] %MISSING% fichiers manquants
    pause
    exit /b 1
)

echo [OK] Tous les fichiers sources presents
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Test" mkdir "bin\Test"

echo ========================================
echo  COMPILATION DE TEST EN COURS...
echo ========================================
echo.

REM Compilation de test avec tous les fichiers corrigés
csc /target:winexe /out:bin\Test\InventoryApp-SQLite-Final.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite-Simple.cs Services\BarcodeService.cs Services\InventoryService-SQLite.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

REM Copier la DLL SQLite
copy "lib\System.Data.SQLite.dll" "bin\Test\" >nul
echo [OK] System.Data.SQLite.dll copie

if exist "bin\Test\InventoryApp-SQLite-Final.exe" (
    echo.
    echo Fichier genere: bin\Test\InventoryApp-SQLite-Final.exe
    for %%I in ("bin\Test\InventoryApp-SQLite-Final.exe") do echo Taille: %%~zI octets
    
    echo.
    echo ========================================
    echo  APPLICATION SQLITE COMPLETE !
    echo ========================================
    echo.
    echo FONCTIONNALITES TESTEES ET CORRIGEES:
    echo ====================================
    echo • Base de donnees SQLite locale
    echo • Compatible .NET Framework 4.0
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • MainForm avec statistiques en temps reel
    echo • ArticleForm avec CRUD complet SQLite
    echo • ReportsForm avec 4 types de rapports
    echo • InventoryForm simplifie mais fonctionnel
    echo • Scanner codes-barres integre
    echo.
    
    echo MODULES CORRIGES:
    echo ================
    echo • Program.cs: Support SQLite conditionnel
    echo • MainForm.cs: InventoryServiceSQLite
    echo • ArticleForm.cs: InventoryServiceSQLite
    echo • ReportsForm.cs: InventoryServiceSQLite
    echo • DatabaseHelper-SQLite-Simple.cs: Compatible .NET 4.0
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo ========================================
        echo  LANCEMENT DU TEST SQLITE
        echo ========================================
        echo.
        start "Test Final SQLite" "bin\Test\InventoryApp-SQLite-Final.exe"
        echo.
        echo APPLICATION LANCEE !
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo ===========================
        echo 1. MENU PRINCIPAL:
        echo    - Voir les statistiques (nombre articles, valeur stock)
        echo    - Tester le scanner de codes-barres
        echo.
        echo 2. GESTION ARTICLES:
        echo    - Creer un nouvel article avec prix en DT
        echo    - Rechercher un article existant
        echo    - Modifier un article
        echo.
        echo 3. RAPPORTS:
        echo    - Rapport stock global avec valeurs en DT
        echo    - Articles en rupture de stock
        echo    - Articles en stock bas
        echo    - Rapport par categorie
        echo.
        echo 4. INVENTAIRE:
        echo    - Demarrer une session d'inventaire
        echo    - Voir les instructions d'utilisation
        echo.
        echo 5. BASE DE DONNEES:
        echo    - Le fichier InventoryDB.sqlite sera cree dans:
        echo      bin\Test\InventoryDB.sqlite
        echo    - Vous pouvez l'ouvrir avec DB Browser for SQLite
        echo.
        echo Si tout fonctionne correctement, utilisez:
        echo build-sqlite-simple.bat pour la version finale
    )
    
    echo.
    echo ========================================
    echo  PROCHAINES ETAPES
    echo ========================================
    echo.
    echo Si le test fonctionne:
    echo 1. Executer: build-sqlite-simple.bat
    echo 2. Deployer sur votre laptop
    echo 3. Creer des articles de test
    echo 4. Generer des rapports
    echo 5. Sauvegarder le fichier .sqlite
    echo.
    echo DEPLOIEMENT:
    echo ============
    echo Copier ces fichiers ensemble:
    echo • InventoryApp-SQLite.exe
    echo • System.Data.SQLite.dll
    echo • Le fichier .sqlite se cree automatiquement
    echo.
    
) else (
    echo [ERREUR] Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  TEST FINAL TERMINE
echo ========================================
echo.
echo Votre application SQLite est maintenant prete !
echo.

REM Nettoyer les fichiers de test
echo Nettoyage des fichiers de test...
rmdir /s /q "bin\Test" >nul 2>&1

pause
