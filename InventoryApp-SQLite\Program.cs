using System;
using System.Windows.Forms;
using InventoryApp.Forms;
using InventoryApp.Data;

namespace InventoryApp
{
    /// <summary>
    /// Point d'entrée principal de l'application d'inventaire
    /// </summary>
    static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // Initialiser la base de données
#if USE_XML_DATABASE
                DatabaseHelperSimple.InitializeDatabase();
#else
                DatabaseHelper.InitializeDatabase();
#endif

                // Lancer le formulaire principal
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    "Erreur lors du démarrage de l'application: " + ex.Message,
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Exclamation,
                    MessageBoxDefaultButton.Button1
                );
            }
        }
    }
}
