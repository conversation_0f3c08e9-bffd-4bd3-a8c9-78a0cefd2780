@echo off
csc /target:winexe /out:InventoryApp.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite-Simple.cs Services\BarcodeService.cs Services\InventoryService-SQLite-Simple.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo SUCCES !
    copy lib\System.Data.SQLite.dll . >nul
    echo Fichier: InventoryApp.exe
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" start InventoryApp.exe
) else (
    echo ERREUR: %ERRORLEVEL%
)
pause
