@echo off
REM Script de compilation final - Version simplifiee compatible
REM Utilise XML au lieu de SQL Server CE pour compatibilite maximale

echo ========================================
echo  BUILD FINAL - INVENTAIRE MC2100
echo  Version XML (Compatible partout)
echo ========================================
echo.

REM Rechercher le compilateur C#
set CSC_PATH=""
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo Compilateur C# .NET 4.0 (64-bit) trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo Compilateur C# .NET 4.0 trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe"
    echo Compilateur C# .NET 3.5 trouve
) else (
    echo ERREUR: Compilateur C# non trouve
    echo Veuillez installer .NET Framework 3.5 ou superieur
    pause
    exit /b 1
)

echo.

REM Nettoyer les anciens builds
echo Nettoyage...
if exist "bin" rmdir /s /q "bin" 2>nul
if exist "obj" rmdir /s /q "obj" 2>nul

REM Créer les dossiers
mkdir "bin" 2>nul
mkdir "bin\Release" 2>nul

echo Compilation en cours...
echo.

REM Compiler l'application avec la version XML
%CSC_PATH% /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /win32icon:Resources\app.ico ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /define:USE_XML_DATABASE ^
    Program.cs ^
    Properties\AssemblyInfo.cs ^
    Models\Article.cs ^
    Models\InventoryItem.cs ^
    Data\DatabaseHelper-Simple.cs ^
    Services\BarcodeService.cs ^
    Forms\MainForm.cs ^
    Forms\MainForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERREUR: Echec de la compilation
    echo.
    echo Tentative sans icone...
    
    %CSC_PATH% /target:winexe ^
        /out:bin\Release\InventoryApp.exe ^
        /reference:System.dll ^
        /reference:System.Data.dll ^
        /reference:System.Drawing.dll ^
        /reference:System.Windows.Forms.dll ^
        /reference:System.Xml.dll ^
        /define:USE_XML_DATABASE ^
        Program.cs ^
        Properties\AssemblyInfo.cs ^
        Models\Article.cs ^
        Models\InventoryItem.cs ^
        Data\DatabaseHelper-Simple.cs ^
        Services\BarcodeService.cs ^
        Forms\MainForm.cs ^
        Forms\MainForm.Designer.cs
    
    if %ERRORLEVEL% neq 0 (
        echo ERREUR FINALE: Impossible de compiler
        echo.
        echo Verifiez:
        echo 1. Tous les fichiers .cs sont presents
        echo 2. .NET Framework est installe
        echo 3. Aucune erreur de syntaxe
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

REM Créer le package de distribution
echo Creation du package de distribution...
if not exist "dist" mkdir "dist"
if not exist "dist\MC2100" mkdir "dist\MC2100"

REM Copier l'executable
copy "bin\Release\InventoryApp.exe" "dist\MC2100\"

REM Créer un fichier de configuration
echo ^<?xml version="1.0" encoding="utf-8"?^> > "dist\MC2100\InventoryApp.exe.config"
echo ^<configuration^> >> "dist\MC2100\InventoryApp.exe.config"
echo   ^<appSettings^> >> "dist\MC2100\InventoryApp.exe.config"
echo     ^<add key="DatabaseType" value="XML" /^> >> "dist\MC2100\InventoryApp.exe.config"
echo     ^<add key="ScannerEnabled" value="true" /^> >> "dist\MC2100\InventoryApp.exe.config"
echo   ^</appSettings^> >> "dist\MC2100\InventoryApp.exe.config"
echo ^</configuration^> >> "dist\MC2100\InventoryApp.exe.config"

REM Copier la documentation
copy "README.md" "dist\MC2100\"
copy "DEPLOYMENT.md" "dist\MC2100\"
copy "COMPILATION_GUIDE.md" "dist\MC2100\"

REM Créer un script d'installation pour MC2100
echo @echo off > "dist\MC2100\install-mc2100.bat"
echo REM Script d'installation pour Zebra MC2100 >> "dist\MC2100\install-mc2100.bat"
echo. >> "dist\MC2100\install-mc2100.bat"
echo echo Installation de l'application Inventaire... >> "dist\MC2100\install-mc2100.bat"
echo. >> "dist\MC2100\install-mc2100.bat"
echo if not exist "\Program Files\InventoryApp" mkdir "\Program Files\InventoryApp" >> "dist\MC2100\install-mc2100.bat"
echo copy InventoryApp.exe "\Program Files\InventoryApp\" >> "dist\MC2100\install-mc2100.bat"
echo copy InventoryApp.exe.config "\Program Files\InventoryApp\" >> "dist\MC2100\install-mc2100.bat"
echo. >> "dist\MC2100\install-mc2100.bat"
echo echo Installation terminee ! >> "dist\MC2100\install-mc2100.bat"
echo echo Lancez l'application depuis \Program Files\InventoryApp\InventoryApp.exe >> "dist\MC2100\install-mc2100.bat"
echo pause >> "dist\MC2100\install-mc2100.bat"

REM Afficher les informations
echo.
echo FICHIERS GENERES:
echo ==================
echo Application:     dist\MC2100\InventoryApp.exe
echo Configuration:   dist\MC2100\InventoryApp.exe.config
echo Installation:    dist\MC2100\install-mc2100.bat
echo Documentation:   dist\MC2100\*.md
echo.
echo CARACTERISTIQUES:
echo ==================
echo - Base de donnees: XML (compatible partout)
echo - Taille: ~50 KB
echo - Dependances: Aucune (sauf .NET Framework)
echo - Compatible: Windows CE 6.0, Windows 10/11
echo.
echo DEPLOIEMENT SUR MC2100:
echo ========================
echo 1. Copier le dossier dist\MC2100\ vers le terminal
echo 2. Executer install-mc2100.bat sur le terminal
echo 3. Ou copier manuellement vers \Program Files\InventoryApp\
echo.
echo TEST SUR PC:
echo ============
echo 1. Double-cliquer sur dist\MC2100\InventoryApp.exe
echo 2. Tester les fonctionnalites de base
echo 3. Verifier la creation du fichier InventoryDB.xml
echo.

REM Tester l'application
echo Voulez-vous tester l'application maintenant ? (O/N)
set /p TEST_APP=
if /i "%TEST_APP%"=="O" (
    echo Lancement de l'application...
    start "Test Inventaire" "dist\MC2100\InventoryApp.exe"
)

echo.
echo Compilation terminee avec succes !
echo Consultez COMPILATION_GUIDE.md pour plus d'informations.
echo.
pause
