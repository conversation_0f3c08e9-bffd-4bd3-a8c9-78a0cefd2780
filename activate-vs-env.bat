@echo off
REM Script pour activer l'environnement Visual Studio 2022

echo ========================================
echo  ACTIVATION ENVIRONNEMENT VS 2022
echo ========================================
echo.

REM Rechercher le script d'activation de Visual Studio
set VS_SETUP_SCRIPT=""

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    set VS_SETUP_SCRIPT="%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    echo Script VS 2022 Community trouve
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    set VS_SETUP_SCRIPT="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
    echo Script VS 2022 Professional trouve
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" (
    set VS_SETUP_SCRIPT="%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat"
    echo Script VS 2022 Enterprise trouve
) else (
    echo ERREUR: Script d'activation Visual Studio non trouve
    echo.
    echo SOLUTION MANUELLE:
    echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
    echo 2. Ou ouvrir Visual Studio > Tools > Command Line > Developer Command Prompt
    echo.
    pause
    exit /b 1
)

echo.
echo Activation de l'environnement Visual Studio...
call %VS_SETUP_SCRIPT%

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Impossible d'activer l'environnement VS
    pause
    exit /b 1
)

echo.
echo ========================================
echo  ENVIRONNEMENT ACTIVE !
echo ========================================
echo.

REM Tester que csc est maintenant disponible
csc /? >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [SUCCES] Compilateur C# maintenant disponible !
    echo.
    
    echo Compilation de l'application...
    call build-xml-only.bat
) else (
    echo [ERREUR] Compilateur toujours non disponible
    echo.
    echo SOLUTION:
    echo Utiliser manuellement "Developer Command Prompt for VS 2022"
    pause
)

echo.
pause
