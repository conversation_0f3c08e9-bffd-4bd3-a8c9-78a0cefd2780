using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Data.SQLite;
using SyncApp.Models;

namespace SyncApp.Services
{
    /// <summary>
    /// Service principal de synchronisation entre Terminal Zebra et Laptop
    /// </summary>
    public class SyncService
    {
        private readonly string _terminalXmlPath;
        private readonly string _laptopSqlitePath;
        private readonly string _syncLogPath;

        /// <summary>
        /// Événement déclenché lors de la progression de la synchronisation
        /// </summary>
        public event EventHandler<SyncProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Événement déclenché lors d'un message de synchronisation
        /// </summary>
        public event EventHandler<SyncMessageEventArgs> MessageReceived;

        /// <summary>
        /// Constructeur du service de synchronisation
        /// </summary>
        public SyncService()
        {
            _terminalXmlPath = "InventoryDB.xml"; // Fichier XML du terminal
            _laptopSqlitePath = "InventoryDB.sqlite"; // Base SQLite du laptop
            _syncLogPath = "SyncLog.txt"; // Journal de synchronisation
        }

        /// <summary>
        /// Constructeur avec chemins personnalisés
        /// </summary>
        public SyncService(string terminalXmlPath, string laptopSqlitePath)
        {
            _terminalXmlPath = terminalXmlPath;
            _laptopSqlitePath = laptopSqlitePath;
            _syncLogPath = "SyncLog.txt";
        }

        /// <summary>
        /// Synchronise les données du terminal vers le laptop
        /// </summary>
        public List<SyncItem> SyncTerminalToLaptop()
        {
            List<SyncItem> results = new List<SyncItem>();
            
            try
            {
                OnMessageReceived("Début de la synchronisation Terminal → Laptop");
                OnProgressChanged(0, "Lecture des données du terminal...");

                // Lire les données XML du terminal
                List<SyncItem> terminalItems = ReadTerminalXmlData();
                OnProgressChanged(25, string.Format("Trouvé {0} articles sur le terminal", terminalItems.Count));

                // Lire les données SQLite du laptop
                List<SyncItem> laptopItems = ReadLaptopSqliteData();
                OnProgressChanged(50, string.Format("Trouvé {0} articles sur le laptop", laptopItems.Count));

                // Comparer et synchroniser
                results = CompareAndSync(terminalItems, laptopItems, SyncDirection.TerminalToLaptop);
                OnProgressChanged(75, "Synchronisation des différences...");

                // Écrire les résultats dans SQLite
                WriteSqliteData(results);
                OnProgressChanged(100, "Synchronisation terminée");

                OnMessageReceived(string.Format("Synchronisation terminée: {0} articles traités", results.Count));
                LogSync("Terminal → Laptop", results.Count, 0);
            }
            catch (Exception ex)
            {
                OnMessageReceived("Erreur: " + ex.Message);
                LogSync("Terminal → Laptop", 0, 1);
                throw;
            }

            return results;
        }

        /// <summary>
        /// Synchronise les données du laptop vers le terminal
        /// </summary>
        public List<SyncItem> SyncLaptopToTerminal()
        {
            List<SyncItem> results = new List<SyncItem>();
            
            try
            {
                OnMessageReceived("Début de la synchronisation Laptop → Terminal");
                OnProgressChanged(0, "Lecture des données du laptop...");

                // Lire les données SQLite du laptop
                List<SyncItem> laptopItems = ReadLaptopSqliteData();
                OnProgressChanged(25, string.Format("Trouvé {0} articles sur le laptop", laptopItems.Count));

                // Lire les données XML du terminal
                List<SyncItem> terminalItems = ReadTerminalXmlData();
                OnProgressChanged(50, string.Format("Trouvé {0} articles sur le terminal", terminalItems.Count));

                // Comparer et synchroniser
                results = CompareAndSync(laptopItems, terminalItems, SyncDirection.LaptopToTerminal);
                OnProgressChanged(75, "Synchronisation des différences...");

                // Écrire les résultats dans XML
                WriteXmlData(results);
                OnProgressChanged(100, "Synchronisation terminée");

                OnMessageReceived(string.Format("Synchronisation terminée: {0} articles traités", results.Count));
                LogSync("Laptop → Terminal", results.Count, 0);
            }
            catch (Exception ex)
            {
                OnMessageReceived("Erreur: " + ex.Message);
                LogSync("Laptop → Terminal", 0, 1);
                throw;
            }

            return results;
        }

        /// <summary>
        /// Synchronisation bidirectionnelle avec résolution de conflits
        /// </summary>
        public List<SyncItem> SyncBidirectional()
        {
            List<SyncItem> results = new List<SyncItem>();
            
            try
            {
                OnMessageReceived("Début de la synchronisation bidirectionnelle");
                OnProgressChanged(0, "Analyse des deux sources...");

                // Lire les deux sources
                List<SyncItem> terminalItems = ReadTerminalXmlData();
                List<SyncItem> laptopItems = ReadLaptopSqliteData();

                OnProgressChanged(30, "Détection des conflits...");

                // Détecter les conflits
                List<SyncItem> conflicts = DetectConflicts(terminalItems, laptopItems);
                
                if (conflicts.Count > 0)
                {
                    OnMessageReceived(string.Format("ATTENTION: {0} conflits détectés", conflicts.Count));
                    // Les conflits devront être résolus manuellement
                    results.AddRange(conflicts);
                }

                OnProgressChanged(60, "Synchronisation des éléments sans conflit...");

                // Synchroniser les éléments sans conflit
                List<SyncItem> noConflictItems = GetItemsWithoutConflicts(terminalItems, laptopItems, conflicts);
                results.AddRange(noConflictItems);

                OnProgressChanged(100, "Synchronisation bidirectionnelle terminée");
                OnMessageReceived(string.Format("Synchronisation terminée: {0} articles, {1} conflits", 
                    results.Count - conflicts.Count, conflicts.Count));

                LogSync("Bidirectionnelle", results.Count, conflicts.Count);
            }
            catch (Exception ex)
            {
                OnMessageReceived("Erreur: " + ex.Message);
                LogSync("Bidirectionnelle", 0, 1);
                throw;
            }

            return results;
        }

        /// <summary>
        /// Lit les données XML du terminal
        /// </summary>
        private List<SyncItem> ReadTerminalXmlData()
        {
            List<SyncItem> items = new List<SyncItem>();

            if (!File.Exists(_terminalXmlPath))
            {
                OnMessageReceived("Fichier XML du terminal non trouvé: " + _terminalXmlPath);
                return items;
            }

            try
            {
                XmlDocument doc = new XmlDocument();
                doc.Load(_terminalXmlPath);

                XmlNodeList articleNodes = doc.SelectNodes("//Article");
                foreach (XmlNode node in articleNodes)
                {
                    SyncItem item = new SyncItem
                    {
                        Barcode = GetXmlValue(node, "Barcode"),
                        Name = GetXmlValue(node, "Name"),
                        Description = GetXmlValue(node, "Description"),
                        Category = GetXmlValue(node, "Category"),
                        UnitPrice = decimal.Parse(GetXmlValue(node, "UnitPrice") ?? "0"),
                        StockQuantity = int.Parse(GetXmlValue(node, "StockQuantity") ?? "0"),
                        MinimumStock = int.Parse(GetXmlValue(node, "MinimumStock") ?? "0"),
                        Unit = GetXmlValue(node, "Unit") ?? "pièce",
                        Location = GetXmlValue(node, "Location"),
                        Source = "Terminal",
                        Status = SyncStatus.Pending
                    };

                    // Essayer de parser les dates
                    string createdDate = GetXmlValue(node, "CreatedDate");
                    if (!string.IsNullOrEmpty(createdDate))
                    {
                        DateTime tempCreatedDate;
                        if (DateTime.TryParse(createdDate, out tempCreatedDate))
                        {
                            item.CreatedDate = tempCreatedDate;
                        }
                    }

                    string lastModified = GetXmlValue(node, "LastModified");
                    if (!string.IsNullOrEmpty(lastModified))
                    {
                        DateTime tempLastModified;
                        if (DateTime.TryParse(lastModified, out tempLastModified))
                        {
                            item.LastModified = tempLastModified;
                        }
                    }

                    items.Add(item);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Erreur lors de la lecture du fichier XML: " + ex.Message);
            }

            return items;
        }

        /// <summary>
        /// Lit les données SQLite du laptop
        /// </summary>
        private List<SyncItem> ReadLaptopSqliteData()
        {
            List<SyncItem> items = new List<SyncItem>();

            if (!File.Exists(_laptopSqlitePath))
            {
                OnMessageReceived("Base SQLite du laptop non trouvée: " + _laptopSqlitePath);
                return items;
            }

            try
            {
                string connectionString = "Data Source=" + _laptopSqlitePath + ";Version=3;";
                using (SQLiteConnection connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    string sql = "SELECT * FROM Articles WHERE IsActive = 1";
                    
                    using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                    {
                        using (SQLiteDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SyncItem item = new SyncItem
                                {
                                    Id = Convert.ToInt32(reader["Id"]),
                                    Barcode = reader["Barcode"].ToString(),
                                    Name = reader["Name"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    Category = reader["Category"].ToString(),
                                    UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                                    StockQuantity = Convert.ToInt32(reader["StockQuantity"]),
                                    MinimumStock = Convert.ToInt32(reader["MinimumStock"]),
                                    Unit = reader["Unit"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    LastModified = Convert.ToDateTime(reader["LastModified"]),
                                    Source = "Laptop",
                                    Status = SyncStatus.Pending
                                };

                                items.Add(item);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Erreur lors de la lecture de la base SQLite: " + ex.Message);
            }

            return items;
        }

        /// <summary>
        /// Compare et synchronise deux listes d'articles
        /// </summary>
        private List<SyncItem> CompareAndSync(List<SyncItem> sourceItems, List<SyncItem> targetItems, SyncDirection direction)
        {
            List<SyncItem> results = new List<SyncItem>();

            // Créer un dictionnaire des articles cibles pour recherche rapide
            Dictionary<string, SyncItem> targetDict = new Dictionary<string, SyncItem>();
            foreach (SyncItem item in targetItems)
            {
                if (!targetDict.ContainsKey(item.Barcode))
                {
                    targetDict[item.Barcode] = item;
                }
            }

            // Comparer chaque article source
            foreach (SyncItem sourceItem in sourceItems)
            {
                if (targetDict.ContainsKey(sourceItem.Barcode))
                {
                    // Article existe dans les deux - vérifier les modifications
                    SyncItem targetItem = targetDict[sourceItem.Barcode];
                    
                    if (IsItemModified(sourceItem, targetItem))
                    {
                        sourceItem.Status = SyncStatus.Modified;
                        sourceItem.SyncComments = "Article modifié";
                    }
                    else
                    {
                        sourceItem.Status = SyncStatus.Synchronized;
                        sourceItem.SyncComments = "Article identique";
                    }
                }
                else
                {
                    // Nouvel article
                    sourceItem.Status = SyncStatus.New;
                    sourceItem.SyncComments = "Nouvel article";
                }

                results.Add(sourceItem);
            }

            return results;
        }

        /// <summary>
        /// Vérifie si un article a été modifié
        /// </summary>
        private bool IsItemModified(SyncItem item1, SyncItem item2)
        {
            return item1.Name != item2.Name ||
                   item1.UnitPrice != item2.UnitPrice ||
                   item1.StockQuantity != item2.StockQuantity ||
                   item1.LastModified != item2.LastModified;
        }

        /// <summary>
        /// Détecte les conflits entre deux sources
        /// </summary>
        private List<SyncItem> DetectConflicts(List<SyncItem> terminalItems, List<SyncItem> laptopItems)
        {
            List<SyncItem> conflicts = new List<SyncItem>();
            
            // Logique de détection de conflits à implémenter
            // Pour l'instant, retourne une liste vide
            
            return conflicts;
        }

        /// <summary>
        /// Obtient les articles sans conflits
        /// </summary>
        private List<SyncItem> GetItemsWithoutConflicts(List<SyncItem> terminalItems, List<SyncItem> laptopItems, List<SyncItem> conflicts)
        {
            List<SyncItem> results = new List<SyncItem>();
            
            // Logique à implémenter
            
            return results;
        }

        /// <summary>
        /// Écrit les données dans SQLite
        /// </summary>
        private void WriteSqliteData(List<SyncItem> items)
        {
            // Logique d'écriture SQLite à implémenter
        }

        /// <summary>
        /// Écrit les données dans XML
        /// </summary>
        private void WriteXmlData(List<SyncItem> items)
        {
            // Logique d'écriture XML à implémenter
        }

        /// <summary>
        /// Obtient la valeur d'un nœud XML
        /// </summary>
        private string GetXmlValue(XmlNode parentNode, string nodeName)
        {
            XmlNode node = parentNode.SelectSingleNode(nodeName);
            return node?.InnerText;
        }

        /// <summary>
        /// Enregistre un log de synchronisation
        /// </summary>
        private void LogSync(string operation, int itemsCount, int errorsCount)
        {
            try
            {
                string logEntry = string.Format("{0:yyyy-MM-dd HH:mm:ss} - {1}: {2} articles, {3} erreurs",
                    DateTime.Now, operation, itemsCount, errorsCount);
                
                File.AppendAllText(_syncLogPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Ignorer les erreurs de log
            }
        }

        /// <summary>
        /// Déclenche l'événement de progression
        /// </summary>
        protected virtual void OnProgressChanged(int percentage, string message)
        {
            ProgressChanged?.Invoke(this, new SyncProgressEventArgs(percentage, message));
        }

        /// <summary>
        /// Déclenche l'événement de message
        /// </summary>
        protected virtual void OnMessageReceived(string message)
        {
            MessageReceived?.Invoke(this, new SyncMessageEventArgs(message));
        }
    }

    /// <summary>
    /// Direction de synchronisation
    /// </summary>
    public enum SyncDirection
    {
        TerminalToLaptop,
        LaptopToTerminal,
        Bidirectional
    }

    /// <summary>
    /// Arguments d'événement pour la progression de synchronisation
    /// </summary>
    public class SyncProgressEventArgs : EventArgs
    {
        public int Percentage { get; }
        public string Message { get; }

        public SyncProgressEventArgs(int percentage, string message)
        {
            Percentage = percentage;
            Message = message;
        }
    }

    /// <summary>
    /// Arguments d'événement pour les messages de synchronisation
    /// </summary>
    public class SyncMessageEventArgs : EventArgs
    {
        public string Message { get; }
        public DateTime Timestamp { get; }

        public SyncMessageEventArgs(string message)
        {
            Message = message;
            Timestamp = DateTime.Now;
        }
    }
}
