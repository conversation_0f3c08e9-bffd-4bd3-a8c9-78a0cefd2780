@echo off
REM Script de diagnostic pour identifier les problemes de compilation

echo ========================================
echo  DIAGNOSTIC SYSTEME - INVENTAIRE
echo ========================================
echo.

echo 1. VERIFICATION DU SYSTEME
echo ===========================
echo Version Windows: 
ver
echo.
echo Architecture:
echo %PROCESSOR_ARCHITECTURE%
echo.

echo 2. VERIFICATION .NET FRAMEWORK
echo ===============================
echo Recherche des versions .NET Framework installees...
echo.

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319" (
    echo [OK] .NET Framework 4.0 trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework\v4.0.30319
) else (
    echo [ERREUR] .NET Framework 4.0 NON trouve
)

if exist "%WINDIR%\Microsoft.NET\Framework\v3.5" (
    echo [OK] .NET Framework 3.5 trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework\v3.5
) else (
    echo [ERREUR] .NET Framework 3.5 NON trouve
)

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319" (
    echo [OK] .NET Framework 4.0 (64-bit) trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework64\v4.0.30319
) else (
    echo [INFO] .NET Framework 4.0 (64-bit) non trouve (normal sur systemes 32-bit)
)

echo.

echo 3. VERIFICATION COMPILATEUR C#
echo ===============================
set CSC_FOUND=0

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    echo [OK] Compilateur C# 4.0 (64-bit) trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe
    set CSC_FOUND=1
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    echo [OK] Compilateur C# 4.0 trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe
    set CSC_FOUND=1
)

if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe" (
    echo [OK] Compilateur C# 3.5 trouve
    echo Chemin: %WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe
    set CSC_FOUND=1
)

if %CSC_FOUND%==0 (
    echo [ERREUR] Aucun compilateur C# trouve !
    echo.
    echo SOLUTION: Installer .NET Framework SDK ou Visual Studio
) else (
    echo [OK] Au moins un compilateur C# disponible
)

echo.

echo 4. VERIFICATION FICHIERS SOURCE
echo ================================
set MISSING_FILES=0

echo Verification des fichiers requis...
if exist "Program.cs" (
    echo [OK] Program.cs
) else (
    echo [ERREUR] Program.cs MANQUANT
    set MISSING_FILES=1
)

if exist "Properties\AssemblyInfo.cs" (
    echo [OK] Properties\AssemblyInfo.cs
) else (
    echo [ERREUR] Properties\AssemblyInfo.cs MANQUANT
    set MISSING_FILES=1
)

if exist "Models\Article.cs" (
    echo [OK] Models\Article.cs
) else (
    echo [ERREUR] Models\Article.cs MANQUANT
    set MISSING_FILES=1
)

if exist "Data\DatabaseHelper-Simple.cs" (
    echo [OK] Data\DatabaseHelper-Simple.cs
) else (
    echo [ERREUR] Data\DatabaseHelper-Simple.cs MANQUANT
    set MISSING_FILES=1
)

if exist "Forms\MainForm.cs" (
    echo [OK] Forms\MainForm.cs
) else (
    echo [ERREUR] Forms\MainForm.cs MANQUANT
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo.
    echo [ERREUR] Des fichiers source sont manquants !
) else (
    echo.
    echo [OK] Tous les fichiers source essentiels sont presents
)

echo.

echo 5. VERIFICATION PERMISSIONS
echo ============================
echo Test d'ecriture dans le repertoire courant...
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo [OK] Permissions d'ecriture OK
    del test_write.tmp >nul 2>&1
) else (
    echo [ERREUR] Pas de permissions d'ecriture !
    echo SOLUTION: Executer en tant qu'administrateur
)

echo.

echo 6. VERIFICATION VISUAL STUDIO
echo ==============================
set VS_FOUND=0

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2022 Community trouve
    set VS_FOUND=1
)

if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2019 Community trouve
    set VS_FOUND=1
)

if %VS_FOUND%==0 (
    echo [INFO] Visual Studio non trouve (optionnel)
    echo Pour une compilation complete, installer Visual Studio Community
) else (
    echo [OK] Visual Studio disponible
)

echo.

echo ========================================
echo  RESUME DU DIAGNOSTIC
echo ========================================

if %CSC_FOUND%==1 if %MISSING_FILES%==0 (
    echo [OK] SYSTEME PRET POUR LA COMPILATION
    echo.
    echo Vous pouvez essayer:
    echo 1. build-final.bat
    echo 2. build-simple.bat
    echo.
) else (
    echo [ERREUR] PROBLEMES DETECTES
    echo.
    if %CSC_FOUND%==0 (
        echo - Compilateur C# manquant
        echo   Solution: Installer .NET Framework SDK
    )
    if %MISSING_FILES%==1 (
        echo - Fichiers source manquants
        echo   Solution: Verifier l'extraction complete du projet
    )
    echo.
)

echo ACTIONS RECOMMANDEES:
echo 1. Si compilateur manquant: Installer Visual Studio Community
echo 2. Si fichiers manquants: Re-telecharger le projet
echo 3. Si permissions: Executer en tant qu'administrateur
echo 4. Essayer build-simple.bat en premier
echo.

echo Appuyez sur une touche pour continuer...
pause >nul
