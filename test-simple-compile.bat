@echo off
echo Test compilation simple...

echo Test 1: Program.cs avec dependances
csc /target:exe /out:test1.exe /reference:System.dll /reference:System.Windows.Forms.dll Program.cs Forms\MainForm.cs Forms\MainForm.Designer.cs
if %ERRORLEVEL% neq 0 (
    echo ERREUR dans Program.cs ou MainForm
    goto end
)
del test1.exe >nul 2>&1
echo [OK] Program.cs et MainForm

echo Test 2: Models
csc /target:library /out:test2.dll Models\Article.cs Models\InventoryItem.cs
if %ERRORLEVEL% neq 0 (
    echo ERREUR dans Models
    goto end
)
del test2.dll >nul 2>&1

echo Test 3: Utils
csc /target:library /out:test3.dll /reference:System.dll Utils\CurrencyHelper.cs
if %ERRORLEVEL% neq 0 (
    echo ERREUR dans Utils\CurrencyHelper.cs
    goto end
)
del test3.dll >nul 2>&1

echo Test 4: Services
csc /target:library /out:test4.dll /reference:System.dll Services\BarcodeService.cs
if %ERRORLEVEL% neq 0 (
    echo ERREUR dans Services\BarcodeService.cs
    goto end
)
del test4.dll >nul 2>&1

echo Tous les tests OK - Le probleme vient de la ligne de commande longue

:end
pause
