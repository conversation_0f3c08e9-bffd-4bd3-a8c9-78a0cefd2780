@echo off
REM Script pour localiser le compilateur C# sur le systeme

echo ========================================
echo  RECHERCHE DU COMPILATEUR C#
echo ========================================
echo.

echo Recherche en cours...
echo.

echo 1. VISUAL STUDIO 2022
echo ======================
set VS2022_FOUND=0

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community" (
    echo [TROUVE] Visual Studio 2022 Community installe
    echo Chemin: %ProgramFiles%\Microsoft Visual Studio\2022\Community
    set VS2022_FOUND=1
    
    if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
        echo [OK] Compilateur C# trouve !
        echo Chemin: %ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe
        
        REM Tester le compilateur
        echo.
        echo Test du compilateur...
        "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" /help >nul 2>&1
        if %ERRORLEVEL% equ 0 (
            echo [OK] Compilateur fonctionnel !
        ) else (
            echo [ERREUR] Compilateur ne repond pas
        )
    ) else (
        echo [MANQUE] Compilateur C# non trouve dans Visual Studio 2022
        echo Chemin attendu: %ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe
    )
) else (
    echo [NON TROUVE] Visual Studio 2022 Community non installe
)

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional" (
    echo [TROUVE] Visual Studio 2022 Professional installe
    set VS2022_FOUND=1
)

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise" (
    echo [TROUVE] Visual Studio 2022 Enterprise installe
    set VS2022_FOUND=1
)

echo.

echo 2. .NET FRAMEWORK CLASSIQUE
echo ============================

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    echo [TROUVE] .NET Framework 4.0 (64-bit)
    echo Chemin: %WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe
) else (
    echo [NON TROUVE] .NET Framework 4.0 (64-bit)
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    echo [TROUVE] .NET Framework 4.0 (32-bit)
    echo Chemin: %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe
) else (
    echo [NON TROUVE] .NET Framework 4.0 (32-bit)
)

echo.

echo 3. RECHERCHE AUTOMATIQUE
echo =========================
echo Recherche de tous les fichiers csc.exe sur le systeme...
echo (Cela peut prendre quelques secondes)
echo.

for /f "delims=" %%i in ('dir /s /b "%ProgramFiles%\*csc.exe" 2^>nul') do (
    echo [TROUVE] %%i
)

for /f "delims=" %%i in ('dir /s /b "%ProgramFiles(x86)%\*csc.exe" 2^>nul') do (
    echo [TROUVE] %%i
)

for /f "delims=" %%i in ('dir /s /b "%WINDIR%\Microsoft.NET\*csc.exe" 2^>nul') do (
    echo [TROUVE] %%i
)

echo.

echo 4. VARIABLES D'ENVIRONNEMENT
echo =============================
echo PATH actuel (extrait):
echo %PATH% | findstr /i "visual\|microsoft\|msbuild"
echo.

echo 5. COMMANDES DEVELOPER
echo ======================
echo Test de 'dotnet' command:
dotnet --version 2>nul
if %ERRORLEVEL% equ 0 (
    echo [OK] dotnet CLI disponible
) else (
    echo [NON TROUVE] dotnet CLI non disponible
)

echo.

echo ========================================
echo  DIAGNOSTIC ET SOLUTIONS
echo ========================================
echo.

if %VS2022_FOUND% equ 1 (
    echo DIAGNOSTIC: Visual Studio 2022 est installe
    echo.
    echo SOLUTIONS:
    echo 1. Redemarrer votre PC
    echo 2. Ouvrir "Developer Command Prompt for VS 2022"
    echo 3. Executer build-quick.bat depuis cette invite
    echo.
    echo OU utiliser MSBuild directement:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers votre dossier projet
    echo 3. Executer: msbuild InventoryApp.sln
    echo.
) else (
    echo DIAGNOSTIC: Visual Studio 2022 non detecte
    echo.
    echo SOLUTIONS:
    echo 1. Reinstaller Visual Studio 2022 Community
    echo 2. Selectionner "Developpement .NET Desktop"
    echo 3. Inclure ".NET Framework 4.8 SDK"
    echo.
)

echo ALTERNATIVE: Utiliser Developer Command Prompt
echo ==============================================
echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
echo 2. Naviguer vers: cd "C:\Users\<USER>\Desktop\Inventaire"
echo 3. Executer: build-quick.bat
echo.

echo Cette methode utilise l'environnement Visual Studio
echo qui configure automatiquement tous les chemins.
echo.

pause
