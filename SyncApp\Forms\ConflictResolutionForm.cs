using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using SyncApp.Models;

namespace SyncApp.Forms
{
    /// <summary>
    /// Formulaire de résolution des conflits de synchronisation
    /// </summary>
    public partial class ConflictResolutionForm : Form
    {
        private readonly List<SyncItem> _conflicts;
        private int _currentConflictIndex;
        private SyncItem _currentConflict;

        /// <summary>
        /// Résultats de la résolution des conflits
        /// </summary>
        public List<SyncItem> ResolvedConflicts { get; private set; }

        /// <summary>
        /// Constructeur du formulaire de résolution de conflits
        /// </summary>
        public ConflictResolutionForm(List<SyncItem> conflicts)
        {
            InitializeComponent();
            _conflicts = conflicts ?? new List<SyncItem>();
            ResolvedConflicts = new List<SyncItem>();
            _currentConflictIndex = 0;
            
            LoadConflicts();
        }

        /// <summary>
        /// Charge les conflits dans l'interface
        /// </summary>
        private void LoadConflicts()
        {
            if (_conflicts.Count == 0)
            {
                MessageBox.Show("Aucun conflit à résoudre.", "Résolution de conflits",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
                return;
            }

            lblConflictCount.Text = string.Format("Conflit {0} sur {1}", 
                _currentConflictIndex + 1, _conflicts.Count);
            
            LoadCurrentConflict();
        }

        /// <summary>
        /// Charge le conflit actuel dans l'interface
        /// </summary>
        private void LoadCurrentConflict()
        {
            if (_currentConflictIndex >= 0 && _currentConflictIndex < _conflicts.Count)
            {
                _currentConflict = _conflicts[_currentConflictIndex];
                DisplayConflictDetails();
                UpdateNavigationButtons();
            }
        }

        /// <summary>
        /// Affiche les détails du conflit actuel
        /// </summary>
        private void DisplayConflictDetails()
        {
            if (_currentConflict == null) return;

            // Informations générales
            lblBarcode.Text = "Code-barres: " + _currentConflict.Barcode;
            lblArticleName.Text = "Article: " + _currentConflict.Name;
            lblConflictType.Text = "Type de conflit: " + GetConflictTypeText(_currentConflict);

            // Version Terminal (supposée être la source originale)
            txtTerminalName.Text = _currentConflict.Name;
            txtTerminalPrice.Text = _currentConflict.UnitPrice.ToString("0.000");
            txtTerminalStock.Text = _currentConflict.StockQuantity.ToString();
            txtTerminalCategory.Text = _currentConflict.Category;
            txtTerminalLocation.Text = _currentConflict.Location;
            lblTerminalDate.Text = "Modifié: " + _currentConflict.LastModified.ToString("dd/MM/yyyy HH:mm");

            // Version Laptop (supposée être la version modifiée)
            // Pour cet exemple, on simule des différences
            txtLaptopName.Text = _currentConflict.Name + " (Modifié)";
            txtLaptopPrice.Text = (_currentConflict.UnitPrice + 1).ToString("0.000");
            txtLaptopStock.Text = (_currentConflict.StockQuantity + 5).ToString();
            txtLaptopCategory.Text = _currentConflict.Category;
            txtLaptopLocation.Text = _currentConflict.Location;
            lblLaptopDate.Text = "Modifié: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm");

            // Commentaires du conflit
            txtConflictComments.Text = _currentConflict.SyncComments ?? "Conflit détecté lors de la synchronisation";
        }

        /// <summary>
        /// Obtient le texte descriptif du type de conflit
        /// </summary>
        private string GetConflictTypeText(SyncItem item)
        {
            // Logique simplifiée pour déterminer le type de conflit
            return "Modification simultanée";
        }

        /// <summary>
        /// Met à jour l'état des boutons de navigation
        /// </summary>
        private void UpdateNavigationButtons()
        {
            btnPrevious.Enabled = _currentConflictIndex > 0;
            btnNext.Enabled = _currentConflictIndex < _conflicts.Count - 1;
            
            // Mettre à jour le texte du bouton selon la position
            if (_currentConflictIndex == _conflicts.Count - 1)
            {
                btnNext.Text = "Terminer";
            }
            else
            {
                btnNext.Text = "Suivant >";
            }
        }

        /// <summary>
        /// Utiliser la version du terminal
        /// </summary>
        private void btnUseTerminal_Click(object sender, EventArgs e)
        {
            if (_currentConflict != null)
            {
                // Garder la version du terminal (pas de modification)
                _currentConflict.Status = SyncStatus.Synchronized;
                _currentConflict.SyncComments = "Conflit résolu: Version Terminal conservée";
                ResolvedConflicts.Add(_currentConflict);
                
                MoveToNextConflict();
            }
        }

        /// <summary>
        /// Utiliser la version du laptop
        /// </summary>
        private void btnUseLaptop_Click(object sender, EventArgs e)
        {
            if (_currentConflict != null)
            {
                // Utiliser la version du laptop (modifier les valeurs)
                _currentConflict.Name = txtLaptopName.Text;
                _currentConflict.UnitPrice = decimal.Parse(txtLaptopPrice.Text);
                _currentConflict.StockQuantity = int.Parse(txtLaptopStock.Text);
                _currentConflict.Category = txtLaptopCategory.Text;
                _currentConflict.Location = txtLaptopLocation.Text;
                _currentConflict.LastModified = DateTime.Now;
                
                _currentConflict.Status = SyncStatus.Modified;
                _currentConflict.SyncComments = "Conflit résolu: Version Laptop appliquée";
                ResolvedConflicts.Add(_currentConflict);
                
                MoveToNextConflict();
            }
        }

        /// <summary>
        /// Fusionner manuellement les versions
        /// </summary>
        private void btnMerge_Click(object sender, EventArgs e)
        {
            if (_currentConflict != null)
            {
                // Permettre la modification manuelle
                EnableManualEditing(true);
                btnSaveManual.Visible = true;
                btnCancelManual.Visible = true;
                
                // Désactiver les autres boutons
                btnUseTerminal.Enabled = false;
                btnUseLaptop.Enabled = false;
                btnMerge.Enabled = false;
            }
        }

        /// <summary>
        /// Sauvegarder la fusion manuelle
        /// </summary>
        private void btnSaveManual_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentConflict != null)
                {
                    // Appliquer les modifications manuelles
                    _currentConflict.Name = txtManualName.Text;
                    _currentConflict.UnitPrice = decimal.Parse(txtManualPrice.Text);
                    _currentConflict.StockQuantity = int.Parse(txtManualStock.Text);
                    _currentConflict.Category = txtManualCategory.Text;
                    _currentConflict.Location = txtManualLocation.Text;
                    _currentConflict.LastModified = DateTime.Now;
                    
                    _currentConflict.Status = SyncStatus.Modified;
                    _currentConflict.SyncComments = "Conflit résolu: Fusion manuelle";
                    ResolvedConflicts.Add(_currentConflict);
                    
                    EnableManualEditing(false);
                    btnSaveManual.Visible = false;
                    btnCancelManual.Visible = false;
                    
                    MoveToNextConflict();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erreur lors de la sauvegarde: " + ex.Message,
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Annuler la fusion manuelle
        /// </summary>
        private void btnCancelManual_Click(object sender, EventArgs e)
        {
            EnableManualEditing(false);
            btnSaveManual.Visible = false;
            btnCancelManual.Visible = false;
            
            // Réactiver les autres boutons
            btnUseTerminal.Enabled = true;
            btnUseLaptop.Enabled = true;
            btnMerge.Enabled = true;
            
            // Recharger les données originales
            LoadManualEditingFields();
        }

        /// <summary>
        /// Active ou désactive l'édition manuelle
        /// </summary>
        private void EnableManualEditing(bool enabled)
        {
            txtManualName.Enabled = enabled;
            txtManualPrice.Enabled = enabled;
            txtManualStock.Enabled = enabled;
            txtManualCategory.Enabled = enabled;
            txtManualLocation.Enabled = enabled;
            
            if (enabled)
            {
                LoadManualEditingFields();
            }
        }

        /// <summary>
        /// Charge les champs d'édition manuelle
        /// </summary>
        private void LoadManualEditingFields()
        {
            if (_currentConflict != null)
            {
                txtManualName.Text = _currentConflict.Name;
                txtManualPrice.Text = _currentConflict.UnitPrice.ToString("0.000");
                txtManualStock.Text = _currentConflict.StockQuantity.ToString();
                txtManualCategory.Text = _currentConflict.Category;
                txtManualLocation.Text = _currentConflict.Location;
            }
        }

        /// <summary>
        /// Passer au conflit suivant
        /// </summary>
        private void MoveToNextConflict()
        {
            if (_currentConflictIndex < _conflicts.Count - 1)
            {
                _currentConflictIndex++;
                lblConflictCount.Text = string.Format("Conflit {0} sur {1}", 
                    _currentConflictIndex + 1, _conflicts.Count);
                LoadCurrentConflict();
            }
            else
            {
                // Tous les conflits ont été résolus
                MessageBox.Show(
                    string.Format("Tous les conflits ont été résolus !\n\n{0} conflits traités.", 
                        ResolvedConflicts.Count),
                    "Résolution terminée",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        /// <summary>
        /// Conflit précédent
        /// </summary>
        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (_currentConflictIndex > 0)
            {
                _currentConflictIndex--;
                lblConflictCount.Text = string.Format("Conflit {0} sur {1}", 
                    _currentConflictIndex + 1, _conflicts.Count);
                LoadCurrentConflict();
            }
        }

        /// <summary>
        /// Conflit suivant ou terminer
        /// </summary>
        private void btnNext_Click(object sender, EventArgs e)
        {
            if (_currentConflictIndex == _conflicts.Count - 1)
            {
                // Terminer sans résoudre ce conflit
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MoveToNextConflict();
            }
        }

        /// <summary>
        /// Ignorer ce conflit
        /// </summary>
        private void btnSkip_Click(object sender, EventArgs e)
        {
            if (_currentConflict != null)
            {
                _currentConflict.Status = SyncStatus.Conflict;
                _currentConflict.SyncComments = "Conflit ignoré par l'utilisateur";
                // Ne pas ajouter aux conflits résolus
                
                MoveToNextConflict();
            }
        }

        /// <summary>
        /// Annuler la résolution de conflits
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(
                "Voulez-vous vraiment annuler la résolution des conflits ?\n\n" +
                "Les conflits non résolus resteront en attente.",
                "Annuler la résolution",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        // Déclarations des contrôles (normalement dans le Designer)
        private Label lblConflictCount;
        private Label lblBarcode;
        private Label lblArticleName;
        private Label lblConflictType;
        private TextBox txtTerminalName;
        private TextBox txtTerminalPrice;
        private TextBox txtTerminalStock;
        private TextBox txtTerminalCategory;
        private TextBox txtTerminalLocation;
        private Label lblTerminalDate;
        private TextBox txtLaptopName;
        private TextBox txtLaptopPrice;
        private TextBox txtLaptopStock;
        private TextBox txtLaptopCategory;
        private TextBox txtLaptopLocation;
        private Label lblLaptopDate;
        private TextBox txtConflictComments;
        private TextBox txtManualName;
        private TextBox txtManualPrice;
        private TextBox txtManualStock;
        private TextBox txtManualCategory;
        private TextBox txtManualLocation;
        private Button btnUseTerminal;
        private Button btnUseLaptop;
        private Button btnMerge;
        private Button btnSaveManual;
        private Button btnCancelManual;
        private Button btnPrevious;
        private Button btnNext;
        private Button btnSkip;
        private Button btnCancel;
    }
}
