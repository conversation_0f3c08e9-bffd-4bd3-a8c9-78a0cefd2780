using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using SyncApp.Models;

namespace SyncApp.Forms
{
    /// <summary>
    /// Formulaire d'affichage des résultats de synchronisation
    /// </summary>
    public partial class SyncResultsForm : Form
    {
        private readonly List<SyncItem> _syncResults;

        /// <summary>
        /// Constructeur du formulaire des résultats
        /// </summary>
        public SyncResultsForm(List<SyncItem> syncResults)
        {
            InitializeComponent();
            _syncResults = syncResults ?? new List<SyncItem>();
            LoadResults();
        }

        /// <summary>
        /// Charge les résultats dans l'interface
        /// </summary>
        private void LoadResults()
        {
            // Statistiques générales
            LoadStatistics();
            
            // Charger les données dans la grille
            LoadDataGrid();
            
            // Configurer les filtres
            SetupFilters();
        }

        /// <summary>
        /// Charge les statistiques de synchronisation
        /// </summary>
        private void LoadStatistics()
        {
            int totalItems = _syncResults.Count;
            int newItems = _syncResults.Count(x => x.Status == SyncStatus.New);
            int modifiedItems = _syncResults.Count(x => x.Status == SyncStatus.Modified);
            int synchronizedItems = _syncResults.Count(x => x.Status == SyncStatus.Synchronized);
            int conflictItems = _syncResults.Count(x => x.Status == SyncStatus.Conflict);
            int errorItems = _syncResults.Count(x => x.Status == SyncStatus.Error);

            lblTotalItems.Text = string.Format("Total: {0} articles", totalItems);
            lblNewItems.Text = string.Format("Nouveaux: {0}", newItems);
            lblModifiedItems.Text = string.Format("Modifiés: {0}", modifiedItems);
            lblSynchronizedItems.Text = string.Format("Synchronisés: {0}", synchronizedItems);
            lblConflictItems.Text = string.Format("Conflits: {0}", conflictItems);
            lblErrorItems.Text = string.Format("Erreurs: {0}", errorItems);

            // Colorer les labels selon les valeurs
            lblConflictItems.ForeColor = conflictItems > 0 ? Color.Red : Color.Green;
            lblErrorItems.ForeColor = errorItems > 0 ? Color.Red : Color.Green;
        }

        /// <summary>
        /// Charge les données dans la grille
        /// </summary>
        private void LoadDataGrid()
        {
            dgvResults.Rows.Clear();

            foreach (SyncItem item in _syncResults)
            {
                int rowIndex = dgvResults.Rows.Add();
                DataGridViewRow row = dgvResults.Rows[rowIndex];

                row.Cells["colBarcode"].Value = item.Barcode;
                row.Cells["colName"].Value = item.Name;
                row.Cells["colCategory"].Value = item.Category;
                row.Cells["colPrice"].Value = item.UnitPrice.ToString("0.000") + " DT";
                row.Cells["colStock"].Value = item.StockQuantity;
                row.Cells["colSource"].Value = item.Source;
                row.Cells["colStatus"].Value = GetStatusText(item.Status);
                row.Cells["colComments"].Value = item.SyncComments;

                // Colorer la ligne selon le statut
                Color rowColor = GetStatusColor(item.Status);
                row.DefaultCellStyle.BackColor = rowColor;
            }

            // Ajuster la largeur des colonnes
            dgvResults.AutoResizeColumns();
        }

        /// <summary>
        /// Configure les filtres de statut
        /// </summary>
        private void SetupFilters()
        {
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("Tous les statuts");
            cmbStatusFilter.Items.Add("Nouveaux");
            cmbStatusFilter.Items.Add("Modifiés");
            cmbStatusFilter.Items.Add("Synchronisés");
            cmbStatusFilter.Items.Add("Conflits");
            cmbStatusFilter.Items.Add("Erreurs");
            cmbStatusFilter.SelectedIndex = 0;
        }

        /// <summary>
        /// Obtient le texte d'affichage pour un statut
        /// </summary>
        private string GetStatusText(SyncStatus status)
        {
            switch (status)
            {
                case SyncStatus.New: return "Nouveau";
                case SyncStatus.Modified: return "Modifié";
                case SyncStatus.Synchronized: return "Synchronisé";
                case SyncStatus.Conflict: return "Conflit";
                case SyncStatus.Error: return "Erreur";
                case SyncStatus.Pending: return "En attente";
                case SyncStatus.Deleted: return "Supprimé";
                default: return status.ToString();
            }
        }

        /// <summary>
        /// Obtient la couleur d'affichage pour un statut
        /// </summary>
        private Color GetStatusColor(SyncStatus status)
        {
            switch (status)
            {
                case SyncStatus.New: return Color.LightBlue;
                case SyncStatus.Modified: return Color.LightYellow;
                case SyncStatus.Synchronized: return Color.LightGreen;
                case SyncStatus.Conflict: return Color.LightCoral;
                case SyncStatus.Error: return Color.Pink;
                case SyncStatus.Pending: return Color.LightGray;
                case SyncStatus.Deleted: return Color.LightSalmon;
                default: return Color.White;
            }
        }

        /// <summary>
        /// Filtre les résultats par statut
        /// </summary>
        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedFilter = cmbStatusFilter.SelectedItem.ToString();
            
            foreach (DataGridViewRow row in dgvResults.Rows)
            {
                if (selectedFilter == "Tous les statuts")
                {
                    row.Visible = true;
                }
                else
                {
                    string rowStatus = row.Cells["colStatus"].Value?.ToString() ?? "";
                    row.Visible = rowStatus.Contains(selectedFilter.Replace("s", ""));
                }
            }
        }

        /// <summary>
        /// Recherche dans les résultats
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            string searchText = txtSearch.Text.ToLower();
            
            foreach (DataGridViewRow row in dgvResults.Rows)
            {
                bool visible = false;
                
                if (string.IsNullOrEmpty(searchText))
                {
                    visible = true;
                }
                else
                {
                    // Rechercher dans toutes les colonnes
                    foreach (DataGridViewCell cell in row.Cells)
                    {
                        if (cell.Value?.ToString().ToLower().Contains(searchText) == true)
                        {
                            visible = true;
                            break;
                        }
                    }
                }
                
                row.Visible = visible;
            }
        }

        /// <summary>
        /// Exporter les résultats vers un fichier CSV
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = "Fichiers CSV (*.csv)|*.csv|Tous les fichiers (*.*)|*.*";
            saveDialog.FileName = "SyncResults_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".csv";
            
            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    ExportToCsv(saveDialog.FileName);
                    MessageBox.Show("Résultats exportés avec succès vers:\n" + saveDialog.FileName,
                        "Export réussi", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erreur lors de l'export:\n" + ex.Message,
                        "Erreur d'export", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// Exporte les données vers un fichier CSV
        /// </summary>
        private void ExportToCsv(string fileName)
        {
            using (System.IO.StreamWriter writer = new System.IO.StreamWriter(fileName, false, System.Text.Encoding.UTF8))
            {
                // En-têtes
                writer.WriteLine("Code-barres;Nom;Catégorie;Prix (DT);Stock;Source;Statut;Commentaires");
                
                // Données
                foreach (SyncItem item in _syncResults)
                {
                    string line = string.Format("{0};{1};{2};{3:0.000};{4};{5};{6};{7}",
                        item.Barcode,
                        item.Name?.Replace(";", ","),
                        item.Category?.Replace(";", ","),
                        item.UnitPrice,
                        item.StockQuantity,
                        item.Source,
                        GetStatusText(item.Status),
                        item.SyncComments?.Replace(";", ","));
                    
                    writer.WriteLine(line);
                }
            }
        }

        /// <summary>
        /// Afficher les détails d'un article sélectionné
        /// </summary>
        private void dgvResults_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.RowIndex < _syncResults.Count)
            {
                SyncItem selectedItem = _syncResults[e.RowIndex];
                ShowItemDetails(selectedItem);
            }
        }

        /// <summary>
        /// Affiche les détails d'un article
        /// </summary>
        private void ShowItemDetails(SyncItem item)
        {
            string details = string.Format(
                "DÉTAILS DE L'ARTICLE\n" +
                "====================\n\n" +
                "Code-barres: {0}\n" +
                "Nom: {1}\n" +
                "Description: {2}\n" +
                "Catégorie: {3}\n" +
                "Prix unitaire: {4:0.000} DT\n" +
                "Stock actuel: {5} {6}\n" +
                "Stock minimum: {7}\n" +
                "Emplacement: {8}\n" +
                "Source: {9}\n" +
                "Statut: {10}\n" +
                "Commentaires: {11}\n\n" +
                "DATES\n" +
                "=====\n" +
                "Créé le: {12:dd/MM/yyyy HH:mm}\n" +
                "Modifié le: {13:dd/MM/yyyy HH:mm}",
                item.Barcode,
                item.Name,
                item.Description,
                item.Category,
                item.UnitPrice,
                item.StockQuantity,
                item.Unit,
                item.MinimumStock,
                item.Location,
                item.Source,
                GetStatusText(item.Status),
                item.SyncComments,
                item.CreatedDate,
                item.LastModified
            );

            MessageBox.Show(details, "Détails de l'article", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Fermer le formulaire
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
