@echo off
REM Compilation manuelle - <PERSON><PERSON> de dernier recours

echo ========================================
echo  COMPILATION MANUELLE
echo ========================================
echo.

echo INSTRUCTIONS POUR COMPILATION MANUELLE:
echo.
echo 1. OUVRIR VISUAL STUDIO 2022
echo    - Menu Demarrer > Visual Studio 2022
echo.
echo 2. CREER UN NOUVEAU PROJET
echo    - File > New > Project
echo    - Choisir "Windows Forms App (.NET Framework)"
echo    - Nom: InventoryApp
echo    - Framework: .NET Framework 4.8
echo    - Cliquer Create
echo.
echo 3. COPIER LES FICHIERS
echo    - Supprimer Form1.cs et Program.cs generes
echo    - Copier TOUS les fichiers .cs de ce dossier dans le projet
echo    - Clic droit sur projet > Add > Existing Item
echo    - Selectionner tous les fichiers .cs
echo.
echo 4. AJOUTER LES REFERENCES
echo    - Clic droit sur References > Add Reference
echo    - Ajouter: Microsoft.VisualBasic
echo.
echo 5. DEFINIR LA CONSTANTE
echo    - Clic droit sur projet > Properties
echo    - Build > Conditional compilation symbols
echo    - Ajouter: USE_XML_DATABASE
echo.
echo 6. COMPILER
echo    - Build > Build Solution
echo    - Le fichier .exe sera dans bin\Release\
echo.
echo 7. TESTER
echo    - Lancer l'application depuis Visual Studio
echo    - Ou copier le .exe vers votre MC2100
echo.

echo ========================================
echo  ALTERNATIVE: DEVELOPER COMMAND PROMPT
echo ========================================
echo.
echo Si vous preferez la ligne de commande:
echo.
echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
echo 2. cd "C:\Users\<USER>\Desktop\Inventaire"
echo 3. csc /target:winexe /out:InventoryApp.exe /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /define:USE_XML_DATABASE *.cs Models\*.cs Data\*.cs Services\*.cs Forms\*.cs Utils\*.cs Properties\*.cs
echo.

echo ========================================
echo  FICHIERS A COPIER DANS VISUAL STUDIO
echo ========================================
echo.
dir /b *.cs
echo.
echo Dossiers:
dir /b /ad
echo.

echo Appuyez sur une touche pour continuer...
pause >nul
