@echo off
REM Script qui active automatiquement l'environnement Visual Studio

echo ========================================
echo  COMPILATION AVEC ENVIRONNEMENT VS 2022
echo ========================================
echo.

REM Rechercher et activer l'environnement Visual Studio
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    echo Activation de l'environnement Visual Studio 2022 Community...
    call "%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    echo Activation de l'environnement Visual Studio 2022 Professional...
    call "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" (
    echo Activation de l'environnement Visual Studio 2022 Enterprise...
    call "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat"
) else (
    echo ERREUR: Visual Studio 2022 non trouve
    echo.
    echo SOLUTION MANUELLE:
    echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
    echo 2. Dans cette invite: cd "C:\Users\<USER>\Desktop\Inventaire"
    echo 3. Puis: build-final-fixed.bat
    echo.
    pause
    exit /b 1
)

echo.
echo Test du compilateur...
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur toujours non disponible
    echo.
    echo SOLUTION:
    echo Utiliser manuellement "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

echo [SUCCES] Compilateur C# maintenant disponible !
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation de l'application finale...
echo.

REM Compiler l'application
csc /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  COMPILATION REUSSIE !
    echo ========================================
    echo.
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    if exist "bin\Release\InventoryApp.exe" (
        for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
        echo.
        echo APPLICATION COMPLETE AVEC DEVISE TUNISIENNE !
        echo.
        echo Voulez-vous tester l'application ? (O/N)
        set /p TEST_APP=
        if /i "%TEST_APP%"=="O" (
            start "Inventaire MC2100" "bin\Release\InventoryApp.exe"
        )
    )
) else (
    echo.
    echo ERREUR: Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
)

echo.
pause
