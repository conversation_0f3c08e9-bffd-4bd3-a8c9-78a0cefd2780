using System;
using System.Collections.Generic;
using InventoryApp.Data;
using InventoryApp.Models;

namespace InventoryApp.Services
{
    /// <summary>
    /// Service métier pour la gestion de l'inventaire avec SQLite
    /// </summary>
    public class InventoryServiceSQLite
    {
        /// <summary>
        /// Constructeur du service d'inventaire SQLite
        /// </summary>
        public InventoryServiceSQLite()
        {
        }

        #region Gestion des articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        /// <param name="article">Article à ajouter</param>
        /// <returns>ID de l'article créé</returns>
        public int CreateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (string.IsNullOrEmpty(article.Barcode))
                throw new ArgumentException("Le code-barres est obligatoire");

            if (string.IsNullOrEmpty(article.Name))
                throw new ArgumentException("Le nom de l'article est obligatoire");

            // Vérifier que le code-barres n'existe pas déjà
            if (GetArticleByBarcode(article.Barcode) != null)
                throw new InvalidOperationException("Un article avec ce code-barres existe déjà");

            return DatabaseHelperSQLite.AddArticle(article);
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        /// <param name="article">Article à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (article.Id <= 0)
                throw new ArgumentException("ID d'article invalide");

            return DatabaseHelperSQLite.UpdateArticle(article);
        }

        /// <summary>
        /// Supprime un article
        /// </summary>
        /// <param name="articleId">ID de l'article à supprimer</param>
        /// <returns>True si la suppression a réussi</returns>
        public bool DeleteArticle(int articleId)
        {
            if (articleId <= 0)
                throw new ArgumentException("ID d'article invalide");

            return DatabaseHelperSQLite.DeleteArticle(articleId);
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        /// <param name="barcode">Code-barres</param>
        /// <returns>Article ou null si non trouvé</returns>
        public Article GetArticleByBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return null;

            return DatabaseHelperSQLite.GetArticleByBarcode(barcode);
        }

        /// <summary>
        /// Recherche des articles
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des articles correspondants</returns>
        public List<Article> SearchArticles(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return GetAllArticles();
            else
                return DatabaseHelperSQLite.SearchArticles(searchTerm);
        }

        /// <summary>
        /// Récupère tous les articles
        /// </summary>
        /// <returns>Liste de tous les articles</returns>
        public List<Article> GetAllArticles()
        {
            return DatabaseHelperSQLite.GetAllArticles();
        }

        #endregion

        #region Gestion des stocks

        /// <summary>
        /// Met à jour le stock d'un article
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="newQuantity">Nouvelle quantité</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateStock(string barcode, int newQuantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            article.StockQuantity = newQuantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Ajoute une quantité au stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à ajouter</param>
        /// <returns>True si l'ajout a réussi</returns>
        public bool AddToStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            article.StockQuantity += quantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Retire une quantité du stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à retirer</param>
        /// <returns>True si le retrait a réussi</returns>
        public bool RemoveFromStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            if (article.StockQuantity < quantity)
                return false; // Stock insuffisant

            article.StockQuantity -= quantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        /// <returns>Liste des articles en rupture</returns>
        public List<Article> GetOutOfStockArticles()
        {
            return DatabaseHelperSQLite.GetOutOfStockArticles();
        }

        /// <summary>
        /// Récupère les articles en dessous du seuil minimum
        /// </summary>
        /// <returns>Liste des articles en dessous du seuil</returns>
        public List<Article> GetLowStockArticles()
        {
            return DatabaseHelperSQLite.GetLowStockArticles();
        }

        #endregion

        #region Statistiques

        /// <summary>
        /// Récupère le nombre total d'articles
        /// </summary>
        /// <returns>Nombre d'articles</returns>
        public int GetTotalArticlesCount()
        {
            return DatabaseHelperSQLite.GetTotalArticlesCount();
        }

        /// <summary>
        /// Récupère la valeur totale du stock
        /// </summary>
        /// <returns>Valeur totale</returns>
        public decimal GetTotalStockValue()
        {
            return DatabaseHelperSQLite.GetTotalStockValue();
        }

        #endregion

        #region Gestion de l'inventaire

        /// <summary>
        /// Démarre une nouvelle session d'inventaire
        /// </summary>
        /// <param name="sessionName">Nom de la session</param>
        /// <returns>ID de la session créée</returns>
        public string StartInventorySession(string sessionName = null)
        {
            if (string.IsNullOrEmpty(sessionName))
            {
                sessionName = "INV_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
            }

            return sessionName;
        }

        /// <summary>
        /// Ajoute un élément d'inventaire
        /// </summary>
        /// <param name="inventoryItem">Élément d'inventaire</param>
        /// <returns>True si l'ajout a réussi</returns>
        public bool AddInventoryItem(InventoryItem inventoryItem)
        {
            if (inventoryItem == null)
                throw new ArgumentNullException("inventoryItem");

            // Ici, on pourrait ajouter la logique pour sauvegarder en base
            // Pour l'instant, on retourne true
            return true;
        }

        /// <summary>
        /// Finalise une session d'inventaire
        /// </summary>
        /// <param name="sessionId">ID de la session</param>
        /// <param name="updateStock">Mettre à jour les stocks</param>
        /// <returns>True si la finalisation a réussi</returns>
        public bool FinalizeInventorySession(string sessionId, bool updateStock = false)
        {
            if (string.IsNullOrEmpty(sessionId))
                return false;

            // Ici, on pourrait ajouter la logique pour finaliser l'inventaire
            // et optionnellement mettre à jour les stocks
            return true;
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        /// <returns>True si la connexion fonctionne</returns>
        public bool TestDatabaseConnection()
        {
            return DatabaseHelperSQLite.TestConnection();
        }

        /// <summary>
        /// Obtient des informations sur la base de données
        /// </summary>
        /// <returns>Informations sur la base de données</returns>
        public string GetDatabaseInfo()
        {
            return DatabaseHelperSQLite.GetDatabaseInfo();
        }

        /// <summary>
        /// Initialise la base de données
        /// </summary>
        public void InitializeDatabase()
        {
            DatabaseHelperSQLite.InitializeDatabase();
        }

        #endregion
    }
}
