<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- Configuration pour l'application Inventaire Zebra MC2100 -->
  
  <appSettings>
    <!-- Configuration de la base de données -->
    <add key="DatabaseFileName" value="InventoryDB.sdf" />
    <add key="DatabaseTimeout" value="30" />
    <add key="AutoCreateDatabase" value="true" />
    
    <!-- Configuration du scanner -->
    <add key="ScannerEnabled" value="true" />
    <add key="ScannerTimeout" value="5000" />
    <add key="AutoProcessBarcode" value="true" />
    
    <!-- Configuration de l'interface -->
    <add key="FormMaximized" value="true" />
    <add key="ShowSplashScreen" value="false" />
    <add key="AutoRefreshInterval" value="300" />
    
    <!-- Configuration de l'inventaire -->
    <add key="DefaultInventorySession" value="SESSION_{0:yyyyMMdd_HHmmss}" />
    <add key="AutoValidateInventory" value="false" />
    <add key="InventoryTolerancePercent" value="5.0" />
    
    <!-- Configuration des rapports -->
    <add key="ReportDateFormat" value="dd/MM/yyyy HH:mm" />
    <add key="ReportCurrency" value="€" />
    <add key="MaxReportItems" value="1000" />
    
    <!-- Configuration de sauvegarde -->
    <add key="AutoBackup" value="true" />
    <add key="BackupInterval" value="24" />
    <add key="BackupRetentionDays" value="30" />
    <add key="BackupPath" value="\Storage Card\Backup\Inventory\" />
    
    <!-- Configuration de performance -->
    <add key="MaxArticlesInList" value="500" />
    <add key="SearchMinLength" value="2" />
    <add key="CacheEnabled" value="true" />
    
    <!-- Configuration utilisateur -->
    <add key="DefaultUser" value="Utilisateur" />
    <add key="RequireUserValidation" value="false" />
    <add key="SessionTimeout" value="480" />
    
    <!-- Configuration de débogage -->
    <add key="DebugMode" value="false" />
    <add key="LogErrors" value="true" />
    <add key="LogPath" value="\Program Files\InventoryApp\Logs\" />
  </appSettings>
  
  <connectionStrings>
    <!-- Chaîne de connexion par défaut (sera construite dynamiquement) -->
    <add name="DefaultConnection" 
         connectionString="Data Source={0};Persist Security Info=False;" 
         providerName="System.Data.SqlServerCe" />
  </connectionStrings>
  
  <system.data>
    <DbProviderFactories>
      <add name="Microsoft SQL Server Compact Data Provider" 
           invariant="System.Data.SqlServerCe" 
           description=".NET Framework Data Provider for Microsoft SQL Server Compact" 
           type="System.Data.SqlServerCe.SqlCeProviderFactory, System.Data.SqlServerCe" />
    </DbProviderFactories>
  </system.data>
  
  <runtime>
    <!-- Configuration du runtime .NET Compact Framework -->
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SqlServerCe" 
                          publicKeyToken="89845dcd8080cc91" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
</configuration>
