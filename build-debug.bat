@echo off
REM Script de compilation de debug - Version ultra-simple
REM Affiche toutes les etapes et erreurs

echo ========================================
echo  BUILD DEBUG - INVENTAIRE MC2100
echo ========================================
echo.

echo Etape 1: Verification du repertoire courant
echo Repertoire: %CD%
echo.

echo Etape 2: Liste des fichiers dans le repertoire
dir /b *.cs 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Aucun fichier .cs trouve dans le repertoire courant
    echo Verifiez que vous etes dans le bon dossier
    goto :error_exit
)
echo.

echo Etape 3: Recherche du compilateur C#
set CSC_PATH=""

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo Compilateur trouve: Framework64 v4.0
    goto :compiler_found
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo Compilateur trouve: Framework v4.0
    goto :compiler_found
)

if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe"
    echo Compilateur trouve: Framework v3.5
    goto :compiler_found
)

echo ERREUR: Aucun compilateur C# trouve
echo.
echo Chemins verifies:
echo - %WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe
echo - %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe
echo - %WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe
echo.
echo SOLUTION: Installer .NET Framework ou Visual Studio
goto :error_exit

:compiler_found
echo Chemin du compilateur: %CSC_PATH%
echo.

echo Etape 4: Verification des fichiers source essentiels
set MISSING_COUNT=0

if not exist "Program.cs" (
    echo MANQUANT: Program.cs
    set /a MISSING_COUNT+=1
)

if not exist "Properties\AssemblyInfo.cs" (
    echo MANQUANT: Properties\AssemblyInfo.cs
    set /a MISSING_COUNT+=1
)

if not exist "Models\Article.cs" (
    echo MANQUANT: Models\Article.cs
    set /a MISSING_COUNT+=1
)

if %MISSING_COUNT% gtr 0 (
    echo.
    echo ERREUR: %MISSING_COUNT% fichiers essentiels manquants
    goto :error_exit
)

echo Tous les fichiers essentiels sont presents
echo.

echo Etape 5: Creation des dossiers de sortie
if not exist "bin" (
    mkdir "bin"
    echo Dossier bin cree
)
if not exist "bin\Debug" (
    mkdir "bin\Debug"
    echo Dossier bin\Debug cree
)
echo.

echo Etape 6: Test du compilateur
echo Test simple du compilateur...
echo using System; class Test { static void Main() { Console.WriteLine("Test OK"); } } > test_simple.cs
%CSC_PATH% /target:exe /out:bin\Debug\test.exe test_simple.cs

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec du test du compilateur
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Le compilateur ne fonctionne pas correctement
    goto :error_exit
)

echo Test du compilateur reussi !
del test_simple.cs >nul 2>&1
del bin\Debug\test.exe >nul 2>&1
echo.

echo Etape 7: Compilation complete
echo Compilation de tous les fichiers...

%CSC_PATH% /target:winexe ^
    /out:bin\Debug\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /define:USE_XML_DATABASE ^
    Program.cs ^
    Properties\AssemblyInfo.cs ^
    Models\Article.cs ^
    Models\InventoryItem.cs ^
    Data\DatabaseHelper-Simple.cs ^
    Services\BarcodeService.cs ^
    Services\InventoryService-Simple.cs ^
    Forms\MainForm.cs ^
    Forms\MainForm.Designer.cs ^
    Forms\ArticleForm.cs ^
    Forms\ArticleForm.Designer.cs ^
    Forms\InventoryForm.cs ^
    Forms\InventoryForm.Designer.cs ^
    Forms\ReportsForm.cs ^
    Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERREUR: Echec de la compilation complete
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs de compilation sont affichees ci-dessus
    goto :error_exit
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.
echo Fichier genere: bin\Debug\InventoryApp.exe
echo Taille du fichier:
dir bin\Debug\InventoryApp.exe | find "InventoryApp.exe"
echo.

echo Test de lancement...
if exist "bin\Debug\InventoryApp.exe" (
    echo [OK] Fichier executable cree
    echo.
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Test Inventaire" "bin\Debug\InventoryApp.exe"
    )
) else (
    echo [ERREUR] Fichier executable non trouve
)

echo.
echo Compilation terminee avec succes !
goto :end

:error_exit
echo.
echo ========================================
echo  ECHEC DE LA COMPILATION
echo ========================================
echo.
echo Consultez les messages d'erreur ci-dessus
echo.
echo SOLUTIONS POSSIBLES:
echo 1. Installer Visual Studio Community (gratuit)
echo 2. Installer .NET Framework SDK
echo 3. Executer en tant qu'administrateur
echo 4. Verifier que tous les fichiers sont presents
echo.

:end
echo Appuyez sur une touche pour continuer...
pause >nul
