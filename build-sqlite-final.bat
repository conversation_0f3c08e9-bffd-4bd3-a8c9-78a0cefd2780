@echo off
REM Script de compilation SQLite final avec toutes les corrections

echo ========================================
echo  BUILD SQLITE FINAL - INVENTAIRE MC2100
echo  Version SQLite complete et corrigee
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo.
    echo TELECHARGEMENT REQUIS:
    echo 1. Aller sur: https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger: "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Extraire le ZIP
    echo 4. Copier System.Data.SQLite.dll dans le dossier lib\
    echo.
    pause
    exit /b 1
)

echo [OK] Compilateur et SQLite disponibles
for %%I in ("lib\System.Data.SQLite.dll") do echo SQLite DLL: %%~zI octets
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation SQLite finale en cours...
echo.

REM Compilation avec versions simplifiées et corrigées
csc /target:winexe /out:bin\Release\InventoryApp-SQLite.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite-Simple.cs Services\BarcodeService.cs Services\InventoryService-SQLite-Simple.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo.
    echo FICHIERS UTILISES (VERSIONS SIMPLIFIEES):
    echo • DatabaseHelper-SQLite-Simple.cs
    echo • InventoryService-SQLite-Simple.cs
    echo • Tous les formulaires corriges
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION SQLITE REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp-SQLite.exe" (
    echo Fichier genere: bin\Release\InventoryApp-SQLite.exe
    
    for %%I in ("bin\Release\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets
    
    REM Copier la DLL SQLite nécessaire
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie dans bin\Release\
    
    echo.
    echo ========================================
    echo  APPLICATION SQLITE COMPLETE !
    echo ========================================
    echo.
    echo FONCTIONNALITES IMPLEMENTEES:
    echo =============================
    echo • Base de donnees SQLite locale (InventoryDB.sqlite)
    echo • Compatible .NET Framework 4.0
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete (CRUD)
    echo • Recherche rapide par nom/code-barres
    echo • Rapports detailles (4 types)
    echo • Module inventaire simplifie
    echo • Scanner codes-barres integre
    echo • Statistiques en temps reel
    echo • Performance optimisee pour laptop
    echo.
    
    echo MODULES CORRIGES ET TESTES:
    echo ===========================
    echo • Program.cs: Initialisation SQLite conditionnelle
    echo • MainForm.cs: Utilise InventoryServiceSQLite
    echo • ArticleForm.cs: CRUD complet avec SQLite
    echo • ReportsForm.cs: 4 rapports avec donnees SQLite
    echo • InventoryForm-Simple.cs: Interface simplifiee
    echo • DatabaseHelper-SQLite-Simple.cs: Compatible .NET 4.0
    echo • InventoryService-SQLite-Simple.cs: Service complet
    echo • CurrencyHelper.cs: Support Dinar Tunisien
    echo.
    
    echo BASE DE DONNEES SQLITE:
    echo =======================
    echo • Fichier: InventoryDB.sqlite (cree automatiquement)
    echo • Emplacement: dossier de l'application
    echo • Structure: Table Articles avec tous les champs
    echo • Index: Optimise pour recherche rapide
    echo • Sauvegarde: Copier le fichier .sqlite
    echo • Outils: DB Browser for SQLite, SQLiteStudio
    echo.
    
    echo DEPLOIEMENT SUR LAPTOP:
    echo =======================
    echo 1. Copier bin\Release\InventoryApp-SQLite.exe
    echo 2. Copier bin\Release\System.Data.SQLite.dll
    echo 3. Lancer l'application
    echo 4. Le fichier InventoryDB.sqlite se cree automatiquement
    echo 5. Commencer a creer des articles
    echo.
    
    echo COMPATIBILITE MC2100:
    echo =====================
    echo • L'application peut aussi fonctionner sur MC2100
    echo • Verifier que SQLite est compatible Windows CE
    echo • Copier les memes fichiers
    echo • Tester le fonctionnement
    echo.
    
    echo Voulez-vous tester l'application SQLite maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo ========================================
        echo  LANCEMENT DE L'APPLICATION SQLITE
        echo ========================================
        echo.
        start "Inventaire SQLite - Version Complete" "bin\Release\InventoryApp-SQLite.exe"
        echo.
        echo APPLICATION LANCEE !
        echo.
        echo GUIDE DE TEST COMPLET:
        echo ======================
        echo.
        echo 1. MENU PRINCIPAL:
        echo    • Voir les statistiques (nombre articles, valeur stock)
        echo    • Tester le scanner de codes-barres
        echo    • Verifier les informations de base de donnees
        echo.
        echo 2. GESTION ARTICLES:
        echo    • Cliquer sur "Gestion Articles"
        echo    • Creer un nouvel article:
        echo      - Code-barres: 1234567890123
        echo      - Nom: Article Test
        echo      - Prix: 15.500 DT
        echo      - Stock: 10
        echo      - Stock minimum: 2
        echo    • Rechercher l'article cree
        echo    • Modifier l'article
        echo.
        echo 3. RAPPORTS:
        echo    • Cliquer sur "Rapports"
        echo    • Tester les 4 types de rapports:
        echo      - Rapport Stock Global
        echo      - Articles en Rupture
        echo      - Articles Stock Bas
        echo      - Rapport par Categorie
        echo.
        echo 4. INVENTAIRE:
        echo    • Cliquer sur "Inventaire"
        echo    • Demarrer une session d'inventaire
        echo    • Voir les instructions d'utilisation
        echo.
        echo 5. VERIFICATION BASE DE DONNEES:
        echo    • Le fichier InventoryDB.sqlite sera cree dans:
        echo      %CD%\bin\Release\InventoryDB.sqlite
        echo    • Telecharger DB Browser for SQLite (gratuit)
        echo    • Ouvrir le fichier .sqlite pour voir les donnees
        echo.
        echo 6. TESTS DE PERFORMANCE:
        echo    • Creer plusieurs articles
        echo    • Tester la recherche rapide
        echo    • Generer des rapports avec donnees
        echo    • Verifier la vitesse des operations
        echo.
        echo Si tout fonctionne correctement, votre application
        echo SQLite est prete pour le deploiement !
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  APPLICATION SQLITE PRETE !
echo ========================================
echo.
echo Votre application d'inventaire avec SQLite est maintenant
echo complete et prete pour votre laptop !
echo.
echo AVANTAGES DE CETTE VERSION:
echo ===========================
echo • Base de donnees professionnelle SQLite
echo • Performance locale ultra-rapide
echo • Sauvegarde simple (fichier .sqlite)
echo • Compatible .NET Framework 4.0
echo • Requetes SQL completes et optimisees
echo • Devise tunisienne parfaitement integree
echo • Aucun serveur requis
echo • Portable entre machines
echo • Interface optimisee pour ecran tactile
echo.
echo PROCHAINES ETAPES:
echo ==================
echo 1. Tester l'application sur votre laptop
echo 2. Creer des articles de test avec prix en DT
echo 3. Generer et consulter les rapports
echo 4. Sauvegarder regulierement le fichier .sqlite
echo 5. Deployer sur d'autres machines si besoin
echo 6. Considerer l'integration avec MC2100
echo.
echo Compilation terminee avec succes !
echo Votre application SQLite est operationnelle !
pause
