@echo off
REM Script de compilation pour l'application de synchronisation

echo ========================================
echo  BUILD APPLICATION DE SYNCHRONISATION
echo  Zebra MC2100 Terminal ↔ Laptop Desktop
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo.
    echo TELECHARGEMENT REQUIS:
    echo 1. Aller sur: https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger: "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Extraire le ZIP
    echo 4. Copier System.Data.SQLite.dll dans le dossier lib\
    echo.
    pause
    exit /b 1
)

echo [OK] Environnement pret pour compilation
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\SyncApp" mkdir "bin\SyncApp"

echo Creation du fichier de reponse pour l'application de synchronisation...

REM Créer un fichier de réponse pour la compilation
echo /target:winexe > sync_compile.rsp
echo /out:bin\SyncApp\InventorySyncApp.exe >> sync_compile.rsp
echo /reference:System.dll >> sync_compile.rsp
echo /reference:System.Data.dll >> sync_compile.rsp
echo /reference:System.Drawing.dll >> sync_compile.rsp
echo /reference:System.Windows.Forms.dll >> sync_compile.rsp
echo /reference:System.Xml.dll >> sync_compile.rsp
echo /reference:lib\System.Data.SQLite.dll >> sync_compile.rsp
echo /nowarn:0168,0219 >> sync_compile.rsp

REM Ajouter tous les fichiers de l'application de synchronisation
echo SyncApp\Program.cs >> sync_compile.rsp
echo SyncApp\Models\SyncItem.cs >> sync_compile.rsp
echo SyncApp\Services\SyncService.cs >> sync_compile.rsp
echo SyncApp\Forms\SyncMainForm.cs >> sync_compile.rsp
echo SyncApp\Forms\SyncMainForm.Designer.cs >> sync_compile.rsp
echo SyncApp\Forms\SyncResultsForm.cs >> sync_compile.rsp
echo SyncApp\Forms\SyncResultsForm.Designer.cs >> sync_compile.rsp

echo.
echo Compilation de l'application de synchronisation...
echo.

REM Compiler avec le fichier de réponse
csc @sync_compile.rsp

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  COMPILATION REUSSIE !
    echo ========================================
    echo.
    
    REM Copier SQLite DLL
    copy "lib\System.Data.SQLite.dll" "bin\SyncApp\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier genere: bin\SyncApp\InventorySyncApp.exe
    for %%I in ("bin\SyncApp\InventorySyncApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo ========================================
    echo  APPLICATION DE SYNCHRONISATION PRETE !
    echo ========================================
    echo.
    echo FONCTIONNALITES IMPLEMENTEES:
    echo =============================
    echo • Interface graphique moderne et intuitive
    echo • Detection automatique des fichiers de donnees
    echo • Synchronisation Terminal → Laptop (XML vers SQLite)
    echo • Synchronisation Laptop → Terminal (SQLite vers XML)
    echo • Synchronisation bidirectionnelle avec detection de conflits
    echo • Barre de progression en temps reel
    echo • Journal de synchronisation detaille
    echo • Affichage des resultats avec statistiques
    echo • Export des resultats vers CSV
    echo • Gestion des erreurs et recuperation
    echo.
    
    echo SOURCES DE DONNEES SUPPORTEES:
    echo ==============================
    echo • Terminal Zebra MC2100: Fichier XML (InventoryDB.xml)
    echo • Laptop Desktop: Base SQLite (InventoryDB.sqlite)
    echo • Format devise: Dinar Tunisien (DT) avec 3 decimales
    echo • Encodage: UTF-8 pour caracteres speciaux
    echo.
    
    echo TYPES DE SYNCHRONISATION:
    echo ========================
    echo 1. TERMINAL → LAPTOP:
    echo    • Lit les donnees XML du terminal Zebra
    echo    • Met a jour la base SQLite du laptop
    echo    • Ideal apres un inventaire sur le terrain
    echo.
    echo 2. LAPTOP → TERMINAL:
    echo    • Lit les donnees SQLite du laptop
    echo    • Met a jour le fichier XML du terminal
    echo    • Ideal pour deployer de nouveaux articles
    echo.
    echo 3. BIDIRECTIONNELLE:
    echo    • Analyse les deux sources
    echo    • Detecte les conflits automatiquement
    echo    • Propose une resolution manuelle
    echo    • Synchronise les elements sans conflit
    echo.
    
    echo UTILISATION:
    echo ============
    echo 1. Placer les fichiers de donnees dans le meme dossier:
    echo    • InventoryDB.xml (du terminal Zebra)
    echo    • InventoryDB.sqlite (du laptop)
    echo.
    echo 2. Lancer InventorySyncApp.exe
    echo.
    echo 3. Verifier le statut des connexions
    echo.
    echo 4. Choisir le type de synchronisation
    echo.
    echo 5. Suivre la progression dans le journal
    echo.
    echo 6. Consulter les resultats et statistiques
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo ========================================
        echo  LANCEMENT DE L'APPLICATION
        echo ========================================
        echo.
        start "Application de Synchronisation Zebra" "bin\SyncApp\InventorySyncApp.exe"
        echo.
        echo APPLICATION LANCEE !
        echo.
        echo GUIDE DE TEST:
        echo ==============
        echo.
        echo 1. VERIFICATION DES CONNEXIONS:
        echo    • L'application detecte automatiquement les fichiers
        echo    • Statut vert = fichier trouve et lisible
        echo    • Statut rouge = fichier manquant ou erreur
        echo.
        echo 2. TEST DE SYNCHRONISATION:
        echo    • Commencer par "Actualiser" pour verifier les donnees
        echo    • Tester une synchronisation selon vos besoins
        echo    • Observer la barre de progression
        echo    • Lire les messages dans le journal
        echo.
        echo 3. CONSULTATION DES RESULTATS:
        echo    • Cliquer sur "Voir Resultats" apres synchronisation
        echo    • Examiner les statistiques
        echo    • Filtrer par statut si necessaire
        echo    • Exporter vers CSV pour analyse
        echo.
        echo 4. GESTION DES CONFLITS:
        echo    • Utiliser la synchronisation bidirectionnelle
        echo    • Les conflits sont signales en rouge
        echo    • Resolution manuelle requise
        echo.
        echo 5. JOURNAL ET LOGS:
        echo    • Tous les evenements sont enregistres
        echo    • Fichier SyncLog.txt cree automatiquement
        echo    • Historique des synchronisations conserve
        echo.
        echo Si tout fonctionne correctement, votre application
        echo de synchronisation est prete pour la production !
    )
    
) else (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
    echo.
    echo FICHIERS DE L'APPLICATION:
    echo ==========================
    echo • SyncApp\Program.cs - Point d'entree
    echo • SyncApp\Models\SyncItem.cs - Modele de donnees
    echo • SyncApp\Services\SyncService.cs - Logique de synchronisation
    echo • SyncApp\Forms\SyncMainForm.cs - Interface principale
    echo • SyncApp\Forms\SyncMainForm.Designer.cs - Design interface
    echo • SyncApp\Forms\SyncResultsForm.cs - Affichage resultats
    echo.
)

REM Nettoyer
del sync_compile.rsp >nul 2>&1

echo.
echo ========================================
echo  COMPILATION TERMINEE
echo ========================================
echo.
echo Votre application de synchronisation d'inventaires
echo Zebra MC2100 est maintenant prete !
echo.
echo DEPLOIEMENT:
echo ============
echo Copier ces fichiers ensemble:
echo • bin\SyncApp\InventorySyncApp.exe
echo • bin\SyncApp\System.Data.SQLite.dll
echo • Les fichiers de donnees (XML et SQLite)
echo.
echo L'application fonctionne de maniere autonome
echo et ne necessite aucune installation !
echo.
pause
