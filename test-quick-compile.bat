@echo off
REM Test rapide de compilation

echo Test compilation rapide...

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    pause
    exit /b 1
)

if not exist "bin" mkdir "bin"

echo Compilation test...

csc /target:winexe /out:bin\TestApp.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Utils\CurrencyHelper.cs Services\BarcodeService.cs Data\DatabaseHelper-SQLite-Simple.cs Services\InventoryService-SQLite-Simple.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo SUCCES !
    copy lib\System.Data.SQLite.dll bin\ >nul
    echo Fichier: bin\TestApp.exe
    echo.
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" (
        start "Test SQLite" bin\TestApp.exe
    )
) else (
    echo ERREUR: %ERRORLEVEL%
    echo.
    echo Utilisez build-fixed-final.bat pour plus de details
)

pause
