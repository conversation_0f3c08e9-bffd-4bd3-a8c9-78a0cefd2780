-- Données de test pour SQLite avec prix en Dinar Tunisien
-- Exécuter ces requêtes dans votre application ou un outil SQLite

-- Insertion d'articles de test avec prix tunisiens
INSERT INTO Articles (Barcode, Name, Description, Category, UnitPrice, StockQuantity, MinimumStock, Unit, Location, CreatedDate, LastModified, IsActive)
VALUES 
-- Articles de bureau tunisiens
('3760123456789', 'Stylo Bic Bleu', 'Stylo à bille bleu standard', 'Bureau', 0.800, 150, 20, 'pièce', 'A1-01', datetime('now'), datetime('now'), 1),
('3760123456790', 'Cahier A4 200p', 'Cahier spirale A4 200 pages', 'Bureau', 4.500, 45, 10, 'pièce', 'A1-02', datetime('now'), datetime('now'), 1),
('3760123456791', 'Agrafeuse Standard', 'Agrafeuse de bureau standard', 'Bureau', 18.750, 8, 3, 'pièce', 'A1-03', datetime('now'), datetime('now'), 1),
('3760123456792', 'Agrafes 26/6', 'Boîte de 1000 agrafes 26/6', 'Bureau', 3.200, 25, 5, 'boîte', 'A1-04', datetime('now'), datetime('now'), 1),
('3760123456793', 'Classeur A4', 'Classeur à levier A4 dos 80mm', 'Bureau', 6.900, 30, 8, 'pièce', 'A1-05', datetime('now'), datetime('now'), 1),

-- Articles informatiques
('3760123456794', 'Souris USB', 'Souris optique USB filaire', 'Informatique', 22.500, 12, 3, 'pièce', 'B2-01', datetime('now'), datetime('now'), 1),
('3760123456795', 'Clavier USB', 'Clavier AZERTY USB filaire', 'Informatique', 35.800, 8, 2, 'pièce', 'B2-02', datetime('now'), datetime('now'), 1),
('3760123456796', 'Câble USB-A/B', 'Câble USB A vers B 2 mètres', 'Informatique', 12.600, 20, 5, 'pièce', 'B2-03', datetime('now'), datetime('now'), 1),
('3760123456797', 'Hub USB 4 ports', 'Hub USB 2.0 4 ports', 'Informatique', 26.200, 6, 2, 'pièce', 'B2-04', datetime('now'), datetime('now'), 1),
('3760123456798', 'Adaptateur HDMI', 'Adaptateur HDMI vers VGA', 'Informatique', 18.300, 10, 3, 'pièce', 'B2-05', datetime('now'), datetime('now'), 1),

-- Articles de nettoyage
('3760123456799', 'Produit Vitres 500ml', 'Nettoyant vitres spray 500ml', 'Nettoyage', 4.850, 24, 6, 'flacon', 'C3-01', datetime('now'), datetime('now'), 1),
('3760123456800', 'Éponges x5', 'Lot de 5 éponges grattantes', 'Nettoyage', 4.100, 18, 4, 'lot', 'C3-02', datetime('now'), datetime('now'), 1),
('3760123456801', 'Sacs Poubelle 50L', 'Rouleau 20 sacs poubelle 50L', 'Nettoyage', 5.900, 15, 3, 'rouleau', 'C3-03', datetime('now'), datetime('now'), 1),
('3760123456802', 'Désinfectant 1L', 'Désinfectant multi-surfaces 1L', 'Nettoyage', 9.600, 12, 3, 'flacon', 'C3-04', datetime('now'), datetime('now'), 1),
('3760123456803', 'Papier Toilette x12', 'Pack 12 rouleaux papier toilette', 'Nettoyage', 12.000, 20, 5, 'pack', 'C3-05', datetime('now'), datetime('now'), 1),

-- Articles alimentaires tunisiens
('3760123456810', 'Café Soluble 200g', 'Café soluble tunisien 200g', 'Alimentaire', 8.500, 8, 2, 'pot', 'D4-01', datetime('now'), datetime('now'), 1),
('3760123456811', 'Sucre Morceaux 1kg', 'Sucre en morceaux 1kg', 'Alimentaire', 3.200, 12, 3, 'paquet', 'D4-02', datetime('now'), datetime('now'), 1),
('3760123456812', 'Thé Vert 100g', 'Thé vert tunisien 100g', 'Alimentaire', 6.750, 15, 4, 'paquet', 'D4-03', datetime('now'), datetime('now'), 1),
('3760123456813', 'Huile Olive 500ml', 'Huile d\'olive extra vierge 500ml', 'Alimentaire', 15.500, 10, 2, 'bouteille', 'D4-04', datetime('now'), datetime('now'), 1),
('3760123456814', 'Harissa 200g', 'Harissa traditionnelle 200g', 'Alimentaire', 4.200, 20, 5, 'tube', 'D4-05', datetime('now'), datetime('now'), 1),

-- Articles spécifiques tunisiens
('3760123456815', 'Couscous 1kg', 'Couscous grain moyen 1kg', 'Alimentaire', 2.850, 25, 5, 'paquet', 'D4-06', datetime('now'), datetime('now'), 1),
('3760123456816', 'Dates Deglet Nour 500g', 'Dattes Deglet Nour de qualité 500g', 'Alimentaire', 12.800, 8, 2, 'barquette', 'D4-07', datetime('now'), datetime('now'), 1),
('3760123456817', 'Eau Minérale 1.5L', 'Eau minérale naturelle 1.5L', 'Alimentaire', 0.650, 50, 10, 'bouteille', 'D4-08', datetime('now'), datetime('now'), 1),

-- Articles en rupture de stock (pour tester les alertes)
('3760123456804', 'Toner Imprimante', 'Cartouche toner noir HP', 'Informatique', 127.500, 0, 2, 'pièce', 'B2-06', datetime('now'), datetime('now'), 1),
('3760123456805', 'Papier A4 Ramette', 'Ramette 500 feuilles A4 80g', 'Bureau', 6.350, 0, 5, 'ramette', 'A1-06', datetime('now'), datetime('now'), 1),

-- Articles avec stock bas (pour tester les alertes)
('3760123456806', 'Marqueurs Couleur x4', 'Set 4 marqueurs couleurs', 'Bureau', 9.750, 2, 5, 'set', 'A1-07', datetime('now'), datetime('now'), 1),
('3760123456807', 'Pile AA x4', 'Pack 4 piles alcalines AA', 'Informatique', 7.350, 1, 3, 'pack', 'B2-07', datetime('now'), datetime('now'), 1),

-- Articles avec prix élevé (pour tester la valeur du stock)
('3760123456808', 'Écran LCD 24"', 'Moniteur LCD 24 pouces Full HD', 'Informatique', 268.500, 3, 1, 'pièce', 'B2-08', datetime('now'), datetime('now'), 1),
('3760123456809', 'Imprimante Laser', 'Imprimante laser monochrome', 'Informatique', 226.200, 2, 1, 'pièce', 'B2-09', datetime('now'), datetime('now'), 1);

-- Insertion d'éléments d'inventaire de test
INSERT INTO InventoryItems (ArticleId, Barcode, ArticleName, CountedQuantity, ExpectedQuantity, Location, CountDate, CountedBy, Comments, IsValidated, InventorySession)
VALUES 
-- Session d'inventaire SQLite du 29/07/2024
(1, '3760123456789', 'Stylo Bic Bleu', 148, 150, 'A1-01', datetime('now'), 'Ahmed Ben Ali', 'Comptage OK', 1, 'SESSION_SQLITE_20240729_160000'),
(2, '3760123456790', 'Cahier A4 200p', 45, 45, 'A1-02', datetime('now'), 'Ahmed Ben Ali', 'Comptage OK', 1, 'SESSION_SQLITE_20240729_160000'),
(3, '3760123456791', 'Agrafeuse Standard', 7, 8, 'A1-03', datetime('now'), 'Ahmed Ben Ali', 'Manque 1 unité', 0, 'SESSION_SQLITE_20240729_160000'),
(4, '3760123456792', 'Agrafes 26/6', 25, 25, 'A1-04', datetime('now'), 'Ahmed Ben Ali', 'Comptage OK', 1, 'SESSION_SQLITE_20240729_160000'),
(5, '3760123456793', 'Classeur A4', 32, 30, 'A1-05', datetime('now'), 'Ahmed Ben Ali', 'Surplus de 2 unités', 0, 'SESSION_SQLITE_20240729_160000');

-- Requêtes de test pour vérifier les données
/*
-- Vérifier les articles
SELECT COUNT(*) as 'Nombre d\'articles' FROM Articles WHERE IsActive = 1;

-- Vérifier la valeur totale du stock
SELECT ROUND(SUM(StockQuantity * UnitPrice), 3) as 'Valeur totale (DT)' FROM Articles WHERE IsActive = 1;

-- Articles en rupture
SELECT Name, Barcode, StockQuantity FROM Articles WHERE IsActive = 1 AND StockQuantity <= 0;

-- Articles en stock bas
SELECT Name, Barcode, StockQuantity, MinimumStock FROM Articles WHERE IsActive = 1 AND StockQuantity <= MinimumStock;

-- Statistiques par catégorie
SELECT 
    Category as 'Catégorie',
    COUNT(*) as 'Nb Articles',
    SUM(StockQuantity) as 'Quantité totale',
    ROUND(SUM(StockQuantity * UnitPrice), 3) as 'Valeur (DT)'
FROM Articles 
WHERE IsActive = 1 
GROUP BY Category 
ORDER BY Category;

-- Sessions d'inventaire
SELECT 
    InventorySession as 'Session',
    COUNT(*) as 'Nb éléments',
    SUM(CASE WHEN IsValidated = 1 THEN 1 ELSE 0 END) as 'Validés'
FROM InventoryItems 
GROUP BY InventorySession;
*/

-- Informations sur la base SQLite
/*
AVANTAGES DE SQLITE POUR VOTRE APPLICATION:
==========================================

1. PERFORMANCE:
   • Base de données locale ultra-rapide
   • Pas de latence réseau
   • Optimisée pour les applications desktop

2. SIMPLICITÉ:
   • Un seul fichier: InventoryDB.sqlite
   • Pas de serveur à installer
   • Sauvegarde = copie du fichier

3. FIABILITÉ:
   • Transactions ACID
   • Résistant aux pannes
   • Intégrité des données garantie

4. COMPATIBILITÉ:
   • Fonctionne sur laptop et MC2100
   • Compatible .NET Framework
   • Portable entre systèmes

5. FONCTIONNALITÉS:
   • Requêtes SQL complètes
   • Index pour les performances
   • Contraintes d'intégrité
   • Types de données flexibles

UTILISATION:
============
• Le fichier InventoryDB.sqlite sera créé automatiquement
• Emplacement: dossier de l'application
• Taille typique: quelques MB pour milliers d'articles
• Sauvegarde: copier le fichier .sqlite

DÉPLOIEMENT:
============
• Copier InventoryApp.exe
• Copier System.Data.SQLite.dll
• Le fichier .sqlite se crée automatiquement
• Aucune configuration requise
*/
