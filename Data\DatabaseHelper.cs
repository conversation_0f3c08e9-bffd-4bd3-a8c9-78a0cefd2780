using System;
using System.Data;
using System.Data.SqlServerCe;
using System.IO;
using System.Windows.Forms;

namespace InventoryApp.Data
{
    /// <summary>
    /// Helper pour la gestion de la base de données SQL Server Compact Edition
    /// </summary>
    public static class DatabaseHelper
    {
        private static string _connectionString;
        private static readonly string DatabaseFileName = "InventoryDB.sdf";
        
        /// <summary>
        /// Chaîne de connexion à la base de données
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                    _connectionString = string.Format("Data Source={0};Persist Security Info=False;", dbPath);
                }
                return _connectionString;
            }
        }

        /// <summary>
        /// Obtient le chemin de l'application
        /// </summary>
        /// <returns>Chemin de l'application</returns>
        private static string GetApplicationPath()
        {
            return Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().GetName().CodeBase);
        }

        /// <summary>
        /// Initialise la base de données
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                
                // Créer la base de données si elle n'existe pas
                if (!File.Exists(dbPath))
                {
                    CreateDatabase();
                }
                
                // Vérifier et créer les tables si nécessaire
                EnsureTablesExist();
            }
            catch (Exception ex)
            {
                throw new Exception("Erreur lors de l'initialisation de la base de données: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// Crée la base de données
        /// </summary>
        private static void CreateDatabase()
        {
            using (SqlCeEngine engine = new SqlCeEngine(ConnectionString))
            {
                engine.CreateDatabase();
            }
        }

        /// <summary>
        /// S'assure que toutes les tables existent
        /// </summary>
        private static void EnsureTablesExist()
        {
            using (SqlCeConnection connection = new SqlCeConnection(ConnectionString))
            {
                connection.Open();
                
                // Créer la table Articles
                CreateArticlesTable(connection);
                
                // Créer la table InventoryItems
                CreateInventoryItemsTable(connection);
            }
        }

        /// <summary>
        /// Crée la table Articles
        /// </summary>
        /// <param name="connection">Connexion à la base</param>
        private static void CreateArticlesTable(SqlCeConnection connection)
        {
            string createTableSql = @"
                CREATE TABLE Articles (
                    Id int IDENTITY(1,1) PRIMARY KEY,
                    Barcode nvarchar(50) NOT NULL UNIQUE,
                    Name nvarchar(100) NOT NULL,
                    Description nvarchar(255),
                    Category nvarchar(50),
                    UnitPrice decimal(10,2) DEFAULT 0,
                    StockQuantity int DEFAULT 0,
                    MinimumStock int DEFAULT 0,
                    Unit nvarchar(20) DEFAULT 'pièce',
                    Location nvarchar(50),
                    CreatedDate datetime NOT NULL,
                    LastModified datetime NOT NULL,
                    IsActive bit DEFAULT 1
                )";

            ExecuteNonQueryIfTableNotExists(connection, "Articles", createTableSql);
        }

        /// <summary>
        /// Crée la table InventoryItems
        /// </summary>
        /// <param name="connection">Connexion à la base</param>
        private static void CreateInventoryItemsTable(SqlCeConnection connection)
        {
            string createTableSql = @"
                CREATE TABLE InventoryItems (
                    Id int IDENTITY(1,1) PRIMARY KEY,
                    ArticleId int NOT NULL,
                    Barcode nvarchar(50) NOT NULL,
                    ArticleName nvarchar(100) NOT NULL,
                    CountedQuantity int DEFAULT 0,
                    ExpectedQuantity int DEFAULT 0,
                    Location nvarchar(50),
                    CountDate datetime NOT NULL,
                    CountedBy nvarchar(50),
                    Comments nvarchar(255),
                    IsValidated bit DEFAULT 0,
                    ValidatedDate datetime,
                    ValidatedBy nvarchar(50),
                    InventorySession nvarchar(50)
                )";

            ExecuteNonQueryIfTableNotExists(connection, "InventoryItems", createTableSql);
        }

        /// <summary>
        /// Exécute une commande SQL si la table n'existe pas
        /// </summary>
        /// <param name="connection">Connexion</param>
        /// <param name="tableName">Nom de la table</param>
        /// <param name="createSql">SQL de création</param>
        private static void ExecuteNonQueryIfTableNotExists(SqlCeConnection connection, string tableName, string createSql)
        {
            if (!TableExists(connection, tableName))
            {
                using (SqlCeCommand command = new SqlCeCommand(createSql, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Vérifie si une table existe
        /// </summary>
        /// <param name="connection">Connexion</param>
        /// <param name="tableName">Nom de la table</param>
        /// <returns>True si la table existe</returns>
        private static bool TableExists(SqlCeConnection connection, string tableName)
        {
            string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName";
            using (SqlCeCommand command = new SqlCeCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);
                int count = (int)command.ExecuteScalar();
                return count > 0;
            }
        }

        /// <summary>
        /// Obtient une nouvelle connexion à la base de données
        /// </summary>
        /// <returns>Connexion SQL CE</returns>
        public static SqlCeConnection GetConnection()
        {
            return new SqlCeConnection(ConnectionString);
        }

        /// <summary>
        /// Exécute une commande SQL et retourne le nombre de lignes affectées
        /// </summary>
        /// <param name="sql">Commande SQL</param>
        /// <param name="parameters">Paramètres</param>
        /// <returns>Nombre de lignes affectées</returns>
        public static int ExecuteNonQuery(string sql, params SqlCeParameter[] parameters)
        {
            using (SqlCeConnection connection = GetConnection())
            {
                connection.Open();
                using (SqlCeCommand command = new SqlCeCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Exécute une commande SQL et retourne une valeur scalaire
        /// </summary>
        /// <param name="sql">Commande SQL</param>
        /// <param name="parameters">Paramètres</param>
        /// <returns>Valeur scalaire</returns>
        public static object ExecuteScalar(string sql, params SqlCeParameter[] parameters)
        {
            using (SqlCeConnection connection = GetConnection())
            {
                connection.Open();
                using (SqlCeCommand command = new SqlCeCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    return command.ExecuteScalar();
                }
            }
        }

        /// <summary>
        /// Exécute une commande SQL et retourne un DataTable
        /// </summary>
        /// <param name="sql">Commande SQL</param>
        /// <param name="parameters">Paramètres</param>
        /// <returns>DataTable avec les résultats</returns>
        public static DataTable ExecuteQuery(string sql, params SqlCeParameter[] parameters)
        {
            using (SqlCeConnection connection = GetConnection())
            {
                connection.Open();
                using (SqlCeCommand command = new SqlCeCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    using (SqlCeDataAdapter adapter = new SqlCeDataAdapter(command))
                    {
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        /// <summary>
        /// Sauvegarde la base de données
        /// </summary>
        /// <param name="backupPath">Chemin de sauvegarde</param>
        public static void BackupDatabase(string backupPath)
        {
            string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
            File.Copy(dbPath, backupPath, true);
        }

        /// <summary>
        /// Restaure la base de données
        /// </summary>
        /// <param name="backupPath">Chemin de la sauvegarde</param>
        public static void RestoreDatabase(string backupPath)
        {
            string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
            File.Copy(backupPath, dbPath, true);
        }
    }
}
