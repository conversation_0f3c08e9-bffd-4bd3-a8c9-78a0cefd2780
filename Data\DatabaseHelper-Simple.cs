using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace InventoryApp.Data
{
    /// <summary>
    /// Version simplifiée du DatabaseHelper utilisant des fichiers XML
    /// Compatible avec tous les environnements .NET sans dépendances externes
    /// </summary>
    public static class DatabaseHelperSimple
    {
        private static readonly string DatabaseFileName = "InventoryDB.xml";
        private static string _databasePath;
        
        /// <summary>
        /// Chemin complet vers le fichier de base de données
        /// </summary>
        public static string DatabasePath
        {
            get
            {
                if (string.IsNullOrEmpty(_databasePath))
                {
                    _databasePath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                }
                return _databasePath;
            }
        }

        /// <summary>
        /// Obtient le chemin de l'application
        /// </summary>
        private static string GetApplicationPath()
        {
            return Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
        }

        /// <summary>
        /// Initialise la base de données XML
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                if (!File.Exists(DatabasePath))
                {
                    CreateXmlDatabase();
                }
                
                // Vérifier la structure XML
                ValidateXmlStructure();
            }
            catch (Exception ex)
            {
                throw new Exception("Erreur lors de l'initialisation de la base de données: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// Crée le fichier XML de base de données
        /// </summary>
        private static void CreateXmlDatabase()
        {
            XmlDocument doc = new XmlDocument();
            
            // Créer la structure XML
            XmlDeclaration declaration = doc.CreateXmlDeclaration("1.0", "UTF-8", null);
            doc.AppendChild(declaration);
            
            XmlElement root = doc.CreateElement("InventoryDatabase");
            doc.AppendChild(root);
            
            // Créer les tables
            XmlElement articlesTable = doc.CreateElement("Articles");
            root.AppendChild(articlesTable);
            
            XmlElement inventoryTable = doc.CreateElement("InventoryItems");
            root.AppendChild(inventoryTable);
            
            // Sauvegarder
            doc.Save(DatabasePath);
        }

        /// <summary>
        /// Valide la structure du fichier XML
        /// </summary>
        private static void ValidateXmlStructure()
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            // Vérifier que les tables existent
            if (doc.SelectSingleNode("//Articles") == null)
            {
                XmlElement articlesTable = doc.CreateElement("Articles");
                doc.DocumentElement.AppendChild(articlesTable);
            }
            
            if (doc.SelectSingleNode("//InventoryItems") == null)
            {
                XmlElement inventoryTable = doc.CreateElement("InventoryItems");
                doc.DocumentElement.AppendChild(inventoryTable);
            }
            
            doc.Save(DatabasePath);
        }

        /// <summary>
        /// Ajoute un article à la base de données XML
        /// </summary>
        public static int AddArticle(Dictionary<string, object> articleData)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlElement articlesTable = doc.SelectSingleNode("//Articles") as XmlElement;
            
            // Générer un nouvel ID
            int newId = GetNextArticleId(doc);
            
            // Créer l'élément article
            XmlElement article = doc.CreateElement("Article");
            article.SetAttribute("Id", newId.ToString());
            
            // Ajouter les propriétés
            foreach (var kvp in articleData)
            {
                XmlElement property = doc.CreateElement(kvp.Key);
                property.InnerText = kvp.Value?.ToString() ?? "";
                article.AppendChild(property);
            }
            
            // Ajouter les dates
            XmlElement createdDate = doc.CreateElement("CreatedDate");
            createdDate.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            article.AppendChild(createdDate);
            
            XmlElement lastModified = doc.CreateElement("LastModified");
            lastModified.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            article.AppendChild(lastModified);
            
            XmlElement isActive = doc.CreateElement("IsActive");
            isActive.InnerText = "true";
            article.AppendChild(isActive);
            
            articlesTable.AppendChild(article);
            doc.Save(DatabasePath);
            
            return newId;
        }

        /// <summary>
        /// Met à jour un article dans la base de données XML
        /// </summary>
        public static bool UpdateArticle(int articleId, Dictionary<string, object> articleData)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlElement article = doc.SelectSingleNode($"//Article[@Id='{articleId}']") as XmlElement;
            if (article == null)
                return false;
            
            // Mettre à jour les propriétés
            foreach (var kvp in articleData)
            {
                XmlElement property = article.SelectSingleNode(kvp.Key) as XmlElement;
                if (property == null)
                {
                    property = doc.CreateElement(kvp.Key);
                    article.AppendChild(property);
                }
                property.InnerText = kvp.Value?.ToString() ?? "";
            }
            
            // Mettre à jour la date de modification
            XmlElement lastModified = article.SelectSingleNode("LastModified") as XmlElement;
            if (lastModified == null)
            {
                lastModified = doc.CreateElement("LastModified");
                article.AppendChild(lastModified);
            }
            lastModified.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            doc.Save(DatabasePath);
            return true;
        }

        /// <summary>
        /// Supprime un article (suppression logique)
        /// </summary>
        public static bool DeleteArticle(int articleId)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlElement article = doc.SelectSingleNode($"//Article[@Id='{articleId}']") as XmlElement;
            if (article == null)
                return false;
            
            XmlElement isActive = article.SelectSingleNode("IsActive") as XmlElement;
            if (isActive == null)
            {
                isActive = doc.CreateElement("IsActive");
                article.AppendChild(isActive);
            }
            isActive.InnerText = "false";
            
            // Mettre à jour la date de modification
            XmlElement lastModified = article.SelectSingleNode("LastModified") as XmlElement;
            if (lastModified == null)
            {
                lastModified = doc.CreateElement("LastModified");
                article.AppendChild(lastModified);
            }
            lastModified.InnerText = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            doc.Save(DatabasePath);
            return true;
        }

        /// <summary>
        /// Récupère un article par son ID
        /// </summary>
        public static Dictionary<string, object> GetArticleById(int articleId)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlElement article = doc.SelectSingleNode($"//Article[@Id='{articleId}' and IsActive='true']") as XmlElement;
            if (article == null)
                return null;
            
            return XmlElementToDictionary(article);
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        public static Dictionary<string, object> GetArticleByBarcode(string barcode)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlElement article = doc.SelectSingleNode($"//Article[Barcode='{barcode}' and IsActive='true']") as XmlElement;
            if (article == null)
                return null;
            
            return XmlElementToDictionary(article);
        }

        /// <summary>
        /// Récupère tous les articles actifs
        /// </summary>
        public static List<Dictionary<string, object>> GetAllArticles()
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlNodeList articles = doc.SelectNodes("//Article[IsActive='true']");
            List<Dictionary<string, object>> result = new List<Dictionary<string, object>>();
            
            foreach (XmlElement article in articles)
            {
                result.Add(XmlElementToDictionary(article));
            }
            
            return result;
        }

        /// <summary>
        /// Recherche des articles par terme
        /// </summary>
        public static List<Dictionary<string, object>> SearchArticles(string searchTerm)
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            string xpath = $"//Article[IsActive='true' and (contains(translate(Name, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{searchTerm.ToLower()}') or contains(translate(Barcode, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{searchTerm.ToLower()}') or contains(translate(Description, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{searchTerm.ToLower()}'))]";
            
            XmlNodeList articles = doc.SelectNodes(xpath);
            List<Dictionary<string, object>> result = new List<Dictionary<string, object>>();
            
            foreach (XmlElement article in articles)
            {
                result.Add(XmlElementToDictionary(article));
            }
            
            return result;
        }

        /// <summary>
        /// Obtient le prochain ID d'article
        /// </summary>
        private static int GetNextArticleId(XmlDocument doc)
        {
            XmlNodeList articles = doc.SelectNodes("//Article");
            int maxId = 0;
            
            foreach (XmlElement article in articles)
            {
                int id;
                if (int.TryParse(article.GetAttribute("Id"), out id))
                {
                    if (id > maxId)
                        maxId = id;
                }
            }
            
            return maxId + 1;
        }

        /// <summary>
        /// Convertit un élément XML en dictionnaire
        /// </summary>
        private static Dictionary<string, object> XmlElementToDictionary(XmlElement element)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            
            // Ajouter l'ID
            result["Id"] = int.Parse(element.GetAttribute("Id"));
            
            // Ajouter toutes les propriétés
            foreach (XmlElement child in element.ChildNodes)
            {
                result[child.Name] = child.InnerText;
            }
            
            return result;
        }

        /// <summary>
        /// Compte le nombre total d'articles
        /// </summary>
        public static int GetTotalArticlesCount()
        {
            XmlDocument doc = new XmlDocument();
            doc.Load(DatabasePath);
            
            XmlNodeList articles = doc.SelectNodes("//Article[IsActive='true']");
            return articles.Count;
        }

        /// <summary>
        /// Sauvegarde la base de données
        /// </summary>
        public static void BackupDatabase(string backupPath)
        {
            File.Copy(DatabasePath, backupPath, true);
        }

        /// <summary>
        /// Restaure la base de données
        /// </summary>
        public static void RestoreDatabase(string backupPath)
        {
            File.Copy(backupPath, DatabasePath, true);
        }
    }
}
