using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using SyncApp.Models;
using SyncApp.Services;

namespace SyncApp.Forms
{
    /// <summary>
    /// Formulaire principal de synchronisation d'inventaires
    /// </summary>
    public partial class SyncMainForm : Form
    {
        private readonly SyncService _syncService;
        private BackgroundWorker _syncWorker;
        private List<SyncItem> _lastSyncResults;

        /// <summary>
        /// Constructeur du formulaire principal
        /// </summary>
        public SyncMainForm()
        {
            InitializeComponent();
            _syncService = new SyncService();
            _lastSyncResults = new List<SyncItem>();
            
            // S'abonner aux événements du service
            _syncService.ProgressChanged += OnSyncProgressChanged;
            _syncService.MessageReceived += OnSyncMessageReceived;
            
            // Initialiser le BackgroundWorker
            InitializeBackgroundWorker();
            
            // Charger les informations initiales
            LoadInitialInfo();
        }

        /// <summary>
        /// Initialise le BackgroundWorker pour les opérations asynchrones
        /// </summary>
        private void InitializeBackgroundWorker()
        {
            _syncWorker = new BackgroundWorker();
            _syncWorker.WorkerReportsProgress = true;
            _syncWorker.WorkerSupportsCancellation = true;
            _syncWorker.DoWork += SyncWorker_DoWork;
            _syncWorker.ProgressChanged += SyncWorker_ProgressChanged;
            _syncWorker.RunWorkerCompleted += SyncWorker_RunWorkerCompleted;
        }

        /// <summary>
        /// Charge les informations initiales
        /// </summary>
        private void LoadInitialInfo()
        {
            // Vérifier l'existence des fichiers
            CheckFileExistence();
            
            // Afficher les statistiques
            UpdateStatistics();
            
            // Ajouter un message de bienvenue
            AddLogMessage("Application de synchronisation d'inventaires prête");
            AddLogMessage("Terminal Zebra MC2100 ↔ Laptop Desktop");
        }

        /// <summary>
        /// Vérifie l'existence des fichiers de données
        /// </summary>
        private void CheckFileExistence()
        {
            // Vérifier le fichier XML du terminal
            if (File.Exists("InventoryDB.xml"))
            {
                lblTerminalStatus.Text = "✓ Connecté";
                lblTerminalStatus.ForeColor = Color.Green;
                
                // Compter les articles XML
                try
                {
                    // Logique de comptage à implémenter
                    lblTerminalCount.Text = "Articles: ?";
                }
                catch
                {
                    lblTerminalCount.Text = "Erreur lecture";
                }
            }
            else
            {
                lblTerminalStatus.Text = "✗ Non trouvé";
                lblTerminalStatus.ForeColor = Color.Red;
                lblTerminalCount.Text = "Fichier XML manquant";
            }

            // Vérifier la base SQLite du laptop
            if (File.Exists("InventoryDB.sqlite"))
            {
                lblLaptopStatus.Text = "✓ Connecté";
                lblLaptopStatus.ForeColor = Color.Green;
                
                // Compter les articles SQLite
                try
                {
                    // Logique de comptage à implémenter
                    lblLaptopCount.Text = "Articles: ?";
                }
                catch
                {
                    lblLaptopCount.Text = "Erreur lecture";
                }
            }
            else
            {
                lblLaptopStatus.Text = "✗ Non trouvé";
                lblLaptopStatus.ForeColor = Color.Red;
                lblLaptopCount.Text = "Base SQLite manquante";
            }
        }

        /// <summary>
        /// Met à jour les statistiques affichées
        /// </summary>
        private void UpdateStatistics()
        {
            lblLastSync.Text = "Dernière sync: Jamais";
            
            // Vérifier s'il y a un log de synchronisation
            if (File.Exists("SyncLog.txt"))
            {
                try
                {
                    string[] lines = File.ReadAllLines("SyncLog.txt");
                    if (lines.Length > 0)
                    {
                        string lastLine = lines[lines.Length - 1];
                        lblLastSync.Text = "Dernière sync: " + lastLine.Substring(0, 19);
                    }
                }
                catch
                {
                    // Ignorer les erreurs de lecture du log
                }
            }
        }

        /// <summary>
        /// Ajoute un message au log
        /// </summary>
        private void AddLogMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AddLogMessage), message);
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            string logEntry = string.Format("[{0}] {1}", timestamp, message);
            
            txtLog.AppendText(logEntry + Environment.NewLine);
            txtLog.ScrollToCaret();
        }

        /// <summary>
        /// Gestionnaire d'événement pour la progression de synchronisation
        /// </summary>
        private void OnSyncProgressChanged(object sender, SyncProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object, SyncProgressEventArgs>(OnSyncProgressChanged), sender, e);
                return;
            }

            progressBar.Value = e.Percentage;
            lblProgress.Text = e.Message;
        }

        /// <summary>
        /// Gestionnaire d'événement pour les messages de synchronisation
        /// </summary>
        private void OnSyncMessageReceived(object sender, SyncMessageEventArgs e)
        {
            AddLogMessage(e.Message);
        }

        /// <summary>
        /// Synchronisation Terminal → Laptop
        /// </summary>
        private void btnTerminalToLaptop_Click(object sender, EventArgs e)
        {
            if (_syncWorker.IsBusy)
            {
                MessageBox.Show("Une synchronisation est déjà en cours.", "Synchronisation",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (MessageBox.Show(
                "Synchroniser les données du Terminal Zebra vers le Laptop ?\n\n" +
                "Cette opération va mettre à jour la base SQLite du laptop avec les données XML du terminal.",
                "Confirmation de synchronisation",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SetSyncInProgress(true);
                _syncWorker.RunWorkerAsync("TerminalToLaptop");
            }
        }

        /// <summary>
        /// Synchronisation Laptop → Terminal
        /// </summary>
        private void btnLaptopToTerminal_Click(object sender, EventArgs e)
        {
            if (_syncWorker.IsBusy)
            {
                MessageBox.Show("Une synchronisation est déjà en cours.", "Synchronisation",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (MessageBox.Show(
                "Synchroniser les données du Laptop vers le Terminal Zebra ?\n\n" +
                "Cette opération va mettre à jour le fichier XML du terminal avec les données SQLite du laptop.",
                "Confirmation de synchronisation",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SetSyncInProgress(true);
                _syncWorker.RunWorkerAsync("LaptopToTerminal");
            }
        }

        /// <summary>
        /// Synchronisation bidirectionnelle
        /// </summary>
        private void btnBidirectional_Click(object sender, EventArgs e)
        {
            if (_syncWorker.IsBusy)
            {
                MessageBox.Show("Une synchronisation est déjà en cours.", "Synchronisation",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (MessageBox.Show(
                "Effectuer une synchronisation bidirectionnelle ?\n\n" +
                "Cette opération va analyser les deux sources et détecter les conflits.\n" +
                "Les conflits devront être résolus manuellement.",
                "Confirmation de synchronisation",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SetSyncInProgress(true);
                _syncWorker.RunWorkerAsync("Bidirectional");
            }
        }

        /// <summary>
        /// Actualiser les informations
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            CheckFileExistence();
            UpdateStatistics();
            AddLogMessage("Informations actualisées");
        }

        /// <summary>
        /// Afficher les résultats de la dernière synchronisation
        /// </summary>
        private void btnViewResults_Click(object sender, EventArgs e)
        {
            if (_lastSyncResults.Count == 0)
            {
                MessageBox.Show("Aucun résultat de synchronisation disponible.", "Résultats",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // Ouvrir le formulaire des résultats
            SyncResultsForm resultsForm = new SyncResultsForm(_lastSyncResults);
            resultsForm.ShowDialog();
        }

        /// <summary>
        /// Définit l'état de synchronisation en cours
        /// </summary>
        private void SetSyncInProgress(bool inProgress)
        {
            btnTerminalToLaptop.Enabled = !inProgress;
            btnLaptopToTerminal.Enabled = !inProgress;
            btnBidirectional.Enabled = !inProgress;
            btnRefresh.Enabled = !inProgress;
            
            if (inProgress)
            {
                progressBar.Value = 0;
                lblProgress.Text = "Initialisation...";
            }
            else
            {
                progressBar.Value = 0;
                lblProgress.Text = "Prêt";
            }
        }

        /// <summary>
        /// Travail en arrière-plan pour la synchronisation
        /// </summary>
        private void SyncWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            string operation = e.Argument.ToString();
            List<SyncItem> results = new List<SyncItem>();

            try
            {
                switch (operation)
                {
                    case "TerminalToLaptop":
                        results = _syncService.SyncTerminalToLaptop();
                        break;
                    case "LaptopToTerminal":
                        results = _syncService.SyncLaptopToTerminal();
                        break;
                    case "Bidirectional":
                        results = _syncService.SyncBidirectional();
                        break;
                }

                e.Result = results;
            }
            catch (Exception ex)
            {
                e.Result = ex;
            }
        }

        /// <summary>
        /// Progression du travail en arrière-plan
        /// </summary>
        private void SyncWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            // La progression est gérée par les événements du SyncService
        }

        /// <summary>
        /// Fin du travail en arrière-plan
        /// </summary>
        private void SyncWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            SetSyncInProgress(false);

            if (e.Result is Exception)
            {
                Exception ex = (Exception)e.Result;
                MessageBox.Show("Erreur lors de la synchronisation:\n\n" + ex.Message,
                    "Erreur de synchronisation", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else if (e.Result is List<SyncItem>)
            {
                _lastSyncResults = (List<SyncItem>)e.Result;
                
                MessageBox.Show(
                    string.Format("Synchronisation terminée avec succès !\n\n{0} articles traités.",
                        _lastSyncResults.Count),
                    "Synchronisation terminée",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // Actualiser les informations
                CheckFileExistence();
                UpdateStatistics();
            }
        }

        /// <summary>
        /// Fermeture de l'application
        /// </summary>
        private void SyncMainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_syncWorker.IsBusy)
            {
                if (MessageBox.Show(
                    "Une synchronisation est en cours. Voulez-vous vraiment quitter ?",
                    "Synchronisation en cours",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question) == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                _syncWorker.CancelAsync();
            }
        }
    }
}
