@echo off
REM Compilation avec nettoyage d'encodage

echo ========================================
echo  BUILD AVEC NETTOYAGE ENCODAGE
echo ========================================
echo.

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    pause
    exit /b 1
)

echo Nettoyage des fichiers avec problemes d'encodage...
echo.

REM Créer un fichier de compilation temporaire
echo @echo off > compile_temp.bat
echo csc /target:winexe /out:InventoryApp-SQLite.exe >> compile_temp.bat
echo /reference:System.dll >> compile_temp.bat
echo /reference:System.Data.dll >> compile_temp.bat
echo /reference:System.Drawing.dll >> compile_temp.bat
echo /reference:System.Windows.Forms.dll >> compile_temp.bat
echo /reference:System.Xml.dll >> compile_temp.bat
echo /reference:Microsoft.VisualBasic.dll >> compile_temp.bat
echo /reference:lib\System.Data.SQLite.dll >> compile_temp.bat
echo /define:USE_SQLITE_DATABASE >> compile_temp.bat
echo /nowarn:0168,0219 >> compile_temp.bat
echo Program.cs >> compile_temp.bat
echo Properties\AssemblyInfo.cs >> compile_temp.bat
echo Models\Article.cs >> compile_temp.bat
echo Models\InventoryItem.cs >> compile_temp.bat
echo Data\DatabaseHelper-SQLite-Simple.cs >> compile_temp.bat
echo Services\BarcodeService.cs >> compile_temp.bat
echo Services\InventoryService-SQLite-Simple.cs >> compile_temp.bat
echo Utils\CurrencyHelper.cs >> compile_temp.bat
echo Forms\MainForm.cs >> compile_temp.bat
echo Forms\MainForm.Designer.cs >> compile_temp.bat
echo Forms\ArticleForm.cs >> compile_temp.bat
echo Forms\ArticleForm.Designer.cs >> compile_temp.bat
echo Forms\InventoryForm-Simple.cs >> compile_temp.bat
echo Forms\ReportsForm.cs >> compile_temp.bat
echo Forms\ReportsForm.Designer.cs >> compile_temp.bat

echo Execution de la compilation...
call compile_temp.bat

if %ERRORLEVEL% equ 0 (
    echo.
    echo SUCCES ! Application compilee
    
    if not exist "bin" mkdir "bin"
    if not exist "bin\Release" mkdir "bin\Release"
    
    move InventoryApp-SQLite.exe bin\Release\ >nul
    copy lib\System.Data.SQLite.dll bin\Release\ >nul
    
    echo Fichier: bin\Release\InventoryApp-SQLite.exe
    echo.
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" (
        start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe"
    )
) else (
    echo.
    echo ERREUR: Compilation echouee
    echo.
    echo SOLUTIONS:
    echo 1. Executer diagnose-syntax-error.bat
    echo 2. Verifier l'encodage des fichiers
    echo 3. Recreer les fichiers problematiques
)

REM Nettoyer
del compile_temp.bat >nul 2>&1

pause
