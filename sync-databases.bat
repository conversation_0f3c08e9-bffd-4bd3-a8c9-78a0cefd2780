@echo off
REM Script de synchronisation entre SQLite (laptop) et XML (terminal)

echo ========================================
echo  SYNCHRONISATION DES BASES DE DONNEES
echo ========================================
echo.

echo BASES DE DONNEES DETECTEES:
echo ===========================
echo.

REM Vérifier la base SQLite (laptop)
if exist "bin\Release\InventoryDB.sqlite" (
    echo [TROUVE] Base SQLite (laptop): bin\Release\InventoryDB.sqlite
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo          Taille: %%~zI octets
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo          Modifie: %%~tI
    set SQLITE_EXISTS=1
) else (
    echo [ABSENT] Base SQLite (laptop): bin\Release\InventoryDB.sqlite
    set SQLITE_EXISTS=0
)

echo.

REM Vérifier la base XML (terminal)
if exist "InventoryDB.xml" (
    echo [TROUVE] Base XML (terminal): InventoryDB.xml
    for %%I in ("InventoryDB.xml") do echo          Taille: %%~zI octets
    for %%I in ("InventoryDB.xml") do echo          Modifie: %%~tI
    set XML_EXISTS=1
) else (
    echo [ABSENT] Base XML (terminal): InventoryDB.xml
    set XML_EXISTS=0
)

echo.
echo ========================================
echo  OPTIONS DE SYNCHRONISATION
echo ========================================
echo.

if %SQLITE_EXISTS%==1 if %XML_EXISTS%==1 (
    echo Les deux bases existent. Choisissez la synchronisation:
    echo.
    echo 1. SQLite vers XML (laptop vers terminal)
    echo 2. XML vers SQLite (terminal vers laptop)
    echo 3. Sauvegarder les deux bases
    echo 4. Comparer les bases
    echo 5. Annuler
    echo.
    set /p CHOICE="Votre choix (1-5): "
    
    if "%CHOICE%"=="1" goto :sqlite_to_xml
    if "%CHOICE%"=="2" goto :xml_to_sqlite
    if "%CHOICE%"=="3" goto :backup_both
    if "%CHOICE%"=="4" goto :compare_bases
    if "%CHOICE%"=="5" goto :end
    
) else if %SQLITE_EXISTS%==1 (
    echo Seule la base SQLite existe.
    echo.
    echo OPTIONS:
    echo 1. Exporter vers XML pour le terminal
    echo 2. Sauvegarder la base SQLite
    echo 3. Annuler
    echo.
    set /p CHOICE="Votre choix (1-3): "
    
    if "%CHOICE%"=="1" goto :sqlite_to_xml
    if "%CHOICE%"=="2" goto :backup_sqlite
    if "%CHOICE%"=="3" goto :end
    
) else if %XML_EXISTS%==1 (
    echo Seule la base XML existe.
    echo.
    echo OPTIONS:
    echo 1. Importer vers SQLite pour le laptop
    echo 2. Sauvegarder la base XML
    echo 3. Annuler
    echo.
    set /p CHOICE="Votre choix (1-3): "
    
    if "%CHOICE%"=="1" goto :xml_to_sqlite
    if "%CHOICE%"=="2" goto :backup_xml
    if "%CHOICE%"=="3" goto :end
    
) else (
    echo Aucune base de donnees trouvee.
    echo.
    echo ACTIONS POSSIBLES:
    echo 1. Lancer l'application SQLite pour creer la base
    echo 2. Lancer l'application XML pour creer la base
    echo 3. Restaurer une sauvegarde
    echo.
    goto :end
)

:sqlite_to_xml
echo.
echo ========================================
echo  EXPORT SQLITE VERS XML
echo ========================================
echo.
echo ATTENTION: Cette operation va ecraser InventoryDB.xml
echo.
set /p CONFIRM="Confirmer l'export SQLite vers XML ? (O/N): "
if /i not "%CONFIRM%"=="O" goto :end

echo.
echo Export en cours...
echo.
echo METHODE MANUELLE REQUISE:
echo =========================
echo 1. Ouvrir l'application SQLite (laptop)
echo 2. Aller dans Gestion Articles
echo 3. Exporter tous les articles
echo 4. Copier les donnees vers l'application XML (terminal)
echo.
echo OU utiliser un outil de conversion SQLite vers XML
echo.
goto :end

:xml_to_sqlite
echo.
echo ========================================
echo  IMPORT XML VERS SQLITE
echo ========================================
echo.
echo ATTENTION: Cette operation va modifier InventoryDB.sqlite
echo.
set /p CONFIRM="Confirmer l'import XML vers SQLite ? (O/N): "
if /i not "%CONFIRM%"=="O" goto :end

echo.
echo Import en cours...
echo.
echo METHODE MANUELLE REQUISE:
echo =========================
echo 1. Ouvrir l'application XML (terminal)
echo 2. Exporter les donnees
echo 3. Ouvrir l'application SQLite (laptop)
echo 4. Importer les donnees exportees
echo.
goto :end

:backup_both
echo.
echo ========================================
echo  SAUVEGARDE DES DEUX BASES
echo ========================================
echo.

if not exist "Backups" mkdir "Backups"

set BACKUP_DATE=%DATE:~6,4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%
set BACKUP_DATE=%BACKUP_DATE: =0%

if %SQLITE_EXISTS%==1 (
    copy "bin\Release\InventoryDB.sqlite" "Backups\InventoryDB_SQLite_%BACKUP_DATE%.sqlite" >nul
    echo [OK] Base SQLite sauvegardee: Backups\InventoryDB_SQLite_%BACKUP_DATE%.sqlite
)

if %XML_EXISTS%==1 (
    copy "InventoryDB.xml" "Backups\InventoryDB_XML_%BACKUP_DATE%.xml" >nul
    echo [OK] Base XML sauvegardee: Backups\InventoryDB_XML_%BACKUP_DATE%.xml
)

echo.
echo Sauvegardes terminees dans le dossier Backups\
goto :end

:backup_sqlite
echo.
echo ========================================
echo  SAUVEGARDE BASE SQLITE
echo ========================================
echo.

if not exist "Backups" mkdir "Backups"

set BACKUP_DATE=%DATE:~6,4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%
set BACKUP_DATE=%BACKUP_DATE: =0%

copy "bin\Release\InventoryDB.sqlite" "Backups\InventoryDB_SQLite_%BACKUP_DATE%.sqlite" >nul
echo [OK] Base SQLite sauvegardee: Backups\InventoryDB_SQLite_%BACKUP_DATE%.sqlite
goto :end

:backup_xml
echo.
echo ========================================
echo  SAUVEGARDE BASE XML
echo ========================================
echo.

if not exist "Backups" mkdir "Backups"

set BACKUP_DATE=%DATE:~6,4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%
set BACKUP_DATE=%BACKUP_DATE: =0%

copy "InventoryDB.xml" "Backups\InventoryDB_XML_%BACKUP_DATE%.xml" >nul
echo [OK] Base XML sauvegardee: Backups\InventoryDB_XML_%BACKUP_DATE%.xml
goto :end

:compare_bases
echo.
echo ========================================
echo  COMPARAISON DES BASES
echo ========================================
echo.

echo INFORMATIONS BASES DE DONNEES:
echo ==============================
echo.

if %SQLITE_EXISTS%==1 (
    echo BASE SQLITE (laptop):
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo   Fichier: bin\Release\InventoryDB.sqlite
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo   Taille: %%~zI octets
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo   Modifie: %%~tI
    echo   Type: Base relationnelle SQLite
    echo   Utilisation: Application laptop
    echo.
)

if %XML_EXISTS%==1 (
    echo BASE XML (terminal):
    for %%I in ("InventoryDB.xml") do echo   Fichier: InventoryDB.xml
    for %%I in ("InventoryDB.xml") do echo   Taille: %%~zI octets
    for %%I in ("InventoryDB.xml") do echo   Modifie: %%~tI
    echo   Type: Fichier XML structure
    echo   Utilisation: Application terminal MC2100
    echo.
)

echo RECOMMANDATIONS:
echo ===============
echo • Utiliser SQLite sur laptop pour performance
echo • Utiliser XML sur terminal MC2100 pour compatibilite
echo • Synchroniser manuellement selon les besoins
echo • Sauvegarder regulierement les deux bases
echo.

:end
echo.
echo ========================================
echo  RESUME DE LA SYNCHRONISATION
echo ========================================
echo.

echo EMPLACEMENTS DES BASES:
echo =======================
echo • SQLite (laptop): bin\Release\InventoryDB.sqlite
echo • XML (terminal): InventoryDB.xml ou sur MC2100
echo.

echo SYNCHRONISATION:
echo ===============
echo • PAS de synchronisation automatique
echo • Synchronisation manuelle requise
echo • Utiliser les fonctions d'export/import
echo • Sauvegardes regulieres recommandees
echo.

echo OUTILS POUR CONSULTER LES BASES:
echo ================================
echo • SQLite: DB Browser for SQLite, SQLiteStudio
echo • XML: Editeur de texte, navigateur web
echo.

echo PROCHAINES ETAPES:
echo ==================
echo 1. Choisir la base principale (SQLite recommande)
echo 2. Configurer des sauvegardes automatiques
echo 3. Etablir une procedure de synchronisation
echo 4. Tester les transferts de donnees
echo.

pause
