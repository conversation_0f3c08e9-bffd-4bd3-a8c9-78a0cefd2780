@echo off
REM Création du package complet de synchronisation

echo ========================================
echo  PACKAGE SYNC APP - VERSION COMPLETE
echo ========================================
echo.

REM Compiler d'abord l'application
call build-sync-modern.bat

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilation echouee
    exit /b 1
)

echo.
echo Creation du package de distribution...

REM Créer le dossier de distribution
set DIST_DIR=dist\SyncApp-Modern
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%DIST_DIR%"
mkdir "%DIST_DIR%\Terminal"
mkdir "%DIST_DIR%\Laptop"
mkdir "%DIST_DIR%\Documentation"

echo Copie des fichiers...

REM Copier l'application de synchronisation
copy "bin\SyncApp\InventorySyncApp.exe" "%DIST_DIR%\Laptop\"
copy "bin\SyncApp\InventorySyncApp.exe.config" "%DIST_DIR%\Laptop\"
copy "bin\SyncApp\System.Data.SQLite.dll" "%DIST_DIR%\Laptop\"

REM Copier l'application terminal (si elle existe)
if exist "bin\Release\InventoryApp.exe" (
    copy "bin\Release\InventoryApp.exe" "%DIST_DIR%\Terminal\"
    echo [OK] Application terminal copiee
)

REM Créer la documentation
echo Guide d'Installation et d'Utilisation > "%DIST_DIR%\Documentation\README.txt"
echo ====================================== >> "%DIST_DIR%\Documentation\README.txt"
echo. >> "%DIST_DIR%\Documentation\README.txt"
echo INSTALLATION: >> "%DIST_DIR%\Documentation\README.txt"
echo 1. Laptop: Installer le contenu du dossier Laptop\ >> "%DIST_DIR%\Documentation\README.txt"
echo 2. Terminal: Copier InventoryApp.exe vers le MC2100 >> "%DIST_DIR%\Documentation\README.txt"
echo 3. Configurer les chemins dans InventorySyncApp.exe.config >> "%DIST_DIR%\Documentation\README.txt"
echo. >> "%DIST_DIR%\Documentation\README.txt"
echo UTILISATION: >> "%DIST_DIR%\Documentation\README.txt"
echo 1. Lancer InventorySyncApp.exe sur le laptop >> "%DIST_DIR%\Documentation\README.txt"
echo 2. Connecter le terminal MC2100 >> "%DIST_DIR%\Documentation\README.txt"
echo 3. Choisir le type de synchronisation >> "%DIST_DIR%\Documentation\README.txt"
echo 4. Resoudre les conflits si necessaire >> "%DIST_DIR%\Documentation\README.txt"

REM Créer un script d'installation automatique
echo @echo off > "%DIST_DIR%\install-sync.bat"
echo REM Installation automatique de l'application de synchronisation >> "%DIST_DIR%\install-sync.bat"
echo. >> "%DIST_DIR%\install-sync.bat"
echo echo Installation de l'application de synchronisation... >> "%DIST_DIR%\install-sync.bat"
echo. >> "%DIST_DIR%\install-sync.bat"
echo if not exist "C:\InventorySync" mkdir "C:\InventorySync" >> "%DIST_DIR%\install-sync.bat"
echo copy Laptop\*.* "C:\InventorySync\" >> "%DIST_DIR%\install-sync.bat"
echo. >> "%DIST_DIR%\install-sync.bat"
echo echo Installation terminee ! >> "%DIST_DIR%\install-sync.bat"
echo echo Lancez C:\InventorySync\InventorySyncApp.exe >> "%DIST_DIR%\install-sync.bat"
echo pause >> "%DIST_DIR%\install-sync.bat"

echo.
echo ========================================
echo  PACKAGE CREE AVEC SUCCES
echo ========================================
echo.
echo Dossier: %DIST_DIR%
echo.
echo CONTENU:
echo ========
echo • Laptop\     : Application de synchronisation moderne
echo • Terminal\   : Application pour Zebra MC2100
echo • Documentation\ : Guide d'installation
echo • install-sync.bat : Installation automatique
echo.
echo Pour deployer:
echo 1. Copier le dossier complet sur le laptop principal
echo 2. Executer install-sync.bat
echo 3. Configurer les chemins de synchronisation
echo.

REM Ouvrir le dossier de distribution
if exist "%DIST_DIR%" (
    explorer "%DIST_DIR%"
)

pause