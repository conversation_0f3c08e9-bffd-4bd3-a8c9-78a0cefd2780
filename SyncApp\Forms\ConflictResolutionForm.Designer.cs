using System;
using System.Drawing;
using System.Windows.Forms;

namespace SyncApp.Forms
{
    partial class ConflictResolutionForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new System.Windows.Forms.Panel();
            this.lblTitle = new System.Windows.Forms.Label();
            this.lblConflictCount = new System.Windows.Forms.Label();
            
            this.pnlConflictInfo = new System.Windows.Forms.Panel();
            this.lblBarcode = new System.Windows.Forms.Label();
            this.lblArticleName = new System.Windows.Forms.Label();
            this.lblConflictType = new System.Windows.Forms.Label();
            this.txtConflictComments = new System.Windows.Forms.TextBox();
            
            this.pnlVersions = new System.Windows.Forms.Panel();
            this.grpTerminal = new System.Windows.Forms.GroupBox();
            this.txtTerminalName = new System.Windows.Forms.TextBox();
            this.txtTerminalPrice = new System.Windows.Forms.TextBox();
            this.txtTerminalStock = new System.Windows.Forms.TextBox();
            this.txtTerminalCategory = new System.Windows.Forms.TextBox();
            this.txtTerminalLocation = new System.Windows.Forms.TextBox();
            this.lblTerminalDate = new System.Windows.Forms.Label();
            
            this.grpLaptop = new System.Windows.Forms.GroupBox();
            this.txtLaptopName = new System.Windows.Forms.TextBox();
            this.txtLaptopPrice = new System.Windows.Forms.TextBox();
            this.txtLaptopStock = new System.Windows.Forms.TextBox();
            this.txtLaptopCategory = new System.Windows.Forms.TextBox();
            this.txtLaptopLocation = new System.Windows.Forms.TextBox();
            this.lblLaptopDate = new System.Windows.Forms.Label();
            
            this.grpManual = new System.Windows.Forms.GroupBox();
            this.txtManualName = new System.Windows.Forms.TextBox();
            this.txtManualPrice = new System.Windows.Forms.TextBox();
            this.txtManualStock = new System.Windows.Forms.TextBox();
            this.txtManualCategory = new System.Windows.Forms.TextBox();
            this.txtManualLocation = new System.Windows.Forms.TextBox();
            
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnUseTerminal = new System.Windows.Forms.Button();
            this.btnUseLaptop = new System.Windows.Forms.Button();
            this.btnMerge = new System.Windows.Forms.Button();
            this.btnSaveManual = new System.Windows.Forms.Button();
            this.btnCancelManual = new System.Windows.Forms.Button();
            this.btnSkip = new System.Windows.Forms.Button();
            
            this.pnlNavigation = new System.Windows.Forms.Panel();
            this.btnPrevious = new System.Windows.Forms.Button();
            this.btnNext = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            
            this.SuspendLayout();
            
            // 
            // pnlHeader
            // 
            this.pnlHeader.BackColor = System.Drawing.Color.DarkRed;
            this.pnlHeader.Controls.Add(this.lblConflictCount);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlHeader.Location = new System.Drawing.Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new System.Drawing.Size(900, 60);
            this.pnlHeader.TabIndex = 0;
            
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(12, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(200, 21);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "⚠️ Résolution de Conflits";
            
            // 
            // lblConflictCount
            // 
            this.lblConflictCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lblConflictCount.AutoSize = true;
            this.lblConflictCount.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.lblConflictCount.ForeColor = System.Drawing.Color.White;
            this.lblConflictCount.Location = new System.Drawing.Point(750, 20);
            this.lblConflictCount.Name = "lblConflictCount";
            this.lblConflictCount.Size = new System.Drawing.Size(100, 19);
            this.lblConflictCount.TabIndex = 1;
            this.lblConflictCount.Text = "Conflit 1 sur 1";
            
            // Initialiser tous les contrôles avec des positions et tailles basiques
            InitializeBasicControls();
            
            // 
            // ConflictResolutionForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(900, 600);
            this.Controls.Add(this.pnlHeader);
            this.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ConflictResolutionForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Résolution de Conflits de Synchronisation";
            
            this.ResumeLayout(false);
        }

        /// <summary>
        /// Initialise les contrôles de base avec des positions simples
        /// </summary>
        private void InitializeBasicControls()
        {
            // Panel d'informations sur le conflit
            this.pnlConflictInfo = new System.Windows.Forms.Panel();
            this.pnlConflictInfo.Location = new System.Drawing.Point(10, 70);
            this.pnlConflictInfo.Size = new System.Drawing.Size(880, 80);
            this.pnlConflictInfo.BorderStyle = BorderStyle.FixedSingle;
            this.Controls.Add(this.pnlConflictInfo);
            
            this.lblBarcode = new System.Windows.Forms.Label();
            this.lblBarcode.Location = new System.Drawing.Point(10, 10);
            this.lblBarcode.Size = new System.Drawing.Size(400, 20);
            this.lblBarcode.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.pnlConflictInfo.Controls.Add(this.lblBarcode);
            
            this.lblArticleName = new System.Windows.Forms.Label();
            this.lblArticleName.Location = new System.Drawing.Point(10, 35);
            this.lblArticleName.Size = new System.Drawing.Size(400, 20);
            this.pnlConflictInfo.Controls.Add(this.lblArticleName);
            
            this.lblConflictType = new System.Windows.Forms.Label();
            this.lblConflictType.Location = new System.Drawing.Point(450, 10);
            this.lblConflictType.Size = new System.Drawing.Size(400, 20);
            this.lblConflictType.ForeColor = Color.Red;
            this.pnlConflictInfo.Controls.Add(this.lblConflictType);
            
            this.txtConflictComments = new System.Windows.Forms.TextBox();
            this.txtConflictComments.Location = new System.Drawing.Point(450, 35);
            this.txtConflictComments.Size = new System.Drawing.Size(400, 20);
            this.txtConflictComments.ReadOnly = true;
            this.pnlConflictInfo.Controls.Add(this.txtConflictComments);
            
            // Groupes de versions
            CreateVersionGroups();
            
            // Boutons d'action
            CreateActionButtons();
            
            // Boutons de navigation
            CreateNavigationButtons();
        }

        /// <summary>
        /// Crée les groupes de versions (Terminal, Laptop, Manuel)
        /// </summary>
        private void CreateVersionGroups()
        {
            // Groupe Terminal
            this.grpTerminal = new System.Windows.Forms.GroupBox();
            this.grpTerminal.Text = "📱 Version Terminal";
            this.grpTerminal.Location = new System.Drawing.Point(10, 160);
            this.grpTerminal.Size = new System.Drawing.Size(280, 200);
            this.Controls.Add(this.grpTerminal);
            
            CreateTerminalControls();
            
            // Groupe Laptop
            this.grpLaptop = new System.Windows.Forms.GroupBox();
            this.grpLaptop.Text = "💻 Version Laptop";
            this.grpLaptop.Location = new System.Drawing.Point(310, 160);
            this.grpLaptop.Size = new System.Drawing.Size(280, 200);
            this.Controls.Add(this.grpLaptop);
            
            CreateLaptopControls();
            
            // Groupe Manuel
            this.grpManual = new System.Windows.Forms.GroupBox();
            this.grpManual.Text = "✏️ Fusion Manuelle";
            this.grpManual.Location = new System.Drawing.Point(610, 160);
            this.grpManual.Size = new System.Drawing.Size(280, 200);
            this.Controls.Add(this.grpManual);
            
            CreateManualControls();
        }

        /// <summary>
        /// Crée les contrôles pour la version Terminal
        /// </summary>
        private void CreateTerminalControls()
        {
            this.txtTerminalName = new System.Windows.Forms.TextBox();
            this.txtTerminalName.Location = new System.Drawing.Point(10, 30);
            this.txtTerminalName.Size = new System.Drawing.Size(260, 23);
            this.txtTerminalName.ReadOnly = true;
            this.grpTerminal.Controls.Add(this.txtTerminalName);
            
            this.txtTerminalPrice = new System.Windows.Forms.TextBox();
            this.txtTerminalPrice.Location = new System.Drawing.Point(10, 60);
            this.txtTerminalPrice.Size = new System.Drawing.Size(120, 23);
            this.txtTerminalPrice.ReadOnly = true;
            this.grpTerminal.Controls.Add(this.txtTerminalPrice);
            
            this.txtTerminalStock = new System.Windows.Forms.TextBox();
            this.txtTerminalStock.Location = new System.Drawing.Point(150, 60);
            this.txtTerminalStock.Size = new System.Drawing.Size(120, 23);
            this.txtTerminalStock.ReadOnly = true;
            this.grpTerminal.Controls.Add(this.txtTerminalStock);
            
            this.txtTerminalCategory = new System.Windows.Forms.TextBox();
            this.txtTerminalCategory.Location = new System.Drawing.Point(10, 90);
            this.txtTerminalCategory.Size = new System.Drawing.Size(260, 23);
            this.txtTerminalCategory.ReadOnly = true;
            this.grpTerminal.Controls.Add(this.txtTerminalCategory);
            
            this.txtTerminalLocation = new System.Windows.Forms.TextBox();
            this.txtTerminalLocation.Location = new System.Drawing.Point(10, 120);
            this.txtTerminalLocation.Size = new System.Drawing.Size(260, 23);
            this.txtTerminalLocation.ReadOnly = true;
            this.grpTerminal.Controls.Add(this.txtTerminalLocation);
            
            this.lblTerminalDate = new System.Windows.Forms.Label();
            this.lblTerminalDate.Location = new System.Drawing.Point(10, 150);
            this.lblTerminalDate.Size = new System.Drawing.Size(260, 20);
            this.lblTerminalDate.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.grpTerminal.Controls.Add(this.lblTerminalDate);
        }

        /// <summary>
        /// Crée les contrôles pour la version Laptop
        /// </summary>
        private void CreateLaptopControls()
        {
            this.txtLaptopName = new System.Windows.Forms.TextBox();
            this.txtLaptopName.Location = new System.Drawing.Point(10, 30);
            this.txtLaptopName.Size = new System.Drawing.Size(260, 23);
            this.txtLaptopName.ReadOnly = true;
            this.grpLaptop.Controls.Add(this.txtLaptopName);
            
            this.txtLaptopPrice = new System.Windows.Forms.TextBox();
            this.txtLaptopPrice.Location = new System.Drawing.Point(10, 60);
            this.txtLaptopPrice.Size = new System.Drawing.Size(120, 23);
            this.txtLaptopPrice.ReadOnly = true;
            this.grpLaptop.Controls.Add(this.txtLaptopPrice);
            
            this.txtLaptopStock = new System.Windows.Forms.TextBox();
            this.txtLaptopStock.Location = new System.Drawing.Point(150, 60);
            this.txtLaptopStock.Size = new System.Drawing.Size(120, 23);
            this.txtLaptopStock.ReadOnly = true;
            this.grpLaptop.Controls.Add(this.txtLaptopStock);
            
            this.txtLaptopCategory = new System.Windows.Forms.TextBox();
            this.txtLaptopCategory.Location = new System.Drawing.Point(10, 90);
            this.txtLaptopCategory.Size = new System.Drawing.Size(260, 23);
            this.txtLaptopCategory.ReadOnly = true;
            this.grpLaptop.Controls.Add(this.txtLaptopCategory);
            
            this.txtLaptopLocation = new System.Windows.Forms.TextBox();
            this.txtLaptopLocation.Location = new System.Drawing.Point(10, 120);
            this.txtLaptopLocation.Size = new System.Drawing.Size(260, 23);
            this.txtLaptopLocation.ReadOnly = true;
            this.grpLaptop.Controls.Add(this.txtLaptopLocation);
            
            this.lblLaptopDate = new System.Windows.Forms.Label();
            this.lblLaptopDate.Location = new System.Drawing.Point(10, 150);
            this.lblLaptopDate.Size = new System.Drawing.Size(260, 20);
            this.lblLaptopDate.Font = new System.Drawing.Font("Segoe UI", 8F);
            this.grpLaptop.Controls.Add(this.lblLaptopDate);
        }

        /// <summary>
        /// Crée les contrôles pour l'édition manuelle
        /// </summary>
        private void CreateManualControls()
        {
            this.txtManualName = new System.Windows.Forms.TextBox();
            this.txtManualName.Location = new System.Drawing.Point(10, 30);
            this.txtManualName.Size = new System.Drawing.Size(260, 23);
            this.txtManualName.Enabled = false;
            this.grpManual.Controls.Add(this.txtManualName);
            
            this.txtManualPrice = new System.Windows.Forms.TextBox();
            this.txtManualPrice.Location = new System.Drawing.Point(10, 60);
            this.txtManualPrice.Size = new System.Drawing.Size(120, 23);
            this.txtManualPrice.Enabled = false;
            this.grpManual.Controls.Add(this.txtManualPrice);
            
            this.txtManualStock = new System.Windows.Forms.TextBox();
            this.txtManualStock.Location = new System.Drawing.Point(150, 60);
            this.txtManualStock.Size = new System.Drawing.Size(120, 23);
            this.txtManualStock.Enabled = false;
            this.grpManual.Controls.Add(this.txtManualStock);
            
            this.txtManualCategory = new System.Windows.Forms.TextBox();
            this.txtManualCategory.Location = new System.Drawing.Point(10, 90);
            this.txtManualCategory.Size = new System.Drawing.Size(260, 23);
            this.txtManualCategory.Enabled = false;
            this.grpManual.Controls.Add(this.txtManualCategory);
            
            this.txtManualLocation = new System.Windows.Forms.TextBox();
            this.txtManualLocation.Location = new System.Drawing.Point(10, 120);
            this.txtManualLocation.Size = new System.Drawing.Size(260, 23);
            this.txtManualLocation.Enabled = false;
            this.grpManual.Controls.Add(this.txtManualLocation);
        }

        /// <summary>
        /// Crée les boutons d'action
        /// </summary>
        private void CreateActionButtons()
        {
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.pnlButtons.Location = new System.Drawing.Point(10, 370);
            this.pnlButtons.Size = new System.Drawing.Size(880, 80);
            this.Controls.Add(this.pnlButtons);
            
            this.btnUseTerminal = new System.Windows.Forms.Button();
            this.btnUseTerminal.Text = "📱 Utiliser Terminal";
            this.btnUseTerminal.Location = new System.Drawing.Point(10, 10);
            this.btnUseTerminal.Size = new System.Drawing.Size(140, 30);
            this.btnUseTerminal.BackColor = Color.LightBlue;
            this.btnUseTerminal.Click += new System.EventHandler(this.btnUseTerminal_Click);
            this.pnlButtons.Controls.Add(this.btnUseTerminal);
            
            this.btnUseLaptop = new System.Windows.Forms.Button();
            this.btnUseLaptop.Text = "💻 Utiliser Laptop";
            this.btnUseLaptop.Location = new System.Drawing.Point(160, 10);
            this.btnUseLaptop.Size = new System.Drawing.Size(140, 30);
            this.btnUseLaptop.BackColor = Color.LightGreen;
            this.btnUseLaptop.Click += new System.EventHandler(this.btnUseLaptop_Click);
            this.pnlButtons.Controls.Add(this.btnUseLaptop);
            
            this.btnMerge = new System.Windows.Forms.Button();
            this.btnMerge.Text = "✏️ Fusionner";
            this.btnMerge.Location = new System.Drawing.Point(310, 10);
            this.btnMerge.Size = new System.Drawing.Size(140, 30);
            this.btnMerge.BackColor = Color.LightYellow;
            this.btnMerge.Click += new System.EventHandler(this.btnMerge_Click);
            this.pnlButtons.Controls.Add(this.btnMerge);
            
            this.btnSkip = new System.Windows.Forms.Button();
            this.btnSkip.Text = "⏭️ Ignorer";
            this.btnSkip.Location = new System.Drawing.Point(460, 10);
            this.btnSkip.Size = new System.Drawing.Size(140, 30);
            this.btnSkip.BackColor = Color.LightGray;
            this.btnSkip.Click += new System.EventHandler(this.btnSkip_Click);
            this.pnlButtons.Controls.Add(this.btnSkip);
            
            // Boutons pour l'édition manuelle (cachés par défaut)
            this.btnSaveManual = new System.Windows.Forms.Button();
            this.btnSaveManual.Text = "💾 Sauvegarder";
            this.btnSaveManual.Location = new System.Drawing.Point(310, 45);
            this.btnSaveManual.Size = new System.Drawing.Size(140, 25);
            this.btnSaveManual.BackColor = Color.LightGreen;
            this.btnSaveManual.Visible = false;
            this.btnSaveManual.Click += new System.EventHandler(this.btnSaveManual_Click);
            this.pnlButtons.Controls.Add(this.btnSaveManual);
            
            this.btnCancelManual = new System.Windows.Forms.Button();
            this.btnCancelManual.Text = "❌ Annuler";
            this.btnCancelManual.Location = new System.Drawing.Point(460, 45);
            this.btnCancelManual.Size = new System.Drawing.Size(140, 25);
            this.btnCancelManual.BackColor = Color.LightCoral;
            this.btnCancelManual.Visible = false;
            this.btnCancelManual.Click += new System.EventHandler(this.btnCancelManual_Click);
            this.pnlButtons.Controls.Add(this.btnCancelManual);
        }

        /// <summary>
        /// Crée les boutons de navigation
        /// </summary>
        private void CreateNavigationButtons()
        {
            this.pnlNavigation = new System.Windows.Forms.Panel();
            this.pnlNavigation.Location = new System.Drawing.Point(10, 460);
            this.pnlNavigation.Size = new System.Drawing.Size(880, 50);
            this.Controls.Add(this.pnlNavigation);
            
            this.btnPrevious = new System.Windows.Forms.Button();
            this.btnPrevious.Text = "< Précédent";
            this.btnPrevious.Location = new System.Drawing.Point(10, 10);
            this.btnPrevious.Size = new System.Drawing.Size(100, 30);
            this.btnPrevious.Click += new System.EventHandler(this.btnPrevious_Click);
            this.pnlNavigation.Controls.Add(this.btnPrevious);
            
            this.btnNext = new System.Windows.Forms.Button();
            this.btnNext.Text = "Suivant >";
            this.btnNext.Location = new System.Drawing.Point(120, 10);
            this.btnNext.Size = new System.Drawing.Size(100, 30);
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            this.pnlNavigation.Controls.Add(this.btnNext);
            
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnCancel.Text = "❌ Annuler Tout";
            this.btnCancel.Location = new System.Drawing.Point(770, 10);
            this.btnCancel.Size = new System.Drawing.Size(100, 30);
            this.btnCancel.BackColor = Color.LightCoral;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            this.pnlNavigation.Controls.Add(this.btnCancel);
        }

        #endregion

        private System.Windows.Forms.Panel pnlHeader;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblConflictCount;
        private System.Windows.Forms.Panel pnlConflictInfo;
        private System.Windows.Forms.Label lblBarcode;
        private System.Windows.Forms.Label lblArticleName;
        private System.Windows.Forms.Label lblConflictType;
        private System.Windows.Forms.TextBox txtConflictComments;
        private System.Windows.Forms.Panel pnlVersions;
        private System.Windows.Forms.GroupBox grpTerminal;
        private System.Windows.Forms.TextBox txtTerminalName;
        private System.Windows.Forms.TextBox txtTerminalPrice;
        private System.Windows.Forms.TextBox txtTerminalStock;
        private System.Windows.Forms.TextBox txtTerminalCategory;
        private System.Windows.Forms.TextBox txtTerminalLocation;
        private System.Windows.Forms.Label lblTerminalDate;
        private System.Windows.Forms.GroupBox grpLaptop;
        private System.Windows.Forms.TextBox txtLaptopName;
        private System.Windows.Forms.TextBox txtLaptopPrice;
        private System.Windows.Forms.TextBox txtLaptopStock;
        private System.Windows.Forms.TextBox txtLaptopCategory;
        private System.Windows.Forms.TextBox txtLaptopLocation;
        private System.Windows.Forms.Label lblLaptopDate;
        private System.Windows.Forms.GroupBox grpManual;
        private System.Windows.Forms.TextBox txtManualName;
        private System.Windows.Forms.TextBox txtManualPrice;
        private System.Windows.Forms.TextBox txtManualStock;
        private System.Windows.Forms.TextBox txtManualCategory;
        private System.Windows.Forms.TextBox txtManualLocation;
        private System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Button btnUseTerminal;
        private System.Windows.Forms.Button btnUseLaptop;
        private System.Windows.Forms.Button btnMerge;
        private System.Windows.Forms.Button btnSaveManual;
        private System.Windows.Forms.Button btnCancelManual;
        private System.Windows.Forms.Button btnSkip;
        private System.Windows.Forms.Panel pnlNavigation;
        private System.Windows.Forms.Button btnPrevious;
        private System.Windows.Forms.Button btnNext;
        private System.Windows.Forms.Button btnCancel;
    }
}
