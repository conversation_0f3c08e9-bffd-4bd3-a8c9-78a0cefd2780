using System;
using System.Drawing;
using System.Windows.Forms;
using System.Xml;
using System.IO;
using ZebraApp.Models;
using ZebraApp.Services;

namespace ZebraApp.Forms
{
    /// <summary>
    /// Formulaire principal optimisé pour Zebra MC2100
    /// Compatible .NET Compact Framework 3.5
    /// </summary>
    public partial class ZebraMainForm : Form
    {
        private readonly ZebraInventoryService _inventoryService;
        private readonly ZebraBarcodeService _barcodeService;

        /// <summary>
        /// Constructeur du formulaire principal Zebra
        /// </summary>
        public ZebraMainForm()
        {
            InitializeComponent();
            
            _inventoryService = new ZebraInventoryService();
            _barcodeService = new ZebraBarcodeService();
            
            // S'abonner aux événements du scanner
            _barcodeService.BarcodeScanned += OnBarcodeScanned;
            
            // Initialiser l'interface
            InitializeZebraInterface();
            LoadStatistics();
        }

        /// <summary>
        /// Initialise l'interface spécifique au Zebra
        /// </summary>
        private void InitializeZebraInterface()
        {
            // Configuration pour écran tactile Zebra MC2100
            this.WindowState = FormWindowState.Maximized;
            this.Text = "Inventaire MC2100 - " + DateTime.Now.ToString("dd/MM/yyyy");
            
            // Activer le scanner
            _barcodeService.EnableScanner();
            
            // Afficher le statut
            lblStatus.Text = "Scanner activé - Prêt";
            lblStatus.ForeColor = Color.Green;
        }

        /// <summary>
        /// Charge les statistiques d'inventaire
        /// </summary>
        private void LoadStatistics()
        {
            try
            {
                int totalArticles = _inventoryService.GetTotalArticlesCount();
                decimal totalValue = _inventoryService.GetTotalStockValue();
                
                lblTotalArticles.Text = "Articles: " + totalArticles.ToString();
                lblTotalValue.Text = "Valeur: " + totalValue.ToString("0.000") + " DT";
                
                // Vérifier la base de données
                if (File.Exists("InventoryDB.xml"))
                {
                    FileInfo fileInfo = new FileInfo("InventoryDB.xml");
                    lblDbStatus.Text = "Base: OK (" + (fileInfo.Length / 1024).ToString() + " KB)";
                    lblDbStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblDbStatus.Text = "Base: Nouvelle";
                    lblDbStatus.ForeColor = Color.Orange;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Erreur: " + ex.Message;
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// Gestionnaire d'événement pour les codes-barres scannés
        /// </summary>
        private void OnBarcodeScanned(object sender, ZebraBarcodeEventArgs e)
        {
            try
            {
                // Afficher le code scanné
                txtBarcode.Text = e.Barcode;
                lblLastScan.Text = "Scanné: " + e.Barcode + " à " + DateTime.Now.ToString("HH:mm:ss");
                
                // Rechercher l'article
                ZebraArticle article = _inventoryService.GetArticleByBarcode(e.Barcode);
                
                if (article != null)
                {
                    // Article trouvé
                    txtArticleName.Text = article.Name;
                    txtCurrentStock.Text = article.StockQuantity.ToString();
                    txtPrice.Text = article.UnitPrice.ToString("0.000") + " DT";
                    
                    lblStatus.Text = "Article trouvé: " + article.Name;
                    lblStatus.ForeColor = Color.Green;
                    
                    // Activer les boutons d'action
                    btnUpdateStock.Enabled = true;
                    btnViewDetails.Enabled = true;
                }
                else
                {
                    // Article non trouvé
                    txtArticleName.Text = "";
                    txtCurrentStock.Text = "";
                    txtPrice.Text = "";
                    
                    lblStatus.Text = "Article non trouvé: " + e.Barcode;
                    lblStatus.ForeColor = Color.Red;
                    
                    // Proposer de créer l'article
                    btnCreateArticle.Enabled = true;
                    btnUpdateStock.Enabled = false;
                    btnViewDetails.Enabled = false;
                }
                
                // Focus sur la quantité pour saisie rapide
                txtNewQuantity.Focus();
                txtNewQuantity.SelectAll();
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Erreur scan: " + ex.Message;
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// Saisie manuelle de code-barres
        /// </summary>
        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                
                if (!string.IsNullOrEmpty(txtBarcode.Text))
                {
                    // Simuler un scan
                    OnBarcodeScanned(this, new ZebraBarcodeEventArgs(txtBarcode.Text));
                }
            }
        }

        /// <summary>
        /// Mettre à jour le stock
        /// </summary>
        private void btnUpdateStock_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtBarcode.Text) || string.IsNullOrEmpty(txtNewQuantity.Text))
                {
                    MessageBox.Show("Veuillez scanner un article et saisir une quantité.", "Information", 
                        MessageBoxButtons.OK, MessageBoxIcon.Asterisk, MessageBoxDefaultButton.Button1);
                    return;
                }

                string barcode = txtBarcode.Text;
                int newQuantity;
                
                if (!int.TryParse(txtNewQuantity.Text, out newQuantity))
                {
                    MessageBox.Show("Quantité invalide.", "Erreur", 
                        MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1);
                    return;
                }

                bool success = _inventoryService.UpdateStock(barcode, newQuantity);
                
                if (success)
                {
                    lblStatus.Text = "Stock mis à jour: " + newQuantity.ToString();
                    lblStatus.ForeColor = Color.Green;
                    
                    // Actualiser l'affichage
                    txtCurrentStock.Text = newQuantity.ToString();
                    txtNewQuantity.Text = "";
                    
                    // Actualiser les statistiques
                    LoadStatistics();
                    
                    // Son de confirmation (si supporté)
                    try
                    {
                        System.Media.SystemSounds.Asterisk.Play();
                    }
                    catch { }
                }
                else
                {
                    MessageBox.Show("Erreur lors de la mise à jour du stock.", "Erreur", 
                        MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erreur: " + ex.Message, "Erreur", 
                    MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1);
            }
        }

        /// <summary>
        /// Créer un nouvel article
        /// </summary>
        private void btnCreateArticle_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtBarcode.Text))
            {
                MessageBox.Show("Veuillez scanner ou saisir un code-barres.", "Information", 
                    MessageBoxButtons.OK, MessageBoxIcon.Asterisk, MessageBoxDefaultButton.Button1);
                return;
            }

            // Ouvrir le formulaire de création d'article
            ZebraArticleForm articleForm = new ZebraArticleForm(txtBarcode.Text);
            
            if (articleForm.ShowDialog() == DialogResult.OK)
            {
                // Article créé, actualiser l'affichage
                OnBarcodeScanned(this, new ZebraBarcodeEventArgs(txtBarcode.Text));
                LoadStatistics();
            }
        }

        /// <summary>
        /// Voir les détails de l'article
        /// </summary>
        private void btnViewDetails_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtBarcode.Text))
                return;

            ZebraArticle article = _inventoryService.GetArticleByBarcode(txtBarcode.Text);
            if (article != null)
            {
                ZebraArticleForm articleForm = new ZebraArticleForm(article);
                
                if (articleForm.ShowDialog() == DialogResult.OK)
                {
                    // Article modifié, actualiser l'affichage
                    OnBarcodeScanned(this, new ZebraBarcodeEventArgs(txtBarcode.Text));
                    LoadStatistics();
                }
            }
        }

        /// <summary>
        /// Afficher la liste des articles
        /// </summary>
        private void btnListArticles_Click(object sender, EventArgs e)
        {
            ZebraListForm listForm = new ZebraListForm();
            listForm.ShowDialog();
        }

        /// <summary>
        /// Démarrer une session d'inventaire
        /// </summary>
        private void btnStartInventory_Click(object sender, EventArgs e)
        {
            ZebraInventoryForm inventoryForm = new ZebraInventoryForm();
            inventoryForm.ShowDialog();
            
            // Actualiser après l'inventaire
            LoadStatistics();
        }

        /// <summary>
        /// Actualiser les données
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadStatistics();
            lblStatus.Text = "Données actualisées";
            lblStatus.ForeColor = Color.Green;
        }

        /// <summary>
        /// Quitter l'application
        /// </summary>
        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Quitter l'application d'inventaire ?", "Confirmation", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            {
                // Désactiver le scanner
                _barcodeService.DisableScanner();
                
                Application.Exit();
            }
        }

        /// <summary>
        /// Fermeture du formulaire
        /// </summary>
        private void ZebraMainForm_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // Désactiver le scanner
            _barcodeService.DisableScanner();
        }

        /// <summary>
        /// Effacer les champs
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            txtBarcode.Text = "";
            txtArticleName.Text = "";
            txtCurrentStock.Text = "";
            txtPrice.Text = "";
            txtNewQuantity.Text = "";
            
            btnCreateArticle.Enabled = false;
            btnUpdateStock.Enabled = false;
            btnViewDetails.Enabled = false;
            
            lblStatus.Text = "Champs effacés - Prêt";
            lblStatus.ForeColor = Color.Blue;
            
            txtBarcode.Focus();
        }
    }
}
