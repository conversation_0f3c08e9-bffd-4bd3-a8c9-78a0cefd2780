using System;
using System.Collections.Generic;
using System.Data;
#if !USE_XML_DATABASE
using System.Data.SqlServerCe;
#endif
using InventoryApp.Data;
using InventoryApp.Models;

namespace InventoryApp.Services
{
    /// <summary>
    /// Service métier pour la gestion de l'inventaire
    /// </summary>
    public class InventoryService
    {
        private readonly InventoryContext _context;

        /// <summary>
        /// Constructeur du service d'inventaire
        /// </summary>
        public InventoryService()
        {
            _context = new InventoryContext();
        }

        #region Gestion des articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        /// <param name="article">Article à ajouter</param>
        /// <returns>ID de l'article créé</returns>
        public int CreateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (string.IsNullOrEmpty(article.Barcode))
                throw new ArgumentException("Le code-barres est obligatoire");

            if (string.IsNullOrEmpty(article.Name))
                throw new ArgumentException("Le nom de l'article est obligatoire");

            // Vérifier que le code-barres n'existe pas déjà
            if (_context.GetArticleByBarcode(article.Barcode) != null)
                throw new InvalidOperationException("Un article avec ce code-barres existe déjà");

            return _context.AddArticle(article);
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        /// <param name="article">Article à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (article.Id <= 0)
                throw new ArgumentException("ID d'article invalide");

            return _context.UpdateArticle(article);
        }

        /// <summary>
        /// Supprime un article
        /// </summary>
        /// <param name="articleId">ID de l'article à supprimer</param>
        /// <returns>True si la suppression a réussi</returns>
        public bool DeleteArticle(int articleId)
        {
            if (articleId <= 0)
                throw new ArgumentException("ID d'article invalide");

            return _context.DeleteArticle(articleId);
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        /// <param name="barcode">Code-barres</param>
        /// <returns>Article ou null si non trouvé</returns>
        public Article GetArticleByBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return null;

            return _context.GetArticleByBarcode(barcode);
        }

        /// <summary>
        /// Recherche des articles
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des articles correspondants</returns>
        public List<Article> SearchArticles(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return _context.GetAllArticles();

            return _context.SearchArticles(searchTerm);
        }

        /// <summary>
        /// Récupère tous les articles
        /// </summary>
        /// <returns>Liste de tous les articles</returns>
        public List<Article> GetAllArticles()
        {
            return _context.GetAllArticles();
        }

        #endregion

        #region Gestion des stocks

        /// <summary>
        /// Met à jour le stock d'un article
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="newQuantity">Nouvelle quantité</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateStock(string barcode, int newQuantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            return _context.UpdateArticleStock(article.Id, newQuantity);
        }

        /// <summary>
        /// Ajoute une quantité au stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à ajouter</param>
        /// <returns>True si l'ajout a réussi</returns>
        public bool AddToStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            int newQuantity = article.StockQuantity + quantity;
            return _context.UpdateArticleStock(article.Id, newQuantity);
        }

        /// <summary>
        /// Retire une quantité du stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à retirer</param>
        /// <returns>True si le retrait a réussi</returns>
        public bool RemoveFromStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            if (article.StockQuantity < quantity)
                return false; // Stock insuffisant

            int newQuantity = article.StockQuantity - quantity;
            return _context.UpdateArticleStock(article.Id, newQuantity);
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        /// <returns>Liste des articles en rupture</returns>
        public List<Article> GetOutOfStockArticles()
        {
            return _context.GetOutOfStockArticles();
        }

        /// <summary>
        /// Récupère les articles en dessous du seuil minimum
        /// </summary>
        /// <returns>Liste des articles en dessous du seuil</returns>
        public List<Article> GetLowStockArticles()
        {
            return _context.GetLowStockArticles();
        }

        #endregion

        #region Inventaire

        /// <summary>
        /// Ajoute un élément d'inventaire
        /// </summary>
        /// <param name="inventoryItem">Élément d'inventaire</param>
        /// <returns>ID de l'élément créé</returns>
        public int AddInventoryItem(InventoryItem inventoryItem)
        {
            if (inventoryItem == null)
                throw new ArgumentNullException("inventoryItem");

            string sql = @"
                INSERT INTO InventoryItems (ArticleId, Barcode, ArticleName, CountedQuantity, 
                                          ExpectedQuantity, Location, CountDate, CountedBy, 
                                          Comments, IsValidated, InventorySession)
                VALUES (@ArticleId, @Barcode, @ArticleName, @CountedQuantity, 
                        @ExpectedQuantity, @Location, @CountDate, @CountedBy, 
                        @Comments, @IsValidated, @InventorySession)";

            var parameters = new[]
            {
                new SqlCeParameter("@ArticleId", inventoryItem.ArticleId),
                new SqlCeParameter("@Barcode", inventoryItem.Barcode),
                new SqlCeParameter("@ArticleName", inventoryItem.ArticleName),
                new SqlCeParameter("@CountedQuantity", inventoryItem.CountedQuantity),
                new SqlCeParameter("@ExpectedQuantity", inventoryItem.ExpectedQuantity),
                new SqlCeParameter("@Location", inventoryItem.Location ?? ""),
                new SqlCeParameter("@CountDate", inventoryItem.CountDate),
                new SqlCeParameter("@CountedBy", inventoryItem.CountedBy ?? ""),
                new SqlCeParameter("@Comments", inventoryItem.Comments ?? ""),
                new SqlCeParameter("@IsValidated", inventoryItem.IsValidated),
                new SqlCeParameter("@InventorySession", inventoryItem.InventorySession ?? "")
            };

            DatabaseHelper.ExecuteNonQuery(sql, parameters);

            // Récupérer l'ID généré
            string getIdSql = "SELECT @@IDENTITY";
            object result = DatabaseHelper.ExecuteScalar(getIdSql);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// Effectue un comptage d'inventaire pour un article
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="countedQuantity">Quantité comptée</param>
        /// <param name="countedBy">Utilisateur ayant effectué le comptage</param>
        /// <param name="location">Emplacement</param>
        /// <param name="session">Session d'inventaire</param>
        /// <returns>Élément d'inventaire créé</returns>
        public InventoryItem PerformInventoryCount(string barcode, int countedQuantity, 
            string countedBy, string location = "", string session = "")
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                throw new InvalidOperationException("Article non trouvé pour le code-barres: " + barcode);

            InventoryItem inventoryItem = new InventoryItem
            {
                ArticleId = article.Id,
                Barcode = barcode,
                ArticleName = article.Name,
                CountedQuantity = countedQuantity,
                ExpectedQuantity = article.StockQuantity,
                Location = location,
                CountedBy = countedBy,
                InventorySession = session
            };

            inventoryItem.Id = AddInventoryItem(inventoryItem);
            return inventoryItem;
        }

        /// <summary>
        /// Récupère les éléments d'inventaire d'une session
        /// </summary>
        /// <param name="session">Session d'inventaire</param>
        /// <returns>Liste des éléments d'inventaire</returns>
        public List<InventoryItem> GetInventoryItemsBySession(string session)
        {
            string sql = "SELECT * FROM InventoryItems WHERE InventorySession = @Session ORDER BY CountDate";
            var parameters = new[] { new SqlCeParameter("@Session", session) };

            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql, parameters);
            List<InventoryItem> items = new List<InventoryItem>();

            foreach (DataRow row in dataTable.Rows)
            {
                items.Add(MapDataRowToInventoryItem(row));
            }

            return items;
        }

        /// <summary>
        /// Valide un élément d'inventaire
        /// </summary>
        /// <param name="inventoryItemId">ID de l'élément d'inventaire</param>
        /// <param name="validatedBy">Utilisateur validant</param>
        /// <returns>True si la validation a réussi</returns>
        public bool ValidateInventoryItem(int inventoryItemId, string validatedBy)
        {
            string sql = @"
                UPDATE InventoryItems 
                SET IsValidated = 1, ValidatedDate = @ValidatedDate, ValidatedBy = @ValidatedBy
                WHERE Id = @Id";

            var parameters = new[]
            {
                new SqlCeParameter("@Id", inventoryItemId),
                new SqlCeParameter("@ValidatedDate", DateTime.Now),
                new SqlCeParameter("@ValidatedBy", validatedBy)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, parameters);
            return rowsAffected > 0;
        }

        /// <summary>
        /// Mappe une ligne de données vers un objet InventoryItem
        /// </summary>
        /// <param name="row">Ligne de données</param>
        /// <returns>InventoryItem</returns>
        private InventoryItem MapDataRowToInventoryItem(DataRow row)
        {
            return new InventoryItem
            {
                Id = Convert.ToInt32(row["Id"]),
                ArticleId = Convert.ToInt32(row["ArticleId"]),
                Barcode = row["Barcode"].ToString(),
                ArticleName = row["ArticleName"].ToString(),
                CountedQuantity = Convert.ToInt32(row["CountedQuantity"]),
                ExpectedQuantity = Convert.ToInt32(row["ExpectedQuantity"]),
                Location = row["Location"].ToString(),
                CountDate = Convert.ToDateTime(row["CountDate"]),
                CountedBy = row["CountedBy"].ToString(),
                Comments = row["Comments"].ToString(),
                IsValidated = Convert.ToBoolean(row["IsValidated"]),
                ValidatedDate = row["ValidatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["ValidatedDate"]),
                ValidatedBy = row["ValidatedBy"].ToString(),
                InventorySession = row["InventorySession"].ToString()
            };
        }

        #endregion

        #region Statistiques

        /// <summary>
        /// Récupère le nombre total d'articles
        /// </summary>
        /// <returns>Nombre d'articles</returns>
        public int GetTotalArticlesCount()
        {
            string sql = "SELECT COUNT(*) FROM Articles WHERE IsActive = 1";
            object result = DatabaseHelper.ExecuteScalar(sql);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// Récupère la valeur totale du stock
        /// </summary>
        /// <returns>Valeur totale</returns>
        public decimal GetTotalStockValue()
        {
            string sql = "SELECT SUM(StockQuantity * UnitPrice) FROM Articles WHERE IsActive = 1";
            object result = DatabaseHelper.ExecuteScalar(sql);
            return result == DBNull.Value ? 0 : Convert.ToDecimal(result);
        }

        #endregion
    }
}
