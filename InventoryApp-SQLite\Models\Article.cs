using System;

namespace InventoryApp.Models
{
    /// <summary>
    /// Modèle représentant un article dans l'inventaire
    /// </summary>
    public class Article
    {
        /// <summary>
        /// Identifiant unique de l'article
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Code-barres de l'article
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// Nom de l'article
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Description de l'article
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Catégorie de l'article
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Prix unitaire de l'article
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Quantité en stock
        /// </summary>
        public int StockQuantity { get; set; }

        /// <summary>
        /// Quantité minimale en stock (seuil d'alerte)
        /// </summary>
        public int MinimumStock { get; set; }

        /// <summary>
        /// Unité de mesure (pièce, kg, litre, etc.)
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// Emplacement de stockage
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Date de création de l'article
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date de dernière modification
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// Indique si l'article est actif
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Constructeur par défaut
        /// </summary>
        public Article()
        {
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            IsActive = true;
            Unit = "pièce";
            StockQuantity = 0;
            MinimumStock = 0;
            UnitPrice = 0;
        }

        /// <summary>
        /// Constructeur avec paramètres essentiels
        /// </summary>
        /// <param name="barcode">Code-barres</param>
        /// <param name="name">Nom de l'article</param>
        /// <param name="category">Catégorie</param>
        public Article(string barcode, string name, string category) : this()
        {
            Barcode = barcode;
            Name = name;
            Category = category;
        }

        /// <summary>
        /// Vérifie si l'article est en rupture de stock
        /// </summary>
        /// <returns>True si en rupture</returns>
        public bool IsOutOfStock()
        {
            return StockQuantity <= 0;
        }

        /// <summary>
        /// Vérifie si l'article est en dessous du seuil minimum
        /// </summary>
        /// <returns>True si en dessous du seuil</returns>
        public bool IsBelowMinimumStock()
        {
            return StockQuantity <= MinimumStock;
        }

        /// <summary>
        /// Met à jour la quantité en stock
        /// </summary>
        /// <param name="quantity">Nouvelle quantité</param>
        public void UpdateStock(int quantity)
        {
            StockQuantity = quantity;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Ajoute une quantité au stock
        /// </summary>
        /// <param name="quantity">Quantité à ajouter</param>
        public void AddToStock(int quantity)
        {
            StockQuantity += quantity;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Retire une quantité du stock
        /// </summary>
        /// <param name="quantity">Quantité à retirer</param>
        /// <returns>True si l'opération a réussi</returns>
        public bool RemoveFromStock(int quantity)
        {
            if (StockQuantity >= quantity)
            {
                StockQuantity -= quantity;
                LastModified = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Représentation textuelle de l'article
        /// </summary>
        /// <returns>Chaîne descriptive</returns>
        public override string ToString()
        {
            return string.Format("{0} - {1} (Stock: {2})", Barcode, Name, StockQuantity);
        }
    }
}
