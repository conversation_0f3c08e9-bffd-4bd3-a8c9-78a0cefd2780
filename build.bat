@echo off
REM Script de compilation pour l'application Inventaire Zebra MC2100
REM Auteur: Assistant IA
REM Date: 2024

echo ========================================
echo  BUILD SCRIPT - INVENTAIRE MC2100
echo ========================================
echo.

REM Rechercher MSBuild dans différentes versions de Visual Studio et .NET Framework
set MSBUILD_PATH=""

REM Chercher MSBuild dans Visual Studio 2022
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve dans Visual Studio 2022 Community
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve dans Visual Studio 2022 Professional
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve dans Visual Studio 2022 Enterprise

REM Chercher MSBuild dans Visual Studio 2019
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve dans Visual Studio 2019 Community
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve dans Visual Studio 2019 Professional

REM Chercher MSBuild dans .NET Framework
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
    echo MSBuild trouve dans Build Tools 2017
) else if exist "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe"
    echo MSBuild 14.0 trouve
) else if exist "%ProgramFiles(x86)%\MSBuild\12.0\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\MSBuild\12.0\Bin\MSBuild.exe"
    echo MSBuild 12.0 trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\MSBuild.exe" (
    set MSBUILD_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\MSBuild.exe"
    echo MSBuild .NET Framework 4.0 trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\MSBuild.exe" (
    set MSBUILD_PATH="%WINDIR%\Microsoft.NET\Framework\v3.5\MSBuild.exe"
    echo MSBuild .NET Framework 3.5 trouve
) else (
    echo ERREUR: MSBuild non trouve
    echo.
    echo Solutions possibles:
    echo 1. Installer Visual Studio Community (gratuit)
    echo 2. Installer .NET Framework SDK
    echo 3. Utiliser la compilation manuelle (voir README.md)
    echo.
    pause
    exit /b 1
)

echo.
echo Chemin Visual Studio: %MSBUILD_PATH%
echo.

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo Nettoyage termine.
echo.

REM Compiler en mode Debug
echo ========================================
echo  COMPILATION DEBUG
echo ========================================
%MSBUILD_PATH% InventoryApp.sln /p:Configuration=Debug /p:Platform="Any CPU" /t:Clean;Build /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation Debug
    echo Tentative avec .NET Framework 3.5...
    %MSBUILD_PATH% InventoryApp.sln /p:Configuration=Debug /p:Platform="Any CPU" /p:TargetFrameworkVersion=v3.5 /t:Clean;Build /verbosity:minimal
    if %ERRORLEVEL% neq 0 (
        echo ERREUR: Echec definitif de la compilation Debug
        pause
        exit /b 1
    )
)
echo Compilation Debug reussie.
echo.

REM Compiler en mode Release
echo ========================================
echo  COMPILATION RELEASE
echo ========================================
%MSBUILD_PATH% InventoryApp.sln /p:Configuration=Release /p:Platform="Any CPU" /t:Clean;Build /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation Release
    echo Tentative avec .NET Framework 3.5...
    %MSBUILD_PATH% InventoryApp.sln /p:Configuration=Release /p:Platform="Any CPU" /p:TargetFrameworkVersion=v3.5 /t:Clean;Build /verbosity:minimal
    if %ERRORLEVEL% neq 0 (
        echo ERREUR: Echec definitif de la compilation Release
        pause
        exit /b 1
    )
)
echo Compilation Release reussie.
echo.

REM Créer le dossier de distribution
echo ========================================
echo  CREATION PACKAGE DISTRIBUTION
echo ========================================
set DIST_DIR=dist
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%DIST_DIR%"
mkdir "%DIST_DIR%\Debug"
mkdir "%DIST_DIR%\Release"

REM Copier les fichiers Debug
echo Copie des fichiers Debug...
copy "bin\Debug\InventoryApp.exe" "%DIST_DIR%\Debug\"
if exist "bin\Debug\InventoryApp.exe.config" copy "bin\Debug\InventoryApp.exe.config" "%DIST_DIR%\Debug\"
if exist "bin\Debug\InventoryApp.pdb" copy "bin\Debug\InventoryApp.pdb" "%DIST_DIR%\Debug\"

REM Copier les fichiers Release
echo Copie des fichiers Release...
copy "bin\Release\InventoryApp.exe" "%DIST_DIR%\Release\"
if exist "bin\Release\InventoryApp.exe.config" copy "bin\Release\InventoryApp.exe.config" "%DIST_DIR%\Release\"

REM Copier la documentation
echo Copie de la documentation...
copy "README.md" "%DIST_DIR%\"
copy "DEPLOYMENT.md" "%DIST_DIR%\"
if exist "CHANGELOG.md" copy "CHANGELOG.md" "%DIST_DIR%\"

REM Créer un fichier d'information de build
echo Creation du fichier build-info...
echo Build Information > "%DIST_DIR%\BUILD-INFO.txt"
echo ================== >> "%DIST_DIR%\BUILD-INFO.txt"
echo Date: %DATE% %TIME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo Machine: %COMPUTERNAME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo User: %USERNAME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo Visual Studio: %MSBUILD_PATH% >> "%DIST_DIR%\BUILD-INFO.txt"
echo. >> "%DIST_DIR%\BUILD-INFO.txt"
echo Fichiers Debug: >> "%DIST_DIR%\BUILD-INFO.txt"
dir /b "%DIST_DIR%\Debug\" >> "%DIST_DIR%\BUILD-INFO.txt"
echo. >> "%DIST_DIR%\BUILD-INFO.txt"
echo Fichiers Release: >> "%DIST_DIR%\BUILD-INFO.txt"
dir /b "%DIST_DIR%\Release\" >> "%DIST_DIR%\BUILD-INFO.txt"

echo.
echo ========================================
echo  BUILD TERMINE AVEC SUCCES
echo ========================================
echo.
echo Fichiers generes dans le dossier: %DIST_DIR%
echo.
echo Debug:   %DIST_DIR%\Debug\InventoryApp.exe
echo Release: %DIST_DIR%\Release\InventoryApp.exe
echo.
echo Pour deployer sur MC2100:
echo 1. Copier le contenu de %DIST_DIR%\Release\ vers le terminal
echo 2. Placer dans \Program Files\InventoryApp\
echo 3. Creer un raccourci si necessaire
echo.
echo Consultez DEPLOYMENT.md pour plus d'informations.
echo.

REM Ouvrir le dossier de distribution
if exist "%DIST_DIR%" (
    echo Ouverture du dossier de distribution...
    explorer "%DIST_DIR%"
)

echo Appuyez sur une touche pour continuer...
pause >nul
