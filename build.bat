@echo off
REM Script de compilation pour l'application Inventaire Zebra MC2100
REM Auteur: Assistant IA
REM Date: 2024

echo ========================================
echo  BUILD SCRIPT - INVENTAIRE MC2100
echo ========================================
echo.

REM Vérifier que Visual Studio est installé
set MSBUILD_PATH=""
if exist "%ProgramFiles%\Microsoft Visual Studio 10.0\Common7\IDE\devenv.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio 10.0\Common7\IDE\devenv.exe"
    echo Visual Studio 2010 detecte
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio 10.0\Common7\IDE\devenv.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio 10.0\Common7\IDE\devenv.exe"
    echo Visual Studio 2010 detecte (x86)
) else if exist "%ProgramFiles%\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe"
    echo Visual Studio 2008 detecte
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe"
    echo Visual Studio 2008 detecte (x86)
) else (
    echo ERREUR: Visual Studio 2008/2010 non trouve
    echo Veuillez installer Visual Studio avec support Windows CE
    pause
    exit /b 1
)

echo.
echo Chemin Visual Studio: %MSBUILD_PATH%
echo.

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo Nettoyage termine.
echo.

REM Compiler en mode Debug
echo ========================================
echo  COMPILATION DEBUG
echo ========================================
%MSBUILD_PATH% InventoryApp.sln /p:Configuration=Debug /p:Platform="Any CPU" /t:Clean,Build
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation Debug
    pause
    exit /b 1
)
echo Compilation Debug reussie.
echo.

REM Compiler en mode Release
echo ========================================
echo  COMPILATION RELEASE
echo ========================================
%MSBUILD_PATH% InventoryApp.sln /p:Configuration=Release /p:Platform="Any CPU" /t:Clean,Build
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation Release
    pause
    exit /b 1
)
echo Compilation Release reussie.
echo.

REM Créer le dossier de distribution
echo ========================================
echo  CREATION PACKAGE DISTRIBUTION
echo ========================================
set DIST_DIR=dist
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%DIST_DIR%"
mkdir "%DIST_DIR%\Debug"
mkdir "%DIST_DIR%\Release"

REM Copier les fichiers Debug
echo Copie des fichiers Debug...
copy "bin\Debug\InventoryApp.exe" "%DIST_DIR%\Debug\"
if exist "bin\Debug\InventoryApp.exe.config" copy "bin\Debug\InventoryApp.exe.config" "%DIST_DIR%\Debug\"
if exist "bin\Debug\InventoryApp.pdb" copy "bin\Debug\InventoryApp.pdb" "%DIST_DIR%\Debug\"

REM Copier les fichiers Release
echo Copie des fichiers Release...
copy "bin\Release\InventoryApp.exe" "%DIST_DIR%\Release\"
if exist "bin\Release\InventoryApp.exe.config" copy "bin\Release\InventoryApp.exe.config" "%DIST_DIR%\Release\"

REM Copier la documentation
echo Copie de la documentation...
copy "README.md" "%DIST_DIR%\"
copy "DEPLOYMENT.md" "%DIST_DIR%\"
if exist "CHANGELOG.md" copy "CHANGELOG.md" "%DIST_DIR%\"

REM Créer un fichier d'information de build
echo Creation du fichier build-info...
echo Build Information > "%DIST_DIR%\BUILD-INFO.txt"
echo ================== >> "%DIST_DIR%\BUILD-INFO.txt"
echo Date: %DATE% %TIME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo Machine: %COMPUTERNAME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo User: %USERNAME% >> "%DIST_DIR%\BUILD-INFO.txt"
echo Visual Studio: %MSBUILD_PATH% >> "%DIST_DIR%\BUILD-INFO.txt"
echo. >> "%DIST_DIR%\BUILD-INFO.txt"
echo Fichiers Debug: >> "%DIST_DIR%\BUILD-INFO.txt"
dir /b "%DIST_DIR%\Debug\" >> "%DIST_DIR%\BUILD-INFO.txt"
echo. >> "%DIST_DIR%\BUILD-INFO.txt"
echo Fichiers Release: >> "%DIST_DIR%\BUILD-INFO.txt"
dir /b "%DIST_DIR%\Release\" >> "%DIST_DIR%\BUILD-INFO.txt"

echo.
echo ========================================
echo  BUILD TERMINE AVEC SUCCES
echo ========================================
echo.
echo Fichiers generes dans le dossier: %DIST_DIR%
echo.
echo Debug:   %DIST_DIR%\Debug\InventoryApp.exe
echo Release: %DIST_DIR%\Release\InventoryApp.exe
echo.
echo Pour deployer sur MC2100:
echo 1. Copier le contenu de %DIST_DIR%\Release\ vers le terminal
echo 2. Placer dans \Program Files\InventoryApp\
echo 3. Creer un raccourci si necessaire
echo.
echo Consultez DEPLOYMENT.md pour plus d'informations.
echo.

REM Ouvrir le dossier de distribution
if exist "%DIST_DIR%" (
    echo Ouverture du dossier de distribution...
    explorer "%DIST_DIR%"
)

echo Appuyez sur une touche pour continuer...
pause >nul
