# Application de Gestion d'Inventaire pour Zebra MC2100

## Description
Application native de gestion d'inventaire développée pour les terminaux Zebra MC2100 fonctionnant sous Windows CE 6.0.

## Caractéristiques techniques
- **Plateforme** : Windows CE 6.0
- **Framework** : .NET Compact Framework 3.5
- **Langage** : C#
- **Interface** : Windows Forms
- **Base de données** : SQL Server Compact Edition
- **Scanner** : Support natif du scanner de codes-barres intégré

## 🇹🇳 Fonctionnalités complètes

### 🏪 **Gestion des articles**
- ✅ Création, modification, suppression d'articles
- ✅ Recherche par nom, code-barres ou description
- ✅ Gestion des catégories et emplacements
- ✅ **Prix en Dinar Tunisien (DT)** avec format 0.000
- ✅ Suivi des stocks minimum et alertes

### 📱 **Scanner de codes-barres**
- ✅ Intégration native avec le scanner du MC2100
- ✅ Lecture automatique des codes-barres
- ✅ Support EAN-13, Code 128, etc.
- ✅ Saisie manuelle en cas de besoin

### 📊 **Module d'inventaire**
- ✅ Sessions d'inventaire avec horodatage
- ✅ Comptage par scan ou saisie manuelle
- ✅ Calcul automatique des écarts
- ✅ Validation avec commentaires
- ✅ Gestion des utilisateurs compteurs

### 📈 **Rapports détaillés**
- ✅ Rapport de stock global avec valeurs en DT
- ✅ Articles en rupture de stock
- ✅ Articles en stock bas
- ✅ Rapport par catégorie
- ✅ Interface optimisée pour écran tactile

## Structure du projet
```
InventoryApp/
├── Forms/                  # Interface utilisateur
│   ├── MainForm.cs        # Écran principal
│   ├── ArticleForm.cs     # Gestion des articles
│   ├── InventoryForm.cs   # Inventaire
│   └── ReportsForm.cs     # Rapports
├── Data/                  # Couche d'accès aux données
│   ├── DatabaseHelper.cs  # Helper base de données
│   └── InventoryContext.cs # Contexte de données
├── Models/                # Modèles de données
│   ├── Article.cs         # Modèle Article
│   └── InventoryItem.cs   # Modèle Inventaire
├── Services/              # Services métier
│   ├── BarcodeService.cs  # Service scanner
│   └── InventoryService.cs # Service inventaire
└── Resources/             # Ressources
    └── Images/            # Images et icônes
```

## Compilation

### 🚀 Méthode rapide (Recommandée)
```bash
# Exécuter le script de compilation final
build-final.bat
```

### 🔧 Méthodes alternatives
1. **Visual Studio Community** (gratuit) : `build.bat`
2. **Compilation simple** : `build-simple.bat`
3. **Manuel** : Voir `COMPILATION_GUIDE.md`

## Installation
1. Compiler l'application avec `build-final.bat`
2. Copier le dossier `dist\MC2100\` vers le terminal Zebra MC2100
3. Exécuter `install-mc2100.bat` sur le terminal
4. Ou copier manuellement vers `\Program Files\InventoryApp\`
5. Lancer l'application

## Utilisation
1. **Démarrage** : Lancer l'application depuis le menu principal
2. **Scan** : Utiliser le scanner intégré pour lire les codes-barres
3. **Inventaire** : Naviguer dans les menus pour gérer l'inventaire
4. **Synchronisation** : Exporter les données vers un système central

## Développement
- Visual Studio 2008/2010 avec SDK Windows CE
- .NET Compact Framework 3.5 SDK
- Zebra EMDK pour l'intégration scanner

## Support
Compatible avec les terminaux Zebra MC2100 et versions similaires sous Windows CE 6.0.
