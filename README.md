# Application de Gestion d'Inventaire pour Zebra MC2100

## Description
Application native de gestion d'inventaire développée pour les terminaux Zebra MC2100 fonctionnant sous Windows CE 6.0.

## Caractéristiques techniques
- **Plateforme** : Windows CE 6.0
- **Framework** : .NET Compact Framework 3.5
- **Langage** : C#
- **Interface** : Windows Forms
- **Base de données** : SQL Server Compact Edition
- **Scanner** : Support natif du scanner de codes-barres intégré

## Fonctionnalités
- ✅ Gestion des articles (CRUD)
- ✅ Scan de codes-barres automatique
- ✅ Inventaire et comptage des stocks
- ✅ Recherche d'articles
- ✅ Rapports d'inventaire
- ✅ Export/Import de données
- ✅ Interface optimisée pour écran tactile

## Structure du projet
```
InventoryApp/
├── Forms/                  # Interface utilisateur
│   ├── MainForm.cs        # Écran principal
│   ├── ArticleForm.cs     # Gestion des articles
│   ├── InventoryForm.cs   # Inventaire
│   └── ReportsForm.cs     # Rapports
├── Data/                  # Couche d'accès aux données
│   ├── DatabaseHelper.cs  # Helper base de données
│   └── InventoryContext.cs # Contexte de données
├── Models/                # Modèles de données
│   ├── Article.cs         # Modèle Article
│   └── InventoryItem.cs   # Modèle Inventaire
├── Services/              # Services métier
│   ├── BarcodeService.cs  # Service scanner
│   └── InventoryService.cs # Service inventaire
└── Resources/             # Ressources
    └── Images/            # Images et icônes
```

## Installation
1. Déployer l'application sur le terminal Zebra MC2100
2. S'assurer que .NET Compact Framework 3.5 est installé
3. Configurer la base de données locale
4. Lancer l'application

## Utilisation
1. **Démarrage** : Lancer l'application depuis le menu principal
2. **Scan** : Utiliser le scanner intégré pour lire les codes-barres
3. **Inventaire** : Naviguer dans les menus pour gérer l'inventaire
4. **Synchronisation** : Exporter les données vers un système central

## Développement
- Visual Studio 2008/2010 avec SDK Windows CE
- .NET Compact Framework 3.5 SDK
- Zebra EMDK pour l'intégration scanner

## Support
Compatible avec les terminaux Zebra MC2100 et versions similaires sous Windows CE 6.0.
