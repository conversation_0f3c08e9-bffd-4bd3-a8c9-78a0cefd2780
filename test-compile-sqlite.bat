@echo off
REM Test rapide de compilation SQLite

echo ========================================
echo  TEST COMPILATION SQLITE
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur non disponible
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo.
    echo TELECHARGEMENT REQUIS:
    echo 1. Aller sur: https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger: "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Extraire le ZIP
    echo 4. Copier System.Data.SQLite.dll dans le dossier lib\
    echo.
    pause
    exit /b 1
)

echo [OK] Compilateur et SQLite disponibles
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Test" mkdir "bin\Test"

echo Test de compilation en cours...
echo.

REM Compilation de test
csc /target:winexe /out:bin\Test\InventoryApp-SQLite-Test.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite.cs Services\BarcodeService.cs Services\InventoryService-SQLite.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo.
    echo [SUCCES] Compilation de test reussie !
    echo.
    
    REM Copier la DLL SQLite
    copy "lib\System.Data.SQLite.dll" "bin\Test\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier: bin\Test\InventoryApp-SQLite-Test.exe
    for %%I in ("bin\Test\InventoryApp-SQLite-Test.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION SQLITE PRETE !
    echo.
    echo FONCTIONNALITES:
    echo • Base de donnees SQLite locale
    echo • Devise Dinar Tunisien (DT)
    echo • Gestion articles complete
    echo • Rapports detailles
    echo • Compatible laptop
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement du test SQLite...
        start "Test SQLite" "bin\Test\InventoryApp-SQLite-Test.exe"
        echo.
        echo TESTEZ:
        echo 1. Creation d'articles avec prix en DT
        echo 2. Recherche d'articles
        echo 3. Generation de rapports
        echo 4. Verification du fichier InventoryDB.sqlite
        echo.
        echo Le fichier de base sera cree dans:
        echo bin\Test\InventoryDB.sqlite
    )
    
    echo.
    echo Si le test fonctionne, utilisez:
    echo build-sqlite-fixed.bat
    
) else (
    echo.
    echo [ERREUR] Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
)

echo.
REM Nettoyer
rmdir /s /q "bin\Test" >nul 2>&1

pause
