# Guide de Compilation - Application Inventaire Zebra MC2100

## 🚨 Problème rencontré

Le script `build.bat` recherche Visual Studio 2008/2010 avec support Windows CE, qui ne sont plus facilement disponibles. Voici plusieurs solutions alternatives.

## 🔧 Solutions de compilation

### **Solution 1: Compilation moderne (Recommandée pour test)**

#### Prérequis
- Windows 10/11
- .NET Framework 4.7.2 ou supérieur

#### Étapes
1. **Utiliser le script simplifié** :
   ```bash
   build-simple.bat
   ```

2. **Ou compiler manuellement** :
   ```bash
   # Ouvrir une invite de commande
   cd C:\Users\<USER>\Desktop\Inventaire
   
   # Compiler avec csc.exe
   %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe /target:winexe /out:InventoryApp.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Program.cs Models\*.cs Data\*.cs Services\*.cs Forms\MainForm.cs Forms\MainForm.Designer.cs
   ```

#### ⚠️ Limitations
- Pas de support SQL Server CE (base de données limitée)
- Interface non optimisée pour MC2100
- Fonctionnalités réduites

### **Solution 2: Visual Studio Community (Gratuit)**

#### Installation
1. **Télécharger Visual Studio Community 2022** (gratuit)
   - Site : https://visualstudio.microsoft.com/fr/vs/community/
   
2. **Installer avec les composants** :
   - Développement .NET Desktop
   - .NET Framework 4.7.2+ SDK
   - Outils de build MSBuild

3. **Compiler le projet** :
   ```bash
   # Utiliser le script mis à jour
   build.bat
   ```

### **Solution 3: Outils de développement Windows CE (Pour production)**

#### Pour une version complète compatible MC2100

1. **Télécharger les outils Windows CE** :
   - Windows Embedded CE 6.0 Tools
   - .NET Compact Framework 3.5 SDK
   - Platform Builder (optionnel)

2. **Alternative moderne** :
   - Utiliser .NET Core avec support Windows CE
   - Xamarin pour applications mobiles
   - Avalonia UI pour interface cross-platform

### **Solution 4: Compilation en ligne**

#### Utiliser GitHub Actions ou Azure DevOps
```yaml
# .github/workflows/build.yml
name: Build Inventory App
on: [push]
jobs:
  build:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1
    - name: Build
      run: msbuild InventoryApp.sln /p:Configuration=Release
```

## 🔄 Adaptation pour MC2100

### **Option A: Version SQLite (Recommandée)**

1. **Modifier DatabaseHelper.cs** :
```csharp
// Remplacer SqlCeConnection par SQLiteConnection
using System.Data.SQLite;

public static string ConnectionString
{
    get
    {
        string dbPath = Path.Combine(GetApplicationPath(), "InventoryDB.sqlite");
        return string.Format("Data Source={0};Version=3;", dbPath);
    }
}
```

2. **Adapter les requêtes SQL** :
```sql
-- SQLite au lieu de SQL Server CE
CREATE TABLE Articles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Barcode TEXT NOT NULL UNIQUE,
    Name TEXT NOT NULL,
    -- etc.
);
```

### **Option B: Version fichier texte**

1. **Remplacer la base de données par des fichiers CSV/XML**
2. **Utiliser System.IO pour la persistance**
3. **Plus simple mais moins performant**

## 🧪 Test de l'application

### **Test sur PC Windows**
1. Compiler avec `build-simple.bat`
2. Lancer `bin\Release\InventoryApp.exe`
3. Tester les fonctionnalités de base
4. Vérifier l'interface

### **Test sur émulateur Windows CE**
1. Installer Windows CE Emulator
2. Déployer l'application
3. Tester avec émulation du scanner

### **Test sur MC2100 réel**
1. Copier `InventoryApp.exe` vers le terminal
2. Installer dans `\Program Files\InventoryApp\`
3. Tester toutes les fonctionnalités
4. Valider le scanner intégré

## 📋 Checklist de compilation

### ✅ **Avant compilation**
- [ ] .NET Framework installé
- [ ] Visual Studio ou MSBuild disponible
- [ ] Tous les fichiers sources présents
- [ ] Dépendances résolues

### ✅ **Après compilation**
- [ ] Fichier .exe généré sans erreur
- [ ] Taille du fichier raisonnable (~500KB)
- [ ] Test de lancement sur PC
- [ ] Vérification des dépendances

### ✅ **Avant déploiement MC2100**
- [ ] Version compatible Windows CE
- [ ] Base de données adaptée (SQLite/fichiers)
- [ ] Interface optimisée petit écran
- [ ] Scanner testé

## 🚀 Déploiement rapide

### **Pour test immédiat**
```bash
# 1. Compilation simple
build-simple.bat

# 2. Test sur PC
bin\Release\InventoryApp.exe

# 3. Copie vers MC2100
copy bin\Release\InventoryApp.exe \\MC2100\Program Files\InventoryApp\
```

### **Pour production**
```bash
# 1. Installation Visual Studio Community
# 2. Compilation complète
build.bat

# 3. Test complet
# 4. Déploiement avec installeur
```

## 🆘 Dépannage

### **Erreur "MSBuild non trouvé"**
- Installer Visual Studio Community
- Ou installer .NET Framework SDK
- Ou utiliser `build-simple.bat`

### **Erreur "SQL Server CE non trouvé"**
- Utiliser SQLite à la place
- Ou installer SQL Server CE runtime
- Ou utiliser fichiers texte

### **Erreur "Windows CE non supporté"**
- Utiliser émulateur pour test
- Adapter le code pour .NET standard
- Tester sur matériel réel

### **Interface non adaptée**
- Ajuster les tailles dans Designer
- Tester sur écran 240x320
- Optimiser pour tactile

## 📞 Support

Si vous rencontrez des difficultés :

1. **Essayez d'abord** : `build-simple.bat`
2. **Pour aide** : Consultez les logs d'erreur
3. **Alternative** : Compilation manuelle étape par étape
4. **Dernier recours** : Développement avec outils modernes + adaptation

---

**Note** : L'objectif est d'avoir une application fonctionnelle sur MC2100. La méthode de compilation peut varier selon vos outils disponibles.
