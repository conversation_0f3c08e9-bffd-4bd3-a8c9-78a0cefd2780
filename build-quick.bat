@echo off
REM Script de compilation rapide - Version ultra-simplifiee

echo ========================================
echo  BUILD QUICK - INVENTAIRE MC2100
echo ========================================
echo.

REM Aller dans le bon repertoire
cd /d "%~dp0"
echo Repertoire: %CD%
echo.

REM Rechercher le compilateur (Visual Studio 2022 et versions precedentes)
set CSC_PATH=""

REM Visual Studio 2022 Community
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo Compilateur trouve: Visual Studio 2022 Community
    goto :compiler_found
)

REM Visual Studio 2022 Professional
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo Compilateur trouve: Visual Studio 2022 Professional
    goto :compiler_found
)

REM Visual Studio 2022 Enterprise
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo Compilateur trouve: Visual Studio 2022 Enterprise
    goto :compiler_found
)

REM Visual Studio 2019
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo Compilateur trouve: Visual Studio 2019 Community
    goto :compiler_found
)

REM .NET Framework classique
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo Compilateur trouve: .NET Framework 4.0 (64-bit)
    goto :compiler_found
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo Compilateur trouve: .NET Framework 4.0
    goto :compiler_found
)

if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe"
    echo Compilateur trouve: .NET Framework 3.5
    goto :compiler_found
)

echo ERREUR: Compilateur C# non trouve
echo.
echo Chemins verifies:
echo - Visual Studio 2022: %ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe
echo - .NET Framework: %WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe
echo.
echo SOLUTIONS:
echo 1. Redemarrer votre PC apres installation de Visual Studio
echo 2. Reinstaller Visual Studio avec "Developpement .NET Desktop"
echo 3. Executer en tant qu'administrateur
echo.
pause
exit /b 1

:compiler_found

echo.

REM Creer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation en cours...
echo.

REM Compiler avec tous les fichiers
%CSC_PATH% /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm.cs" ^
    "Forms\InventoryForm.Designer.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    REM Afficher la taille du fichier
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo DEPLOIEMENT:
    echo 1. Copier bin\Release\InventoryApp.exe vers votre MC2100
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Inventaire Test" "bin\Release\InventoryApp.exe"
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo Compilation terminee avec succes !
echo.
pause
