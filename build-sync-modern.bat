@echo off
REM Script de compilation pour l'application de synchronisation moderne
REM Version avec design moderne et elegant

echo ========================================
echo  BUILD SYNC APP - DESIGN MODERNE
echo ========================================
echo.

REM Vérifier la disponibilité du compilateur
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur C# non disponible
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers ce dossier
    echo 3. Executer ce script
    echo.
    pause
    exit /b 1
)

echo [OK] Compilateur C# disponible
echo.

REM Vérifier SQLite
if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant dans lib\
    echo Telechargez SQLite pour .NET Framework
    pause
    exit /b 1
)

echo [OK] SQLite disponible
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\SyncApp" mkdir "bin\SyncApp"

echo Compilation de l'application de synchronisation moderne...
echo.

REM Créer un fichier de réponse pour éviter les problèmes de ligne de commande
echo /target:winexe > sync_modern.rsp
echo /out:bin\SyncApp\InventorySyncApp.exe >> sync_modern.rsp
echo /reference:System.dll >> sync_modern.rsp
echo /reference:System.Data.dll >> sync_modern.rsp
echo /reference:System.Drawing.dll >> sync_modern.rsp
echo /reference:System.Windows.Forms.dll >> sync_modern.rsp
echo /reference:System.Xml.dll >> sync_modern.rsp
echo /reference:lib\System.Data.SQLite.dll >> sync_modern.rsp
echo /define:USE_SQLITE_DATABASE >> sync_modern.rsp
echo /nowarn:0168,0219 >> sync_modern.rsp
echo SyncApp\Program.cs >> sync_modern.rsp
echo SyncApp\Properties\AssemblyInfo.cs >> sync_modern.rsp
echo Models\Article.cs >> sync_modern.rsp
echo Models\InventoryItem.cs >> sync_modern.rsp
echo Data\DatabaseHelper-SQLite-Simple.cs >> sync_modern.rsp
echo Services\BarcodeService.cs >> sync_modern.rsp
echo Services\InventoryService-SQLite-Simple.cs >> sync_modern.rsp
echo Utils\CurrencyHelper.cs >> sync_modern.rsp
echo SyncApp\Services\SyncService.cs >> sync_modern.rsp
echo SyncApp\Models\SyncResult.cs >> sync_modern.rsp
echo SyncApp\Models\SyncItem.cs >> sync_modern.rsp
echo SyncApp\Forms\SyncMainForm.cs >> sync_modern.rsp
echo SyncApp\Forms\SyncMainForm.Designer.cs >> sync_modern.rsp
echo SyncApp\Forms\SyncResultsForm.cs >> sync_modern.rsp
echo SyncApp\Forms\SyncResultsForm.Designer.cs >> sync_modern.rsp
echo SyncApp\Forms\ConflictResolutionForm.cs >> sync_modern.rsp
echo SyncApp\Forms\ConflictResolutionForm.Designer.cs >> sync_modern.rsp

echo Compilation en cours avec design moderne...
csc @sync_modern.rsp

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  COMPILATION REUSSIE !
    echo ========================================
    echo.
    
    REM Copier SQLite DLL
    copy "lib\System.Data.SQLite.dll" "bin\SyncApp\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    REM Créer le fichier de configuration
    echo ^<?xml version="1.0" encoding="utf-8"?^> > "bin\SyncApp\InventorySyncApp.exe.config"
    echo ^<configuration^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo   ^<appSettings^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo     ^<add key="DatabaseType" value="SQLite" /^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo     ^<add key="TerminalPath" value="\\MC2100\SharedFolder\" /^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo     ^<add key="LaptopPath" value="C:\InventoryData\" /^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo     ^<add key="AutoBackup" value="true" /^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo   ^</appSettings^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    echo ^</configuration^> >> "bin\SyncApp\InventorySyncApp.exe.config"
    
    echo.
    echo Fichier genere: bin\SyncApp\InventorySyncApp.exe
    for %%I in ("bin\SyncApp\InventorySyncApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo NOUVELLES FONCTIONNALITES DESIGN:
    echo =================================
    echo • Interface moderne avec Material Design
    echo • Palette de couleurs professionnelle
    echo • Boutons flat avec effets hover
    echo • Header sombre avec icones emoji
    echo • Log terminal authentique (noir/vert)
    echo • Barre de progression moderne
    echo • Typographie Segoe UI coherente
    echo • Espacement et padding optimises
    echo.
    
    echo FONCTIONNALITES SYNC:
    echo ====================
    echo • Synchronisation bidirectionnelle
    echo • Detection automatique des conflits
    echo • Resolution manuelle des conflits
    echo • Sauvegarde automatique
    echo • Logs detailles des operations
    echo • Filtrage et recherche des resultats
    echo.
    
    echo DEPLOIEMENT:
    echo ===========
    echo 1. Copier le dossier bin\SyncApp\ complet
    echo 2. Installer sur le laptop principal
    echo 3. Configurer les chemins dans le .config
    echo 4. Connecter le terminal MC2100 via USB/WiFi
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application de synchronisation...
        start "Sync App Moderne" "bin\SyncApp\InventorySyncApp.exe"
    )
    
) else (
    echo.
    echo ERREUR: Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo SOLUTIONS:
    echo 1. Verifier que tous les fichiers .cs existent
    echo 2. Executer depuis Developer Command Prompt
    echo 3. Verifier les references SQLite
    echo.
    pause
    exit /b 1
)

REM Nettoyer le fichier de réponse
del sync_modern.rsp >nul 2>&1

echo.
echo ========================================
echo  BUILD TERMINE AVEC SUCCES
echo ========================================
echo.
echo L'application de synchronisation moderne est prete !
echo Interface elegante et fonctionnalites avancees.
echo.
pause