@echo off
REM Script pour aider à installer .NET Framework

echo ========================================
echo  INSTALLATION .NET FRAMEWORK
echo ========================================
echo.

echo Ce script va vous aider à installer .NET Framework
echo pour compiler l'application d'inventaire.
echo.

echo ETAPE 1: Verification de la version Windows
echo ===========================================
ver
echo.

echo ETAPE 2: Verification des versions .NET existantes
echo ==================================================
echo.

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319" (
    echo [TROUVE] .NET Framework 4.0
    if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
        echo [OK] Compilateur C# 4.0 disponible
    ) else (
        echo [MANQUE] Compilateur C# 4.0 manquant
    )
) else (
    echo [MANQUE] .NET Framework 4.0 non installé
)

if exist "%WINDIR%\Microsoft.NET\Framework\v3.5" (
    echo [TROUVE] .NET Framework 3.5
) else (
    echo [MANQUE] .NET Framework 3.5 non installé
)

echo.

echo ETAPE 3: Solutions recommandées
echo ================================
echo.
echo OPTION 1 - Visual Studio Community (Recommandée)
echo -------------------------------------------------
echo • Gratuit et complet
echo • Inclut tout ce qu'il faut
echo • Taille: ~3-5 GB
echo • Lien: https://visualstudio.microsoft.com/fr/vs/community/
echo.

echo OPTION 2 - .NET Framework Developer Pack
echo -----------------------------------------
echo • Plus léger (~200 MB)
echo • Juste le compilateur
echo • Lien: https://dotnet.microsoft.com/download/dotnet-framework/net48
echo.

echo OPTION 3 - Activation de .NET 3.5 (Windows 10/11)
echo ---------------------------------------------------
echo • Déjà sur votre système
echo • Peut nécessiter activation
echo.

echo Voulez-vous essayer d'activer .NET Framework 3.5 ? (O/N)
set /p ACTIVATE_NET35=

if /i "%ACTIVATE_NET35%"=="O" (
    echo.
    echo Tentative d'activation de .NET Framework 3.5...
    echo Cette opération nécessite une connexion Internet.
    echo.
    
    dism /online /enable-feature /featurename:NetFx3 /all
    
    if %ERRORLEVEL% equ 0 (
        echo.
        echo [SUCCES] .NET Framework 3.5 activé !
        echo Redémarrez votre PC puis essayez build-quick.bat
    ) else (
        echo.
        echo [ECHEC] Impossible d'activer .NET Framework 3.5
        echo Essayez les autres options ci-dessus.
    )
) else (
    echo.
    echo Choisissez une des options ci-dessus et revenez
    echo ensuite pour compiler l'application.
)

echo.
echo ETAPE 4: Après installation
echo ============================
echo 1. Redémarrer votre PC
echo 2. Revenir dans ce dossier
echo 3. Exécuter build-quick.bat
echo 4. L'application devrait compiler !
echo.

echo ETAPE 5: Test rapide après installation
echo ========================================
echo Pour vérifier que l'installation a fonctionné:
echo 1. Ouvrir une nouvelle invite de commande
echo 2. Taper: dir "%WINDIR%\Microsoft.NET\Framework\v*\csc.exe"
echo 3. Vous devriez voir au moins un fichier csc.exe
echo.

echo ========================================
echo  RESUME
echo ========================================
echo.
echo PROBLEME: Compilateur C# manquant
echo SOLUTION: Installer .NET Framework ou Visual Studio
echo.
echo LIENS UTILES:
echo • Visual Studio Community: https://visualstudio.microsoft.com/fr/vs/community/
echo • .NET Framework 4.8: https://dotnet.microsoft.com/download/dotnet-framework/net48
echo.
echo Une fois installé, l'application d'inventaire
echo pourra être compilée pour votre Zebra MC2100 !
echo.

pause
