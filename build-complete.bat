@echo off
REM Script de compilation complete avec toutes les fonctionnalites

echo ========================================
echo  BUILD COMPLET - INVENTAIRE MC2100
echo  Version complete avec Dinar Tunisien
echo ========================================
echo.

REM Aller dans le bon repertoire
cd /d "%~dp0"

REM Rechercher le compilateur (Visual Studio 2022 prioritaire)
set CSC_PATH=""
set COMPILER_FOUND=0

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo Compilateur trouve: Visual Studio 2022 Community
    set COMPILER_FOUND=1
    goto :compiler_ready
)

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo Compilateur trouve: .NET Framework 4.0 (64-bit)
    set COMPILER_FOUND=1
    goto :compiler_ready
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo Compilateur trouve: .NET Framework 4.0
    set COMPILER_FOUND=1
    goto :compiler_ready
)

echo ERREUR: Aucun compilateur C# fonctionnel trouve
echo.
echo SOLUTION: Utiliser "Developer Command Prompt for VS 2022"
echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
echo 2. cd "C:\Users\<USER>\Desktop\Inventaire"
echo 3. Executer ce script
echo.
pause
exit /b 1

:compiler_ready

echo.

REM Creer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation de l'application complete...
echo.

REM Compiler avec tous les fichiers
%CSC_PATH% /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo SOLUTION: Utiliser "Developer Command Prompt for VS 2022"
    echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers ce dossier
    echo 3. Executer ce script
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    REM Afficher la taille du fichier
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo NOUVELLES FONCTIONNALITES:
    echo ==========================
    echo • Devise: Dinar Tunisien (DT)
    echo • Gestion articles complete
    echo • Module inventaire fonctionnel
    echo • Rapports detailles
    echo • Scanner codes-barres integre
    echo • Base de donnees XML
    echo.
    
    echo DEPLOIEMENT SUR MC2100:
    echo =======================
    echo 1. Copier bin\Release\InventoryApp.exe vers le terminal
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Inventaire MC2100 - Complet" "bin\Release\InventoryApp.exe"
    )
    
    echo.
    echo MODULES DISPONIBLES:
    echo ===================
    echo • GESTION ARTICLES: Creation, modification, suppression
    echo • INVENTAIRE: Comptage avec scanner, gestion des ecarts
    echo • RAPPORTS: Stock global, ruptures, stock bas, categories
    echo • DEVISE: Dinar Tunisien avec format 0.000 DT
    echo.
    
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo Compilation terminee avec succes !
echo L'application est prete pour votre Zebra MC2100.
echo.
pause
