@echo off 
REM Script de compilation avec SQLite 
 
echo Compilation avec base SQLite... 
 
csc /target:winexe ^ 
    /out:bin\Release\InventoryApp-SQLite.exe ^ 
    /reference:System.dll ^ 
    /reference:System.Data.dll ^ 
    /reference:System.Drawing.dll ^ 
    /reference:System.Windows.Forms.dll ^ 
    /reference:System.Xml.dll ^ 
    /reference:Microsoft.VisualBasic.dll ^ 
    /reference:lib\System.Data.SQLite.dll ^ 
    /define:USE_SQLITE_DATABASE ^ 
    /nowarn:0168,0219 ^ 
    Program.cs ^ 
    "Properties\AssemblyInfo.cs" ^ 
    "Models\Article.cs" ^ 
    "Models\InventoryItem.cs" ^ 
    "Data\DatabaseHelper-SQLite.cs" ^ 
    "Services\BarcodeService.cs" ^ 
    "Services\InventoryService-SQLite.cs" ^ 
    "Utils\CurrencyHelper.cs" ^ 
    "Forms\MainForm.cs" ^ 
    "Forms\MainForm.Designer.cs" ^ 
    "Forms\ArticleForm.cs" ^ 
    "Forms\ArticleForm.Designer.cs" ^ 
    "Forms\InventoryForm-Simple.cs" ^ 
    "Forms\ReportsForm.cs" ^ 
    "Forms\ReportsForm.Designer.cs" 
 
if %ERRORLEVEL% equ 0 ( 
    echo [SUCCES] Application SQLite compilee ! 
    echo Fichier: bin\Release\InventoryApp-SQLite.exe 
    echo. 
    echo IMPORTANT: Copier aussi lib\System.Data.SQLite.dll 
    echo dans le meme dossier que l'executable ! 
    echo. 
    if exist "bin\Release\InventoryApp-SQLite.exe" ( 
        copy "lib\System.Data.SQLite.dll" "bin\Release\" 
        echo [OK] System.Data.SQLite.dll copie 
        echo. 
        echo Voulez-vous tester l'application ? (O/N) 
        set /p TEST_APP= 
        if /i "%TEST_APP%"=="O" ( 
            start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe" 
        ) 
    ) 
) else ( 
    echo [ERREUR] Compilation echouee 
) 
 
pause 
