using System;
using System.Collections.Generic;
using InventoryApp.Data;
using InventoryApp.Models;

namespace InventoryApp.Services
{
    /// <summary>
    /// Service métier simplifié pour SQLite compatible .NET 4.0
    /// </summary>
    public class InventoryServiceSQLite
    {
        /// <summary>
        /// Constructeur du service d'inventaire SQLite
        /// </summary>
        public InventoryServiceSQLite()
        {
        }

        #region Gestion des articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        public int CreateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (string.IsNullOrEmpty(article.Barcode))
                throw new ArgumentException("Le code-barres est obligatoire");

            if (string.IsNullOrEmpty(article.Name))
                throw new ArgumentException("Le nom de l'article est obligatoire");

            // Vérifier que le code-barres n'existe pas déjà
            if (GetArticleByBarcode(article.Barcode) != null)
                throw new InvalidOperationException("Un article avec ce code-barres existe déjà");

            return DatabaseHelperSQLite.AddArticle(article);
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        public bool UpdateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (article.Id <= 0)
                throw new ArgumentException("ID d'article invalide");

            return DatabaseHelperSQLite.UpdateArticle(article);
        }

        /// <summary>
        /// Supprime un article (désactivation logique)
        /// </summary>
        public bool DeleteArticle(int articleId)
        {
            if (articleId <= 0)
                throw new ArgumentException("ID d'article invalide");

            return DatabaseHelperSQLite.DeleteArticle(articleId);
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        public Article GetArticleByBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return null;

            return DatabaseHelperSQLite.GetArticleByBarcode(barcode);
        }

        /// <summary>
        /// Recherche des articles
        /// </summary>
        public List<Article> SearchArticles(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return GetAllArticles();
            else
                return DatabaseHelperSQLite.SearchArticles(searchTerm);
        }

        /// <summary>
        /// Récupère tous les articles
        /// </summary>
        public List<Article> GetAllArticles()
        {
            return DatabaseHelperSQLite.GetAllArticles();
        }

        #endregion

        #region Gestion des stocks

        /// <summary>
        /// Met à jour le stock d'un article
        /// </summary>
        public bool UpdateStock(string barcode, int newQuantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            article.StockQuantity = newQuantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Ajoute une quantité au stock
        /// </summary>
        public bool AddToStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            article.StockQuantity += quantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Retire une quantité du stock
        /// </summary>
        public bool RemoveFromStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            if (article.StockQuantity < quantity)
                return false; // Stock insuffisant

            article.StockQuantity -= quantity;
            return UpdateArticle(article);
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        public List<Article> GetOutOfStockArticles()
        {
            return DatabaseHelperSQLite.GetOutOfStockArticles();
        }

        /// <summary>
        /// Récupère les articles en dessous du seuil minimum
        /// </summary>
        public List<Article> GetLowStockArticles()
        {
            return DatabaseHelperSQLite.GetLowStockArticles();
        }

        #endregion

        #region Statistiques

        /// <summary>
        /// Récupère le nombre total d'articles
        /// </summary>
        public int GetTotalArticlesCount()
        {
            return DatabaseHelperSQLite.GetTotalArticlesCount();
        }

        /// <summary>
        /// Récupère la valeur totale du stock
        /// </summary>
        public decimal GetTotalStockValue()
        {
            return DatabaseHelperSQLite.GetTotalStockValue();
        }

        #endregion

        #region Gestion de l'inventaire (simplifiée)

        /// <summary>
        /// Démarre une nouvelle session d'inventaire
        /// </summary>
        public string StartInventorySession(string sessionName = null)
        {
            if (string.IsNullOrEmpty(sessionName))
            {
                sessionName = "INV_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
            }

            return sessionName;
        }

        /// <summary>
        /// Ajoute un élément d'inventaire (version simplifiée)
        /// </summary>
        public bool AddInventoryItem(InventoryItem inventoryItem)
        {
            if (inventoryItem == null)
                throw new ArgumentNullException("inventoryItem");

            // Version simplifiée - retourne toujours true
            // Dans une version complète, on sauvegarderait en base
            return true;
        }

        /// <summary>
        /// Finalise une session d'inventaire (version simplifiée)
        /// </summary>
        public bool FinalizeInventorySession(string sessionId, bool updateStock = false)
        {
            if (string.IsNullOrEmpty(sessionId))
                return false;

            // Version simplifiée - retourne toujours true
            // Dans une version complète, on finaliserait l'inventaire
            return true;
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        public bool TestDatabaseConnection()
        {
            return DatabaseHelperSQLite.TestConnection();
        }

        /// <summary>
        /// Obtient des informations sur la base de données
        /// </summary>
        public string GetDatabaseInfo()
        {
            return DatabaseHelperSQLite.GetDatabaseInfo();
        }

        /// <summary>
        /// Initialise la base de données
        /// </summary>
        public void InitializeDatabase()
        {
            DatabaseHelperSQLite.InitializeDatabase();
        }

        #endregion
    }
}
