@echo off
REM Verification de tous les fichiers de l'application de synchronisation

echo ========================================
echo  VERIFICATION FICHIERS SYNC APP
echo ========================================
echo.

echo Verification des fichiers sources...
echo.

set MISSING=0
set TOTAL=0

REM Fichiers principaux
set /a TOTAL+=1
if exist "SyncApp\Program.cs" (
    echo [OK] SyncApp\Program.cs
) else (
    echo [MANQUE] SyncApp\Program.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Properties\AssemblyInfo.cs" (
    echo [OK] SyncApp\Properties\AssemblyInfo.cs
) else (
    echo [MANQUE] SyncApp\Properties\AssemblyInfo.cs
    set /a MISSING+=1
)

REM Modèles
set /a TOTAL+=1
if exist "SyncApp\Models\SyncItem.cs" (
    echo [OK] SyncApp\Models\SyncItem.cs
) else (
    echo [MANQUE] SyncApp\Models\SyncItem.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Models\SyncResult.cs" (
    echo [OK] SyncApp\Models\SyncResult.cs
) else (
    echo [MANQUE] SyncApp\Models\SyncResult.cs
    set /a MISSING+=1
)

REM Services
set /a TOTAL+=1
if exist "SyncApp\Services\SyncService.cs" (
    echo [OK] SyncApp\Services\SyncService.cs
) else (
    echo [MANQUE] SyncApp\Services\SyncService.cs
    set /a MISSING+=1
)

REM Formulaires
set /a TOTAL+=1
if exist "SyncApp\Forms\SyncMainForm.cs" (
    echo [OK] SyncApp\Forms\SyncMainForm.cs
) else (
    echo [MANQUE] SyncApp\Forms\SyncMainForm.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Forms\SyncMainForm.Designer.cs" (
    echo [OK] SyncApp\Forms\SyncMainForm.Designer.cs
) else (
    echo [MANQUE] SyncApp\Forms\SyncMainForm.Designer.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Forms\SyncResultsForm.cs" (
    echo [OK] SyncApp\Forms\SyncResultsForm.cs
) else (
    echo [MANQUE] SyncApp\Forms\SyncResultsForm.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Forms\SyncResultsForm.Designer.cs" (
    echo [OK] SyncApp\Forms\SyncResultsForm.Designer.cs
) else (
    echo [MANQUE] SyncApp\Forms\SyncResultsForm.Designer.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Forms\ConflictResolutionForm.cs" (
    echo [OK] SyncApp\Forms\ConflictResolutionForm.cs
) else (
    echo [MANQUE] SyncApp\Forms\ConflictResolutionForm.cs
    set /a MISSING+=1
)

set /a TOTAL+=1
if exist "SyncApp\Forms\ConflictResolutionForm.Designer.cs" (
    echo [OK] SyncApp\Forms\ConflictResolutionForm.Designer.cs
) else (
    echo [MANQUE] SyncApp\Forms\ConflictResolutionForm.Designer.cs
    set /a MISSING+=1
)

REM Dépendances
set /a TOTAL+=1
if exist "lib\System.Data.SQLite.dll" (
    echo [OK] lib\System.Data.SQLite.dll
    for %%I in ("lib\System.Data.SQLite.dll") do echo      Taille: %%~zI octets
) else (
    echo [MANQUE] lib\System.Data.SQLite.dll
    set /a MISSING+=1
)

echo.
echo ========================================
echo  RESULTAT DE LA VERIFICATION
echo ========================================
echo.

if %MISSING% equ 0 (
    echo [SUCCES] Tous les fichiers sont presents !
    echo Total: %TOTAL% fichiers
    echo.
    echo STRUCTURE COMPLETE:
    echo ==================
    echo • Programme principal et AssemblyInfo
    echo • 2 modeles de donnees (SyncItem, SyncResult)
    echo • 1 service de synchronisation
    echo • 3 formulaires avec leurs Designers
    echo • Dependance SQLite
    echo.
    echo L'application de synchronisation est prete
    echo pour la compilation !
    echo.
    echo PROCHAINES ETAPES:
    echo ==================
    echo 1. Executer: build-sync-app.bat
    echo 2. Tester l'application generee
    echo 3. Deployer avec les fichiers de donnees
    echo.
    
) else (
    echo [ERREUR] %MISSING% fichiers manquants sur %TOTAL%
    echo.
    echo ACTIONS REQUISES:
    echo ================
    echo • Creer les fichiers manquants
    echo • Verifier la structure des dossiers
    echo • Installer SQLite si necessaire
    echo.
    echo Pour SQLite:
    echo 1. https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Copier System.Data.SQLite.dll dans lib\
    echo.
)

echo FONCTIONNALITES DE L'APPLICATION:
echo =================================
echo • Interface graphique moderne
echo • Synchronisation XML ↔ SQLite
echo • Detection automatique des fichiers
echo • 3 modes de synchronisation:
echo   - Terminal → Laptop
echo   - Laptop → Terminal  
echo   - Bidirectionnelle avec conflits
echo • Affichage des resultats detailles
echo • Export CSV des donnees
echo • Resolution manuelle des conflits
echo • Journal de synchronisation
echo • Support Dinar Tunisien (DT)
echo.

echo COMPATIBILITE:
echo ==============
echo • Terminal Zebra MC2100 (fichiers XML)
echo • Laptop Desktop (base SQLite)
echo • Windows 7/8/10/11
echo • .NET Framework 4.0+
echo.

pause
