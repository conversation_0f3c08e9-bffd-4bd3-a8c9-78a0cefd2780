@echo off
REM Compilation fichier par fichier pour identifier le probleme

echo ========================================
echo  COMPILATION FICHIER PAR FICHIER
echo ========================================
echo.

REM Test de la commande csc
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande csc non disponible
    pause
    exit /b 1
)

echo Test de compilation de chaque fichier...
echo.

REM Créer le dossier de sortie
if not exist "bin" mkdir "bin"
if not exist "bin\Test" mkdir "bin\Test"

echo 1. Test Program.cs...
csc /target:library /out:bin\Test\test1.dll Program.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Program.cs
    goto :end
) else (
    echo [OK] Program.cs
)

echo 2. Test Properties\AssemblyInfo.cs...
csc /target:library /out:bin\Test\test2.dll "Properties\AssemblyInfo.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Properties\AssemblyInfo.cs
    goto :end
) else (
    echo [OK] Properties\AssemblyInfo.cs
)

echo 3. Test Models\Article.cs...
csc /target:library /out:bin\Test\test3.dll "Models\Article.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Models\Article.cs
    goto :end
) else (
    echo [OK] Models\Article.cs
)

echo 4. Test Models\InventoryItem.cs...
csc /target:library /out:bin\Test\test4.dll "Models\InventoryItem.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Models\InventoryItem.cs
    goto :end
) else (
    echo [OK] Models\InventoryItem.cs
)

echo 5. Test Data\DatabaseHelper-Simple.cs...
csc /target:library /out:bin\Test\test5.dll /reference:System.dll /reference:System.Xml.dll "Data\DatabaseHelper-Simple.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Data\DatabaseHelper-Simple.cs
    goto :end
) else (
    echo [OK] Data\DatabaseHelper-Simple.cs
)

echo 6. Test Services\BarcodeService.cs...
csc /target:library /out:bin\Test\test6.dll /reference:System.dll "Services\BarcodeService.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Services\BarcodeService.cs
    goto :end
) else (
    echo [OK] Services\BarcodeService.cs
)

echo 7. Test Services\InventoryService-Simple.cs...
csc /target:library /out:bin\Test\test7.dll /reference:System.dll "Services\InventoryService-Simple.cs" "Models\Article.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Services\InventoryService-Simple.cs
    goto :end
) else (
    echo [OK] Services\InventoryService-Simple.cs
)

echo 8. Test Utils\CurrencyHelper.cs...
csc /target:library /out:bin\Test\test8.dll /reference:System.dll "Utils\CurrencyHelper.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Utils\CurrencyHelper.cs
    goto :end
) else (
    echo [OK] Utils\CurrencyHelper.cs
)

echo 9. Test Forms\MainForm.cs...
csc /target:library /out:bin\Test\test9.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\MainForm.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\MainForm.cs
    goto :end
) else (
    echo [OK] Forms\MainForm.cs
)

echo 10. Test Forms\MainForm.Designer.cs...
csc /target:library /out:bin\Test\test10.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\MainForm.Designer.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\MainForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\MainForm.Designer.cs
)

echo 11. Test Forms\ArticleForm.cs...
csc /target:library /out:bin\Test\test11.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\ArticleForm.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ArticleForm.cs
    goto :end
) else (
    echo [OK] Forms\ArticleForm.cs
)

echo 12. Test Forms\ArticleForm.Designer.cs...
csc /target:library /out:bin\Test\test12.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\ArticleForm.Designer.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ArticleForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\ArticleForm.Designer.cs
)

echo 13. Test Forms\InventoryForm-Simple.cs...
csc /target:library /out:bin\Test\test13.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\InventoryForm-Simple.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\InventoryForm-Simple.cs
    goto :end
) else (
    echo [OK] Forms\InventoryForm-Simple.cs
)

echo 14. Test Forms\ReportsForm.cs...
csc /target:library /out:bin\Test\test14.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\ReportsForm.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ReportsForm.cs
    goto :end
) else (
    echo [OK] Forms\ReportsForm.cs
)

echo 15. Test Forms\ReportsForm.Designer.cs...
csc /target:library /out:bin\Test\test15.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll "Forms\ReportsForm.Designer.cs"
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ReportsForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\ReportsForm.Designer.cs
)

echo.
echo ========================================
echo  TOUS LES FICHIERS SONT OK !
echo ========================================
echo.
echo Tentative de compilation complete...

csc /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% equ 0 (
    echo.
    echo [SUCCES] Compilation complete reussie !
    echo Fichier: bin\Release\InventoryApp.exe
) else (
    echo.
    echo [ERREUR] Compilation complete echouee
)

:end
REM Nettoyer les fichiers de test
rmdir /s /q "bin\Test" >nul 2>&1

echo.
pause
