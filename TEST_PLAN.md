# Plan de Test - Application Inventaire Zebra MC2100

## Vue d'ensemble

Ce document décrit les tests à effectuer pour valider le bon fonctionnement de l'application d'inventaire sur le terminal Zebra MC2100.

## Environnement de test

- **Terminal**: Zebra MC2100
- **OS**: Windows CE 6.0
- **Framework**: .NET Compact Framework 3.5
- **Base de données**: SQL Server Compact Edition

## Tests préliminaires

### T001 - Installation et démarrage
**Objectif**: Vérifier que l'application s'installe et démarre correctement

**Étapes**:
1. Copier InventoryApp.exe sur le terminal MC2100
2. Lancer l'application
3. Vérifier que l'interface principale s'affiche
4. Contrôler que la base de données est créée automatiquement

**Résultat attendu**: 
- Application démarre sans erreur
- Interface responsive et lisible
- Fichier InventoryDB.sdf créé dans le dossier application

### T002 - Configuration initiale
**Objectif**: Vérifier la configuration par défaut

**Étapes**:
1. Vérifier les statistiques initiales (0 articles, 0€ valeur)
2. Contrôler que le scanner est activé
3. Tester la navigation entre les menus

**Résultat attendu**:
- Statistiques à zéro
- Message "SCANNER ACTIF" affiché
- Tous les boutons fonctionnels

## Tests fonctionnels

### T003 - Gestion des articles
**Objectif**: Tester la création, modification et suppression d'articles

**Étapes**:
1. Ouvrir "Gestion Articles"
2. Créer un nouvel article avec les données suivantes:
   - Code-barres: TEST001
   - Nom: Article Test 1
   - Catégorie: Test
   - Prix: 10.50€
   - Stock: 100
   - Stock minimum: 10
3. Sauvegarder l'article
4. Modifier le stock à 50
5. Supprimer l'article

**Résultat attendu**:
- Article créé avec succès
- Modification sauvegardée
- Suppression confirmée
- Messages de confirmation affichés

### T004 - Scanner de codes-barres
**Objectif**: Tester la lecture de codes-barres

**Prérequis**: Avoir des codes-barres de test disponibles

**Étapes**:
1. Depuis l'écran principal, scanner un code-barres
2. Vérifier que le code apparaît dans "Dernier scan"
3. Depuis la gestion d'articles, scanner un code-barres dans le champ "Code-barres"
4. Scanner un code-barres d'un article existant

**Résultat attendu**:
- Code-barres affiché correctement
- Champ rempli automatiquement
- Article existant trouvé et affiché

### T005 - Inventaire
**Objectif**: Tester le processus d'inventaire

**Prérequis**: Avoir au moins 3 articles créés

**Étapes**:
1. Ouvrir "Inventaire"
2. Démarrer une nouvelle session d'inventaire
3. Scanner les codes-barres des articles
4. Saisir les quantités comptées
5. Valider l'inventaire
6. Consulter les écarts

**Résultat attendu**:
- Session créée
- Articles scannés et comptés
- Écarts calculés correctement
- Validation enregistrée

### T006 - Recherche d'articles
**Objectif**: Tester la fonction de recherche

**Prérequis**: Avoir plusieurs articles avec noms différents

**Étapes**:
1. Dans "Gestion Articles", utiliser la recherche
2. Rechercher par nom partiel
3. Rechercher par code-barres
4. Rechercher avec terme inexistant

**Résultat attendu**:
- Résultats filtrés correctement
- Recherche insensible à la casse
- Aucun résultat pour terme inexistant

### T007 - Rapports
**Objectif**: Tester la génération de rapports

**Étapes**:
1. Ouvrir "Rapports"
2. Générer un rapport de stock
3. Générer un rapport d'articles en rupture
4. Générer un rapport d'inventaire

**Résultat attendu**:
- Rapports générés sans erreur
- Données cohérentes
- Format lisible

## Tests de performance

### T008 - Performance avec volume de données
**Objectif**: Tester les performances avec un grand nombre d'articles

**Étapes**:
1. Créer 100 articles de test
2. Mesurer le temps de chargement de la liste
3. Tester la recherche avec 100 articles
4. Effectuer un inventaire complet

**Résultat attendu**:
- Chargement < 5 secondes
- Recherche < 2 secondes
- Interface reste responsive

### T009 - Utilisation mémoire
**Objectif**: Vérifier que l'application n'utilise pas trop de mémoire

**Étapes**:
1. Lancer l'application
2. Utiliser toutes les fonctionnalités
3. Laisser l'application ouverte 1 heure
4. Vérifier la mémoire disponible

**Résultat attendu**:
- Pas de fuite mémoire
- Mémoire stable après utilisation
- Terminal reste responsive

## Tests de robustesse

### T010 - Gestion des erreurs
**Objectif**: Tester la gestion des cas d'erreur

**Étapes**:
1. Tenter de créer un article avec code-barres existant
2. Saisir des valeurs invalides (prix négatif, etc.)
3. Supprimer le fichier de base de données pendant l'utilisation
4. Scanner un code-barres invalide

**Résultat attendu**:
- Messages d'erreur appropriés
- Application ne plante pas
- Récupération gracieuse

### T011 - Interruptions
**Objectif**: Tester la résistance aux interruptions

**Étapes**:
1. Interrompre l'application pendant une saisie
2. Redémarrer le terminal pendant l'utilisation
3. Retirer/remettre la batterie
4. Relancer l'application après interruption

**Résultat attendu**:
- Données sauvegardées correctement
- Pas de corruption de base de données
- Redémarrage propre

## Tests d'intégration

### T012 - Synchronisation des données
**Objectif**: Tester la cohérence des données

**Étapes**:
1. Créer des articles
2. Effectuer un inventaire
3. Modifier les stocks
4. Vérifier la cohérence entre les écrans

**Résultat attendu**:
- Données synchronisées
- Statistiques mises à jour
- Pas d'incohérences

### T013 - Sauvegarde et restauration
**Objectif**: Tester la sauvegarde des données

**Étapes**:
1. Créer plusieurs articles
2. Copier le fichier InventoryDB.sdf
3. Supprimer des articles
4. Restaurer la sauvegarde
5. Vérifier les données

**Résultat attendu**:
- Sauvegarde complète
- Restauration réussie
- Données intègres

## Critères d'acceptation

### Critères obligatoires
- ✅ Tous les tests T001 à T007 passent
- ✅ Aucun plantage pendant les tests
- ✅ Interface utilisable sur écran MC2100
- ✅ Scanner fonctionne correctement
- ✅ Base de données créée et accessible

### Critères optionnels
- ✅ Tests de performance T008-T009 satisfaisants
- ✅ Gestion d'erreurs robuste (T010-T011)
- ✅ Intégration complète (T012-T013)

## Rapport de test

### Modèle de rapport
```
Test: [Numéro et nom]
Date: [Date d'exécution]
Testeur: [Nom du testeur]
Résultat: [PASS/FAIL/SKIP]
Commentaires: [Observations]
Problèmes: [Liste des problèmes rencontrés]
```

### Suivi des problèmes
- **Critique**: Empêche l'utilisation de l'application
- **Majeur**: Fonctionnalité importante non disponible
- **Mineur**: Problème cosmétique ou de convivialité
- **Suggestion**: Amélioration possible

## Validation finale

L'application est considérée comme prête pour le déploiement si :
1. Tous les tests critiques (T001-T007) passent
2. Aucun problème critique ou majeur non résolu
3. Performance acceptable sur le matériel cible
4. Documentation complète et à jour

---

**Note**: Ce plan de test doit être exécuté sur le matériel cible (Zebra MC2100) dans les conditions d'utilisation réelles.
