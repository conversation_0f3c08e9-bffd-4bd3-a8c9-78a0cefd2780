@echo off
REM Verification de l'emplacement et des fichiers

echo ========================================
echo  VERIFICATION EMPLACEMENT ET FICHIERS
echo ========================================
echo.

echo Repertoire actuel:
cd
echo.

echo Contenu du repertoire actuel:
dir /b
echo.

echo Verification des fichiers principaux:
if exist "Program.cs" (
    echo [OK] Program.cs trouve
) else (
    echo [MANQUE] Program.cs
)

if exist "Properties" (
    echo [OK] Dossier Properties existe
    if exist "Properties\AssemblyInfo.cs" (
        echo [OK] Properties\AssemblyInfo.cs trouve
    ) else (
        echo [MANQUE] Properties\AssemblyInfo.cs
    )
) else (
    echo [MANQUE] Dossier Properties
)

if exist "Models" (
    echo [OK] Dossier Models existe
    dir /b Models
) else (
    echo [MANQUE] Dossier Models
)

if exist "Data" (
    echo [OK] Dossier Data existe
    dir /b Data
) else (
    echo [MANQUE] Dossier Data
)

if exist "Services" (
    echo [OK] Dossier Services existe
    dir /b Services
) else (
    echo [MANQUE] Dossier Services
)

if exist "Utils" (
    echo [OK] Dossier Utils existe
    dir /b Utils
) else (
    echo [MANQUE] Dossier Utils
)

if exist "Forms" (
    echo [OK] Dossier Forms existe
    dir /b Forms
) else (
    echo [MANQUE] Dossier Forms
)

echo.
echo ========================================
echo  SOLUTION
echo ========================================
echo.

if not exist "Program.cs" (
    echo PROBLEME: Vous n'etes pas dans le bon repertoire !
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers: cd "C:\Users\<USER>\Desktop\Inventaire"
    echo 3. Verifier avec: dir
    echo 4. Vous devriez voir Program.cs et les dossiers
    echo.
) else (
    echo Vous etes dans le bon repertoire !
    echo Tous les fichiers semblent presents.
    echo.
)

pause
