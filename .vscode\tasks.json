{"version": "2.0.0", "tasks": [{"label": "build-debug", "type": "shell", "command": "msbuild", "args": ["InventoryApp.sln", "/p:Configuration=Debug", "/p:Platform=AnyCPU", "/t:Build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "build-release", "type": "shell", "command": "msbuild", "args": ["InventoryApp.sln", "/p:Configuration=Release", "/p:Platform=AnyCPU", "/t:Build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "clean", "type": "shell", "command": "msbuild", "args": ["InventoryApp.sln", "/t:Clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "rebuild", "type": "shell", "command": "msbuild", "args": ["InventoryApp.sln", "/t:Rebuild"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "build-package", "type": "shell", "command": "build.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": ["clean"]}]}