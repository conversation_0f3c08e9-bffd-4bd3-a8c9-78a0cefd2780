@echo off
REM Compilation minimale sans caracteres speciaux

echo Build minimal en cours...

csc /target:winexe /out:InventoryApp.exe /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /define:USE_XML_DATABASE Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-Simple.cs Services\BarcodeService.cs Services\InventoryService-Simple.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo SUCCES ! Fichier genere: InventoryApp.exe
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" start InventoryApp.exe
) else (
    echo ERREUR de compilation
    echo Code: %ERRORLEVEL%
)

pause
