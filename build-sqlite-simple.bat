@echo off
REM Script de compilation SQLite avec version simplifiee compatible .NET 4.0

echo ========================================
echo  BUILD SQLITE SIMPLE - INVENTAIRE MC2100
echo  Version SQLite compatible .NET 4.0
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo.
    echo TELECHARGEMENT REQUIS:
    echo 1. Aller sur: https://system.data.sqlite.org/downloads.html
    echo 2. Telecharger: "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
    echo 3. Extraire le ZIP
    echo 4. Copier System.Data.SQLite.dll dans le dossier lib\
    echo.
    pause
    exit /b 1
)

echo [OK] Compilateur et SQLite disponibles
for %%I in ("lib\System.Data.SQLite.dll") do echo SQLite DLL: %%~zI octets
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation SQLite simplifiee en cours...
echo.

REM Compilation avec version simplifiée compatible .NET 4.0
csc /target:winexe /out:bin\Release\InventoryApp-SQLite.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite-Simple.cs Services\BarcodeService.cs Services\InventoryService-SQLite.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo.
    echo FICHIERS UTILISES:
    echo • DatabaseHelper-SQLite-Simple.cs (compatible .NET 4.0)
    echo • InventoryService-SQLite.cs
    echo • Tous les formulaires avec Dinar Tunisien
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION SQLITE REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp-SQLite.exe" (
    echo Fichier genere: bin\Release\InventoryApp-SQLite.exe
    
    for %%I in ("bin\Release\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets
    
    REM Copier la DLL SQLite nécessaire
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie dans bin\Release\
    
    echo.
    echo APPLICATION SQLITE COMPLETE AVEC:
    echo =================================
    echo • Base de donnees SQLite locale (InventoryDB.sqlite)
    echo • Compatible .NET Framework 4.0
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete (CRUD)
    echo • Module inventaire simplifie
    echo • Rapports detailles (4 types)
    echo • Scanner codes-barres integre
    echo • Performance optimisee pour laptop
    echo • Sauvegarde simple (fichier .sqlite)
    echo.
    
    echo FONCTIONNALITES SQLITE:
    echo =======================
    echo • Creation automatique de la base
    echo • Transactions securisees
    echo • Recherche rapide par index
    echo • Requetes SQL completes
    echo • Pas de serveur requis
    echo • Fichier portable
    echo.
    
    echo DEPLOIEMENT:
    echo ============
    echo LAPTOP (recommande):
    echo 1. Copier bin\Release\InventoryApp-SQLite.exe
    echo 2. Copier bin\Release\System.Data.SQLite.dll
    echo 3. Lancer l'application
    echo 4. Le fichier InventoryDB.sqlite se cree automatiquement
    echo.
    
    echo STRUCTURE DE LA BASE:
    echo ====================
    echo • Table Articles: tous les produits avec prix DT
    echo • Index sur codes-barres pour performance
    echo • Contraintes d'integrite
    echo • Champs optimises pour MC2100
    echo.
    
    echo Voulez-vous tester l'application SQLite maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement de l'application SQLite...
        start "Inventaire SQLite - Version Complete" "bin\Release\InventoryApp-SQLite.exe"
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo ===========================
        echo 1. Creation d'articles avec prix en Dinar Tunisien
        echo 2. Recherche rapide par nom ou code-barres
        echo 3. Generation des 4 types de rapports
        echo 4. Verification du fichier InventoryDB.sqlite
        echo.
        echo EMPLACEMENT DE LA BASE:
        echo %CD%\bin\Release\InventoryDB.sqlite
        echo.
        echo OUTILS POUR CONSULTER LA BASE:
        echo • DB Browser for SQLite (gratuit)
        echo • SQLite Expert
        echo • SQLiteStudio
        echo • Ou tout autre outil SQLite
        echo.
        echo La base se cree automatiquement au premier lancement.
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  APPLICATION SQLITE PRETE !
echo ========================================
echo.
echo Votre application d'inventaire avec SQLite est maintenant
echo complete et optimisee pour votre laptop !
echo.
echo AVANTAGES DE CETTE VERSION:
echo ===========================
echo • Base de donnees professionnelle SQLite
echo • Performance locale ultra-rapide
echo • Sauvegarde simple (copie fichier .sqlite)
echo • Compatible .NET Framework 4.0
echo • Requetes SQL completes
echo • Devise tunisienne integree
echo • Aucun serveur requis
echo • Portable entre machines
echo.
echo PROCHAINES ETAPES:
echo ==================
echo 1. Tester l'application sur votre laptop
echo 2. Creer quelques articles de test
echo 3. Generer des rapports
echo 4. Sauvegarder le fichier .sqlite
echo 5. Deployer sur d'autres machines si besoin
echo.
echo Compilation terminee avec succes !
pause
