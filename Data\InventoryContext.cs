using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlServerCe;
using InventoryApp.Models;

namespace InventoryApp.Data
{
    /// <summary>
    /// Contexte de données pour l'accès aux entités de l'inventaire
    /// </summary>
    public class InventoryContext
    {
        #region Articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        /// <param name="article">Article à ajouter</param>
        /// <returns>ID de l'article créé</returns>
        public int AddArticle(Article article)
        {
            string sql = @"
                INSERT INTO Articles (Barcode, Name, Description, Category, UnitPrice, 
                                    StockQuantity, MinimumStock, Unit, Location, 
                                    CreatedDate, LastModified, IsActive)
                VALUES (@Barcode, @Name, @Description, @Category, @UnitPrice, 
                        @StockQuantity, @MinimumStock, @Unit, @Location, 
                        @CreatedDate, @LastModified, @IsActive)";

            var parameters = new[]
            {
                new SqlCeParameter("@Barcode", article.Barcode),
                new SqlCeParameter("@Name", article.Name),
                new SqlCeParameter("@Description", article.Description ?? ""),
                new SqlCeParameter("@Category", article.Category ?? ""),
                new SqlCeParameter("@UnitPrice", article.UnitPrice),
                new SqlCeParameter("@StockQuantity", article.StockQuantity),
                new SqlCeParameter("@MinimumStock", article.MinimumStock),
                new SqlCeParameter("@Unit", article.Unit),
                new SqlCeParameter("@Location", article.Location ?? ""),
                new SqlCeParameter("@CreatedDate", article.CreatedDate),
                new SqlCeParameter("@LastModified", article.LastModified),
                new SqlCeParameter("@IsActive", article.IsActive)
            };

            DatabaseHelper.ExecuteNonQuery(sql, parameters);

            // Récupérer l'ID généré
            string getIdSql = "SELECT @@IDENTITY";
            object result = DatabaseHelper.ExecuteScalar(getIdSql);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        /// <param name="article">Article à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateArticle(Article article)
        {
            string sql = @"
                UPDATE Articles 
                SET Name = @Name, Description = @Description, Category = @Category,
                    UnitPrice = @UnitPrice, StockQuantity = @StockQuantity,
                    MinimumStock = @MinimumStock, Unit = @Unit, Location = @Location,
                    LastModified = @LastModified, IsActive = @IsActive
                WHERE Id = @Id";

            var parameters = new[]
            {
                new SqlCeParameter("@Id", article.Id),
                new SqlCeParameter("@Name", article.Name),
                new SqlCeParameter("@Description", article.Description ?? ""),
                new SqlCeParameter("@Category", article.Category ?? ""),
                new SqlCeParameter("@UnitPrice", article.UnitPrice),
                new SqlCeParameter("@StockQuantity", article.StockQuantity),
                new SqlCeParameter("@MinimumStock", article.MinimumStock),
                new SqlCeParameter("@Unit", article.Unit),
                new SqlCeParameter("@Location", article.Location ?? ""),
                new SqlCeParameter("@LastModified", DateTime.Now),
                new SqlCeParameter("@IsActive", article.IsActive)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, parameters);
            return rowsAffected > 0;
        }

        /// <summary>
        /// Supprime un article (suppression logique)
        /// </summary>
        /// <param name="articleId">ID de l'article</param>
        /// <returns>True si la suppression a réussi</returns>
        public bool DeleteArticle(int articleId)
        {
            string sql = "UPDATE Articles SET IsActive = 0, LastModified = @LastModified WHERE Id = @Id";
            var parameters = new[]
            {
                new SqlCeParameter("@Id", articleId),
                new SqlCeParameter("@LastModified", DateTime.Now)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, parameters);
            return rowsAffected > 0;
        }

        /// <summary>
        /// Récupère un article par son ID
        /// </summary>
        /// <param name="articleId">ID de l'article</param>
        /// <returns>Article ou null si non trouvé</returns>
        public Article GetArticleById(int articleId)
        {
            string sql = "SELECT * FROM Articles WHERE Id = @Id AND IsActive = 1";
            var parameters = new[] { new SqlCeParameter("@Id", articleId) };

            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql, parameters);
            if (dataTable.Rows.Count > 0)
            {
                return MapDataRowToArticle(dataTable.Rows[0]);
            }
            return null;
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        /// <param name="barcode">Code-barres</param>
        /// <returns>Article ou null si non trouvé</returns>
        public Article GetArticleByBarcode(string barcode)
        {
            string sql = "SELECT * FROM Articles WHERE Barcode = @Barcode AND IsActive = 1";
            var parameters = new[] { new SqlCeParameter("@Barcode", barcode) };

            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql, parameters);
            if (dataTable.Rows.Count > 0)
            {
                return MapDataRowToArticle(dataTable.Rows[0]);
            }
            return null;
        }

        /// <summary>
        /// Récupère tous les articles actifs
        /// </summary>
        /// <returns>Liste des articles</returns>
        public List<Article> GetAllArticles()
        {
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 ORDER BY Name";
            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql);

            List<Article> articles = new List<Article>();
            foreach (DataRow row in dataTable.Rows)
            {
                articles.Add(MapDataRowToArticle(row));
            }
            return articles;
        }

        /// <summary>
        /// Recherche des articles par nom ou code-barres
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des articles correspondants</returns>
        public List<Article> SearchArticles(string searchTerm)
        {
            string sql = @"
                SELECT * FROM Articles 
                WHERE IsActive = 1 AND (
                    Name LIKE @SearchTerm OR 
                    Barcode LIKE @SearchTerm OR 
                    Description LIKE @SearchTerm
                )
                ORDER BY Name";

            var parameters = new[] { new SqlCeParameter("@SearchTerm", "%" + searchTerm + "%") };
            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql, parameters);

            List<Article> articles = new List<Article>();
            foreach (DataRow row in dataTable.Rows)
            {
                articles.Add(MapDataRowToArticle(row));
            }
            return articles;
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        /// <returns>Liste des articles en rupture</returns>
        public List<Article> GetOutOfStockArticles()
        {
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 AND StockQuantity <= 0 ORDER BY Name";
            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql);

            List<Article> articles = new List<Article>();
            foreach (DataRow row in dataTable.Rows)
            {
                articles.Add(MapDataRowToArticle(row));
            }
            return articles;
        }

        /// <summary>
        /// Récupère les articles en dessous du seuil minimum
        /// </summary>
        /// <returns>Liste des articles en dessous du seuil</returns>
        public List<Article> GetLowStockArticles()
        {
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 AND StockQuantity <= MinimumStock ORDER BY Name";
            DataTable dataTable = DatabaseHelper.ExecuteQuery(sql);

            List<Article> articles = new List<Article>();
            foreach (DataRow row in dataTable.Rows)
            {
                articles.Add(MapDataRowToArticle(row));
            }
            return articles;
        }

        /// <summary>
        /// Met à jour le stock d'un article
        /// </summary>
        /// <param name="articleId">ID de l'article</param>
        /// <param name="newQuantity">Nouvelle quantité</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateArticleStock(int articleId, int newQuantity)
        {
            string sql = "UPDATE Articles SET StockQuantity = @Quantity, LastModified = @LastModified WHERE Id = @Id";
            var parameters = new[]
            {
                new SqlCeParameter("@Id", articleId),
                new SqlCeParameter("@Quantity", newQuantity),
                new SqlCeParameter("@LastModified", DateTime.Now)
            };

            int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, parameters);
            return rowsAffected > 0;
        }

        /// <summary>
        /// Mappe une ligne de données vers un objet Article
        /// </summary>
        /// <param name="row">Ligne de données</param>
        /// <returns>Article</returns>
        private Article MapDataRowToArticle(DataRow row)
        {
            return new Article
            {
                Id = Convert.ToInt32(row["Id"]),
                Barcode = row["Barcode"].ToString(),
                Name = row["Name"].ToString(),
                Description = row["Description"].ToString(),
                Category = row["Category"].ToString(),
                UnitPrice = Convert.ToDecimal(row["UnitPrice"]),
                StockQuantity = Convert.ToInt32(row["StockQuantity"]),
                MinimumStock = Convert.ToInt32(row["MinimumStock"]),
                Unit = row["Unit"].ToString(),
                Location = row["Location"].ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastModified = Convert.ToDateTime(row["LastModified"]),
                IsActive = Convert.ToBoolean(row["IsActive"])
            };
        }

        #endregion
    }
}
