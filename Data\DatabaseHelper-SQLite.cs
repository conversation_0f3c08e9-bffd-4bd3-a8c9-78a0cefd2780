using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using InventoryApp.Models;

namespace InventoryApp.Data
{
    /// <summary>
    /// Helper pour la gestion de la base de données SQLite
    /// </summary>
    public static class DatabaseHelperSQLite
    {
        /// <summary>
        /// Nom du fichier de base de données
        /// </summary>
        private static readonly string DatabaseFileName = "InventoryDB.sqlite";
        
        /// <summary>
        /// Chaîne de connexion SQLite
        /// </summary>
        private static string ConnectionString
        {
            get
            {
                string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                return string.Format("Data Source={0};Version=3;", dbPath);
            }
        }

        /// <summary>
        /// Obtient le chemin de l'application
        /// </summary>
        private static string GetApplicationPath()
        {
            return Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
        }

        /// <summary>
        /// Initialise la base de données SQLite
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                
                // Créer le fichier de base de données s'il n'existe pas
                if (!File.Exists(dbPath))
                {
                    SQLiteConnection.CreateFile(dbPath);
                }

                using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    CreateTables(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Erreur lors de l'initialisation de la base de données SQLite: " + ex.Message);
            }
        }

        /// <summary>
        /// Crée les tables de la base de données
        /// </summary>
        private static void CreateTables(SQLiteConnection connection)
        {
            // Table Articles
            string createArticlesTable = @"
                CREATE TABLE IF NOT EXISTS Articles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Barcode TEXT NOT NULL UNIQUE,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    Category TEXT,
                    UnitPrice DECIMAL(10,3) NOT NULL DEFAULT 0,
                    StockQuantity INTEGER NOT NULL DEFAULT 0,
                    MinimumStock INTEGER NOT NULL DEFAULT 0,
                    Unit TEXT DEFAULT 'pièce',
                    Location TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastModified DATETIME DEFAULT CURRENT_TIMESTAMP,
                    IsActive BOOLEAN DEFAULT 1
                )";

            // Table InventoryItems
            string createInventoryTable = @"
                CREATE TABLE IF NOT EXISTS InventoryItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ArticleId INTEGER NOT NULL,
                    Barcode TEXT NOT NULL,
                    ArticleName TEXT NOT NULL,
                    CountedQuantity INTEGER NOT NULL,
                    ExpectedQuantity INTEGER NOT NULL,
                    Location TEXT,
                    CountDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CountedBy TEXT,
                    Comments TEXT,
                    IsValidated BOOLEAN DEFAULT 0,
                    InventorySession TEXT,
                    FOREIGN KEY (ArticleId) REFERENCES Articles (Id)
                )";

            // Index pour améliorer les performances
            string createIndexes = @"
                CREATE INDEX IF NOT EXISTS idx_articles_barcode ON Articles(Barcode);
                CREATE INDEX IF NOT EXISTS idx_articles_name ON Articles(Name);
                CREATE INDEX IF NOT EXISTS idx_articles_category ON Articles(Category);
                CREATE INDEX IF NOT EXISTS idx_inventory_session ON InventoryItems(InventorySession);
                CREATE INDEX IF NOT EXISTS idx_inventory_date ON InventoryItems(CountDate);
            ";

            using (SQLiteCommand command = new SQLiteCommand(createArticlesTable, connection))
            {
                command.ExecuteNonQuery();
            }

            using (SQLiteCommand command = new SQLiteCommand(createInventoryTable, connection))
            {
                command.ExecuteNonQuery();
            }

            using (SQLiteCommand command = new SQLiteCommand(createIndexes, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        #region Gestion des Articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        public static int AddArticle(Article article)
        {
            string sql = @"
                INSERT INTO Articles (Barcode, Name, Description, Category, UnitPrice, StockQuantity, MinimumStock, Unit, Location, CreatedDate, LastModified, IsActive)
                VALUES (@Barcode, @Name, @Description, @Category, @UnitPrice, @StockQuantity, @MinimumStock, @Unit, @Location, @CreatedDate, @LastModified, @IsActive);
                SELECT last_insert_rowid();";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Barcode", article.Barcode);
                    command.Parameters.AddWithValue("@Name", article.Name);
                    command.Parameters.AddWithValue("@Description", article.Description ?? "");
                    command.Parameters.AddWithValue("@Category", article.Category ?? "");
                    command.Parameters.AddWithValue("@UnitPrice", article.UnitPrice);
                    command.Parameters.AddWithValue("@StockQuantity", article.StockQuantity);
                    command.Parameters.AddWithValue("@MinimumStock", article.MinimumStock);
                    command.Parameters.AddWithValue("@Unit", article.Unit ?? "pièce");
                    command.Parameters.AddWithValue("@Location", article.Location ?? "");
                    command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    command.Parameters.AddWithValue("@LastModified", DateTime.Now);
                    command.Parameters.AddWithValue("@IsActive", article.IsActive);

                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        public static bool UpdateArticle(Article article)
        {
            string sql = @"
                UPDATE Articles 
                SET Barcode = @Barcode, Name = @Name, Description = @Description, Category = @Category,
                    UnitPrice = @UnitPrice, StockQuantity = @StockQuantity, MinimumStock = @MinimumStock,
                    Unit = @Unit, Location = @Location, LastModified = @LastModified, IsActive = @IsActive
                WHERE Id = @Id";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Id", article.Id);
                    command.Parameters.AddWithValue("@Barcode", article.Barcode);
                    command.Parameters.AddWithValue("@Name", article.Name);
                    command.Parameters.AddWithValue("@Description", article.Description ?? "");
                    command.Parameters.AddWithValue("@Category", article.Category ?? "");
                    command.Parameters.AddWithValue("@UnitPrice", article.UnitPrice);
                    command.Parameters.AddWithValue("@StockQuantity", article.StockQuantity);
                    command.Parameters.AddWithValue("@MinimumStock", article.MinimumStock);
                    command.Parameters.AddWithValue("@Unit", article.Unit ?? "pièce");
                    command.Parameters.AddWithValue("@Location", article.Location ?? "");
                    command.Parameters.AddWithValue("@LastModified", DateTime.Now);
                    command.Parameters.AddWithValue("@IsActive", article.IsActive);

                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        /// <summary>
        /// Supprime un article
        /// </summary>
        public static bool DeleteArticle(int articleId)
        {
            string sql = "UPDATE Articles SET IsActive = 0 WHERE Id = @Id";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Id", articleId);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        public static Article GetArticleByBarcode(string barcode)
        {
            string sql = "SELECT * FROM Articles WHERE Barcode = @Barcode AND IsActive = 1";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Barcode", barcode);
                    using (SQLiteDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return MapReaderToArticle(reader);
                        }
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// Récupère tous les articles actifs
        /// </summary>
        public static List<Article> GetAllArticles()
        {
            List<Article> articles = new List<Article>();
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 ORDER BY Name";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    using (SQLiteDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            articles.Add(MapReaderToArticle(reader));
                        }
                    }
                }
            }
            return articles;
        }

        /// <summary>
        /// Recherche des articles
        /// </summary>
        public static List<Article> SearchArticles(string searchTerm)
        {
            List<Article> articles = new List<Article>();
            string sql = @"
                SELECT * FROM Articles 
                WHERE IsActive = 1 AND (
                    Name LIKE @SearchTerm OR 
                    Description LIKE @SearchTerm OR 
                    Barcode LIKE @SearchTerm OR
                    Category LIKE @SearchTerm
                )
                ORDER BY Name";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", "%" + searchTerm + "%");
                    using (SQLiteDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            articles.Add(MapReaderToArticle(reader));
                        }
                    }
                }
            }
            return articles;
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        public static List<Article> GetOutOfStockArticles()
        {
            List<Article> articles = new List<Article>();
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 AND StockQuantity <= 0 ORDER BY Name";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    using (SQLiteDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            articles.Add(MapReaderToArticle(reader));
                        }
                    }
                }
            }
            return articles;
        }

        /// <summary>
        /// Récupère les articles en stock bas
        /// </summary>
        public static List<Article> GetLowStockArticles()
        {
            List<Article> articles = new List<Article>();
            string sql = "SELECT * FROM Articles WHERE IsActive = 1 AND StockQuantity <= MinimumStock ORDER BY Name";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    using (SQLiteDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            articles.Add(MapReaderToArticle(reader));
                        }
                    }
                }
            }
            return articles;
        }

        #endregion

        #region Statistiques

        /// <summary>
        /// Récupère le nombre total d'articles
        /// </summary>
        public static int GetTotalArticlesCount()
        {
            string sql = "SELECT COUNT(*) FROM Articles WHERE IsActive = 1";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        /// <summary>
        /// Récupère la valeur totale du stock
        /// </summary>
        public static decimal GetTotalStockValue()
        {
            string sql = "SELECT SUM(StockQuantity * UnitPrice) FROM Articles WHERE IsActive = 1";

            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(sql, connection))
                {
                    object result = command.ExecuteScalar();
                    return result == DBNull.Value ? 0 : Convert.ToDecimal(result);
                }
            }
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Mappe un DataReader vers un objet Article
        /// </summary>
        private static Article MapReaderToArticle(SQLiteDataReader reader)
        {
            return new Article
            {
                Id = Convert.ToInt32(reader["Id"]),
                Barcode = reader["Barcode"].ToString(),
                Name = reader["Name"].ToString(),
                Description = reader["Description"].ToString(),
                Category = reader["Category"].ToString(),
                UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                StockQuantity = Convert.ToInt32(reader["StockQuantity"]),
                MinimumStock = Convert.ToInt32(reader["MinimumStock"]),
                Unit = reader["Unit"].ToString(),
                Location = reader["Location"].ToString(),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                LastModified = Convert.ToDateTime(reader["LastModified"]),
                IsActive = Convert.ToBoolean(reader["IsActive"])
            };
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        public static bool TestConnection()
        {
            try
            {
                using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obtient des informations sur la base de données
        /// </summary>
        public static string GetDatabaseInfo()
        {
            try
            {
                string dbPath = Path.Combine(GetApplicationPath(), DatabaseFileName);
                FileInfo fileInfo = new FileInfo(dbPath);
                
                using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    using (SQLiteCommand command = new SQLiteCommand("SELECT COUNT(*) FROM Articles WHERE IsActive = 1", connection))
                    {
                        int articleCount = Convert.ToInt32(command.ExecuteScalar());
                        
                        return string.Format(
                            "Base de données SQLite\n" +
                            "Fichier: {0}\n" +
                            "Taille: {1} KB\n" +
                            "Articles: {2}\n" +
                            "Dernière modification: {3}",
                            dbPath,
                            fileInfo.Length / 1024,
                            articleCount,
                            fileInfo.LastWriteTime.ToString("dd/MM/yyyy HH:mm")
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                return "Erreur: " + ex.Message;
            }
        }

        #endregion
    }
}
