@echo off
REM Script utilisant MSBuild directement

echo ========================================
echo  BUILD MSBUILD - INVENTAIRE MC2100
echo ========================================
echo.

REM Rechercher MSBuild de Visual Studio 2022
set MSBUILD_PATH=""

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve: Visual Studio 2022 Community
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve: Visual Studio 2022 Professional
) else (
    echo ERREUR: MSBuild non trouve
    echo.
    echo SOLUTION GARANTIE:
    echo 1. Menu Demarrer > "Developer Command Prompt for VS 2022"
    echo 2. Dans cette invite, taper: cd "C:\Users\<USER>\Desktop\Inventaire"
    echo 3. Puis taper: msbuild
    echo.
    pause
    exit /b 1
)

echo.

REM Créer un fichier projet simple
echo Creation du fichier projet MSBuild...

echo ^<Project Sdk="Microsoft.NET.Sdk"^> > InventoryApp.csproj
echo   ^<PropertyGroup^> >> InventoryApp.csproj
echo     ^<OutputType^>WinExe^</OutputType^> >> InventoryApp.csproj
echo     ^<TargetFramework^>net48^</TargetFramework^> >> InventoryApp.csproj
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> InventoryApp.csproj
echo     ^<DefineConstants^>USE_XML_DATABASE^</DefineConstants^> >> InventoryApp.csproj
echo   ^</PropertyGroup^> >> InventoryApp.csproj
echo   ^<ItemGroup^> >> InventoryApp.csproj
echo     ^<Reference Include="Microsoft.VisualBasic" /^> >> InventoryApp.csproj
echo   ^</ItemGroup^> >> InventoryApp.csproj
echo ^</Project^> >> InventoryApp.csproj

echo Fichier projet cree: InventoryApp.csproj
echo.

echo Compilation avec MSBuild...
%MSBUILD_PATH% InventoryApp.csproj /p:Configuration=Release /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERREUR: Compilation echouee
    echo.
    echo SOLUTION ALTERNATIVE:
    echo 1. Ouvrir Visual Studio 2022
    echo 2. File > New > Project > Console App (.NET Framework)
    echo 3. Copier tous les fichiers .cs dans le projet
    echo 4. Build > Build Solution
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\net48\InventoryApp.exe" (
    echo Fichier genere: bin\Release\net48\InventoryApp.exe
    
    REM Copier vers un dossier plus simple
    if not exist "dist" mkdir "dist"
    copy "bin\Release\net48\InventoryApp.exe" "dist\"
    copy "bin\Release\net48\*.dll" "dist\" >nul 2>&1
    
    echo Fichier final: dist\InventoryApp.exe
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Inventaire MC2100" "dist\InventoryApp.exe"
    )
) else (
    echo ERREUR: Fichier executable non trouve
)

REM Nettoyer
del InventoryApp.csproj >nul 2>&1

echo.
echo Compilation terminee !
pause
