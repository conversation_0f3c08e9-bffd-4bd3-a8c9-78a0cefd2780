@echo off
REM Verification des fichiers necessaires pour la compilation XML

echo ========================================
echo  VERIFICATION DES FICHIERS
echo ========================================
echo.

set MISSING_FILES=0

echo Verification des fichiers principaux...
if exist "Program.cs" (
    echo [OK] Program.cs
) else (
    echo [MANQUE] Program.cs
    set /a MISSING_FILES+=1
)

if exist "Properties\AssemblyInfo.cs" (
    echo [OK] Properties\AssemblyInfo.cs
) else (
    echo [MANQUE] Properties\AssemblyInfo.cs
    set /a MISSING_FILES+=1
)

echo.
echo Verification des modeles...
if exist "Models\Article.cs" (
    echo [OK] Models\Article.cs
) else (
    echo [MANQUE] Models\Article.cs
    set /a MISSING_FILES+=1
)

if exist "Models\InventoryItem.cs" (
    echo [OK] Models\InventoryItem.cs
) else (
    echo [MANQUE] Models\InventoryItem.cs
    set /a MISSING_FILES+=1
)

echo.
echo Verification des donnees (VERSION XML)...
if exist "Data\DatabaseHelper-Simple.cs" (
    echo [OK] Data\DatabaseHelper-Simple.cs
) else (
    echo [MANQUE] Data\DatabaseHelper-Simple.cs
    set /a MISSING_FILES+=1
)

echo.
echo Verification des services (VERSION XML)...
if exist "Services\BarcodeService.cs" (
    echo [OK] Services\BarcodeService.cs
) else (
    echo [MANQUE] Services\BarcodeService.cs
    set /a MISSING_FILES+=1
)

if exist "Services\InventoryService-Simple.cs" (
    echo [OK] Services\InventoryService-Simple.cs
) else (
    echo [MANQUE] Services\InventoryService-Simple.cs
    set /a MISSING_FILES+=1
)

echo.
echo Verification des utilitaires...
if exist "Utils\CurrencyHelper.cs" (
    echo [OK] Utils\CurrencyHelper.cs
) else (
    echo [MANQUE] Utils\CurrencyHelper.cs
    set /a MISSING_FILES+=1
)

echo.
echo Verification des formulaires...
if exist "Forms\MainForm.cs" (
    echo [OK] Forms\MainForm.cs
) else (
    echo [MANQUE] Forms\MainForm.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\MainForm.Designer.cs" (
    echo [OK] Forms\MainForm.Designer.cs
) else (
    echo [MANQUE] Forms\MainForm.Designer.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\ArticleForm.cs" (
    echo [OK] Forms\ArticleForm.cs
) else (
    echo [MANQUE] Forms\ArticleForm.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\ArticleForm.Designer.cs" (
    echo [OK] Forms\ArticleForm.Designer.cs
) else (
    echo [MANQUE] Forms\ArticleForm.Designer.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\InventoryForm.cs" (
    echo [OK] Forms\InventoryForm.cs
) else (
    echo [MANQUE] Forms\InventoryForm.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\InventoryForm.Designer.cs" (
    echo [OK] Forms\InventoryForm.Designer.cs
) else (
    echo [MANQUE] Forms\InventoryForm.Designer.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\ReportsForm.cs" (
    echo [OK] Forms\ReportsForm.cs
) else (
    echo [MANQUE] Forms\ReportsForm.cs
    set /a MISSING_FILES+=1
)

if exist "Forms\ReportsForm.Designer.cs" (
    echo [OK] Forms\ReportsForm.Designer.cs
) else (
    echo [MANQUE] Forms\ReportsForm.Designer.cs
    set /a MISSING_FILES+=1
)

echo.
echo ========================================
echo  RESULTAT DE LA VERIFICATION
echo ========================================
echo.

if %MISSING_FILES% equ 0 (
    echo [SUCCES] Tous les fichiers sont presents !
    echo.
    echo PRET POUR LA COMPILATION:
    echo - Ouvrir "Developer Command Prompt for VS 2022"
    echo - Executer: build-xml-only.bat
    echo.
    echo VERSION XML COMPLETE AVEC:
    echo • Devise Dinar Tunisien (DT)
    echo • Gestion articles complete
    echo • Module inventaire fonctionnel
    echo • Rapports detailles
    echo • Scanner codes-barres
    echo.
) else (
    echo [ERREUR] %MISSING_FILES% fichiers manquants !
    echo.
    echo FICHIERS A CREER:
    echo Consultez les messages [MANQUE] ci-dessus
    echo.
)

echo FICHIERS EXCLUS (SQL Server CE):
echo • Data\DatabaseHelper.cs
echo • Data\InventoryContext.cs
echo • Services\InventoryService.cs
echo.
echo Ces fichiers ne sont PAS necessaires pour la version XML.
echo.

pause
