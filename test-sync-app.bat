@echo off
REM Test rapide de compilation de l'application de synchronisation

echo ========================================
echo  TEST COMPILATION APPLICATION SYNC
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur non disponible
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    pause
    exit /b 1
)

echo [OK] Environnement pret
echo.

REM Vérifier que tous les fichiers existent
echo Verification des fichiers sources...

set MISSING=0

if not exist "SyncApp\Program.cs" (
    echo [MANQUE] SyncApp\Program.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Program.cs
)

if not exist "SyncApp\Models\SyncItem.cs" (
    echo [MANQUE] SyncApp\Models\SyncItem.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Models\SyncItem.cs
)

if not exist "SyncApp\Services\SyncService.cs" (
    echo [MANQUE] SyncApp\Services\SyncService.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Services\SyncService.cs
)

if not exist "SyncApp\Forms\SyncMainForm.cs" (
    echo [MANQUE] SyncApp\Forms\SyncMainForm.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Forms\SyncMainForm.cs
)

if not exist "SyncApp\Forms\SyncMainForm.Designer.cs" (
    echo [MANQUE] SyncApp\Forms\SyncMainForm.Designer.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Forms\SyncMainForm.Designer.cs
)

if not exist "SyncApp\Forms\SyncResultsForm.cs" (
    echo [MANQUE] SyncApp\Forms\SyncResultsForm.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Forms\SyncResultsForm.cs
)

if not exist "SyncApp\Forms\SyncResultsForm.Designer.cs" (
    echo [MANQUE] SyncApp\Forms\SyncResultsForm.Designer.cs
    set /a MISSING+=1
) else (
    echo [OK] SyncApp\Forms\SyncResultsForm.Designer.cs
)

if %MISSING% gtr 0 (
    echo.
    echo [ERREUR] %MISSING% fichiers manquants
    pause
    exit /b 1
)

echo.
echo [OK] Tous les fichiers sources presents
echo.

REM Créer dossier de test
if not exist "bin" mkdir "bin"
if not exist "bin\TestSync" mkdir "bin\TestSync"

echo Test de compilation...

REM Compilation de test
csc /target:winexe /out:bin\TestSync\SyncAppTest.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:lib\System.Data.SQLite.dll /nowarn:0168,0219 SyncApp\Program.cs SyncApp\Models\SyncItem.cs SyncApp\Services\SyncService.cs SyncApp\Forms\SyncMainForm.cs SyncApp\Forms\SyncMainForm.Designer.cs SyncApp\Forms\SyncResultsForm.cs SyncApp\Forms\SyncResultsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo.
    echo [SUCCES] Compilation de test reussie !
    echo.
    
    REM Copier SQLite DLL
    copy "lib\System.Data.SQLite.dll" "bin\TestSync\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier genere: bin\TestSync\SyncAppTest.exe
    for %%I in ("bin\TestSync\SyncAppTest.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION DE SYNCHRONISATION PRETE !
    echo.
    echo FONCTIONNALITES:
    echo • Interface graphique moderne
    echo • Synchronisation XML ↔ SQLite
    echo • Detection de conflits
    echo • Export CSV des resultats
    echo • Journal de synchronisation
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement du test...
        start "Test Sync App" "bin\TestSync\SyncAppTest.exe"
        echo.
        echo APPLICATION LANCEE !
        echo.
        echo TESTEZ:
        echo 1. Interface principale
        echo 2. Detection des fichiers de donnees
        echo 3. Boutons de synchronisation
        echo 4. Journal en temps reel
        echo.
        echo Si l'interface s'affiche correctement,
        echo utilisez build-sync-app.bat pour la version finale.
    )
    
) else (
    echo.
    echo [ERREUR] Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
)

echo.
REM Nettoyer
rmdir /s /q "bin\TestSync" >nul 2>&1

pause
