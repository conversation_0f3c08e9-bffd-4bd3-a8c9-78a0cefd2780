@echo off
REM Compilation version XML uniquement (sans SQL Server CE)

echo ========================================
echo  BUILD XML ONLY - INVENTAIRE MC2100
echo  Version XML sans dependances SQL CE
echo ========================================
echo.

echo Test de la commande csc...
csc /? >nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Executer ce script depuis cette invite
    echo.
    pause
    exit /b 1
)

echo Commande csc disponible !
echo.

REM Vérifier que les fichiers XML existent
if not exist "Data\DatabaseHelper-Simple.cs" (
    echo ERREUR: Fichier Data\DatabaseHelper-Simple.cs manquant
    pause
    exit /b 1
)

if not exist "Services\InventoryService-Simple.cs" (
    echo ERREUR: Fichier Services\InventoryService-Simple.cs manquant
    pause
    exit /b 1
)

if not exist "Utils\CurrencyHelper.cs" (
    echo ERREUR: Fichier Utils\CurrencyHelper.cs manquant
    pause
    exit /b 1
)

echo Tous les fichiers XML sont presents.
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation version XML en cours...
echo.

REM Compiler UNIQUEMENT les fichiers XML (pas les SQL CE)
csc /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm.cs" ^
    "Forms\InventoryForm.Designer.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo.
    echo FICHIERS EXCLUS (SQL Server CE):
    echo - Data\DatabaseHelper.cs
    echo - Data\InventoryContext.cs
    echo - Services\InventoryService.cs
    echo.
    echo FICHIERS INCLUS (Version XML):
    echo - Data\DatabaseHelper-Simple.cs
    echo - Services\InventoryService-Simple.cs
    echo - Utils\CurrencyHelper.cs
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo VERSION XML COMPLETE AVEC:
    echo =============================
    echo • Base de donnees XML (aucune dependance)
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete
    echo • Module inventaire fonctionnel
    echo • Rapports detailles (4 types)
    echo • Scanner codes-barres integre
    echo • Interface optimisee MC2100
    echo.
    
    echo MODULES FONCTIONNELS:
    echo ====================
    echo • GESTION ARTICLES: Creation, modification, suppression
    echo • INVENTAIRE: Sessions, comptage, ecarts, validation
    echo • RAPPORTS: Stock global, ruptures, stock bas, categories
    echo • SCANNER: Lecture automatique + saisie manuelle
    echo.
    
    echo DEPLOIEMENT SUR MC2100:
    echo =======================
    echo 1. Copier bin\Release\InventoryApp.exe vers le terminal
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo 4. Les donnees seront stockees dans InventoryDB.xml
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application complete...
        start "Inventaire MC2100 - Version Complete" "bin\Release\InventoryApp.exe"
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo 1. Gestion Articles: Creer un article avec prix en DT
        echo 2. Inventaire: Demarrer une session d'inventaire
        echo 3. Rapports: Generer les differents rapports
        echo 4. Scanner: Tester la saisie de codes-barres
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  APPLICATION COMPLETE PRETE !
echo ========================================
echo.
echo L'application d'inventaire est maintenant complete
echo avec toutes les fonctionnalites demandees:
echo.
echo • Devise tunisienne (Dinar - DT)
echo • Tous les modules fonctionnels
echo • Compatible Zebra MC2100
echo • Base de donnees XML autonome
echo.
echo Compilation terminee avec succes !
pause
