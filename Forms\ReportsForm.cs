using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using InventoryApp.Models;
using InventoryApp.Services;
using InventoryApp.Utils;

namespace InventoryApp.Forms
{
    /// <summary>
    /// Formulaire de rapports complet
    /// </summary>
    public partial class ReportsForm : Form
    {
#if USE_SQLITE_DATABASE
        private readonly InventoryServiceSQLite _inventoryService;
#elif USE_XML_DATABASE
        private readonly InventoryServiceSimple _inventoryService;
#else
        private readonly InventoryService _inventoryService;
#endif

        /// <summary>
        /// Constructeur du formulaire de rapports
        /// </summary>
        public ReportsForm()
        {
            InitializeComponent();

#if USE_SQLITE_DATABASE
            _inventoryService = new InventoryServiceSQLite();
#elif USE_XML_DATABASE
            _inventoryService = new InventoryServiceSimple();
#else
            _inventoryService = new InventoryService();
#endif
        }

        /// <summary>
        /// Génère le rapport de stock global
        /// </summary>
        private void btnStockReport_Click(object sender, EventArgs e)
        {
            try
            {
                List<Article> articles = _inventoryService.GetAllArticles();

                StringBuilder report = new StringBuilder();
                report.AppendLine("RAPPORT DE STOCK GLOBAL");
                report.AppendLine("======================");
                report.AppendLine("Date: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm"));
                report.AppendLine();

                decimal totalValue = 0;
                int totalArticles = 0;

                report.AppendLine("CODE-BARRES".PadRight(15) + "ARTICLE".PadRight(20) + "QTÉ".PadRight(8) + "PRIX".PadRight(12) + "VALEUR");
                report.AppendLine(new string('-', 70));

                foreach (Article article in articles)
                {
                    decimal articleValue = article.StockQuantity * article.UnitPrice;
                    totalValue += articleValue;
                    totalArticles++;

                    report.AppendLine(
                        article.Barcode.PadRight(15) +
                        (article.Name.Length > 19 ? article.Name.Substring(0, 19) : article.Name).PadRight(20) +
                        article.StockQuantity.ToString().PadRight(8) +
                        CurrencyHelper.FormatCurrency(article.UnitPrice).PadRight(12) +
                        CurrencyHelper.FormatCurrency(articleValue)
                    );
                }

                report.AppendLine(new string('-', 70));
                report.AppendLine("TOTAL: " + totalArticles + " articles");
                report.AppendLine("VALEUR TOTALE: " + CurrencyHelper.FormatCurrency(totalValue));

                ShowReport("Rapport de Stock", report.ToString());
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la génération du rapport: " + ex.Message);
            }
        }

        /// <summary>
        /// Génère le rapport des articles en rupture
        /// </summary>
        private void btnOutOfStockReport_Click(object sender, EventArgs e)
        {
            try
            {
                List<Article> outOfStockArticles = _inventoryService.GetOutOfStockArticles();

                StringBuilder report = new StringBuilder();
                report.AppendLine("RAPPORT ARTICLES EN RUPTURE");
                report.AppendLine("===========================");
                report.AppendLine("Date: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm"));
                report.AppendLine();

                if (outOfStockArticles.Count == 0)
                {
                    report.AppendLine("Aucun article en rupture de stock.");
                }
                else
                {
                    report.AppendLine("CODE-BARRES".PadRight(15) + "ARTICLE".PadRight(25) + "CATÉGORIE".PadRight(15) + "EMPLACEMENT");
                    report.AppendLine(new string('-', 70));

                    foreach (Article article in outOfStockArticles)
                    {
                        report.AppendLine(
                            article.Barcode.PadRight(15) +
                            (article.Name.Length > 24 ? article.Name.Substring(0, 24) : article.Name).PadRight(25) +
                            (article.Category ?? "").PadRight(15) +
                            (article.Location ?? "")
                        );
                    }

                    report.AppendLine();
                    report.AppendLine("TOTAL: " + outOfStockArticles.Count + " articles en rupture");
                }

                ShowReport("Articles en Rupture", report.ToString());
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la génération du rapport: " + ex.Message);
            }
        }

        /// <summary>
        /// Génère le rapport des articles en stock bas
        /// </summary>
        private void btnLowStockReport_Click(object sender, EventArgs e)
        {
            try
            {
                List<Article> lowStockArticles = _inventoryService.GetLowStockArticles();

                StringBuilder report = new StringBuilder();
                report.AppendLine("RAPPORT STOCK BAS");
                report.AppendLine("=================");
                report.AppendLine("Date: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm"));
                report.AppendLine();

                if (lowStockArticles.Count == 0)
                {
                    report.AppendLine("Aucun article en stock bas.");
                }
                else
                {
                    report.AppendLine("CODE-BARRES".PadRight(15) + "ARTICLE".PadRight(20) + "STOCK".PadRight(8) + "MIN".PadRight(8) + "STATUT");
                    report.AppendLine(new string('-', 65));

                    foreach (Article article in lowStockArticles)
                    {
                        string status = article.StockQuantity == 0 ? "RUPTURE" : "BAS";

                        report.AppendLine(
                            article.Barcode.PadRight(15) +
                            (article.Name.Length > 19 ? article.Name.Substring(0, 19) : article.Name).PadRight(20) +
                            article.StockQuantity.ToString().PadRight(8) +
                            article.MinimumStock.ToString().PadRight(8) +
                            status
                        );
                    }

                    report.AppendLine();
                    report.AppendLine("TOTAL: " + lowStockArticles.Count + " articles en stock bas");
                }

                ShowReport("Stock Bas", report.ToString());
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la génération du rapport: " + ex.Message);
            }
        }

        /// <summary>
        /// Génère le rapport par catégorie
        /// </summary>
        private void btnCategoryReport_Click(object sender, EventArgs e)
        {
            try
            {
                List<Article> articles = _inventoryService.GetAllArticles();
                Dictionary<string, CategoryStats> categoryStats = new Dictionary<string, CategoryStats>();

                // Calculer les statistiques par catégorie
                foreach (Article article in articles)
                {
                    string category = string.IsNullOrEmpty(article.Category) ? "Sans catégorie" : article.Category;

                    if (!categoryStats.ContainsKey(category))
                    {
                        categoryStats[category] = new CategoryStats();
                    }

                    categoryStats[category].ArticleCount++;
                    categoryStats[category].TotalQuantity += article.StockQuantity;
                    categoryStats[category].TotalValue += article.StockQuantity * article.UnitPrice;
                }

                StringBuilder report = new StringBuilder();
                report.AppendLine("RAPPORT PAR CATÉGORIE");
                report.AppendLine("====================");
                report.AppendLine("Date: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm"));
                report.AppendLine();

                report.AppendLine("CATÉGORIE".PadRight(20) + "ARTICLES".PadRight(10) + "QUANTITÉ".PadRight(10) + "VALEUR");
                report.AppendLine(new string('-', 60));

                decimal grandTotal = 0;
                int totalArticleCount = 0;

                foreach (var kvp in categoryStats)
                {
                    report.AppendLine(
                        kvp.Key.PadRight(20) +
                        kvp.Value.ArticleCount.ToString().PadRight(10) +
                        kvp.Value.TotalQuantity.ToString().PadRight(10) +
                        CurrencyHelper.FormatCurrency(kvp.Value.TotalValue)
                    );

                    grandTotal += kvp.Value.TotalValue;
                    totalArticleCount += kvp.Value.ArticleCount;
                }

                report.AppendLine(new string('-', 60));
                report.AppendLine("TOTAL: " + totalArticleCount + " articles");
                report.AppendLine("VALEUR TOTALE: " + CurrencyHelper.FormatCurrency(grandTotal));

                ShowReport("Rapport par Catégorie", report.ToString());
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la génération du rapport: " + ex.Message);
            }
        }

        /// <summary>
        /// Affiche un rapport dans une nouvelle fenêtre
        /// </summary>
        private void ShowReport(string title, string content)
        {
            Form reportForm = new Form();
            reportForm.Text = title;
            reportForm.Size = new System.Drawing.Size(400, 300);
            reportForm.StartPosition = FormStartPosition.CenterParent;

            TextBox txtReport = new TextBox();
            txtReport.Multiline = true;
            txtReport.ScrollBars = ScrollBars.Both;
            txtReport.Font = new System.Drawing.Font("Courier New", 8);
            txtReport.Text = content;
            txtReport.ReadOnly = true;
            txtReport.Dock = DockStyle.Fill;

            reportForm.Controls.Add(txtReport);
            reportForm.ShowDialog();
        }

        /// <summary>
        /// Fermer le formulaire
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #region Classes utilitaires

        /// <summary>
        /// Statistiques par catégorie
        /// </summary>
        private class CategoryStats
        {
            public int ArticleCount { get; set; }
            public int TotalQuantity { get; set; }
            public decimal TotalValue { get; set; }
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Affiche un message d'erreur
        /// </summary>
        private void ShowError(string message)
        {
            MessageBox.Show(message, "Erreur",
                MessageBoxButtons.OK, MessageBoxIcon.Exclamation,
                MessageBoxDefaultButton.Button1);
        }

        #endregion
    }
}
