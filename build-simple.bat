@echo off
REM Script de compilation simplifie pour l'application Inventaire
REM Compatible avec les versions modernes de .NET et Visual Studio

echo ========================================
echo  BUILD SIMPLE - INVENTAIRE MC2100
echo ========================================
echo.

REM Rechercher csc.exe (compilateur C#)
set CSC_PATH=""
if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo Compilateur C# .NET 4.0 trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v3.5\csc.exe"
    echo Compilateur C# .NET 3.5 trouve
) else if exist "%WINDIR%\Microsoft.NET\Framework\v2.0.50727\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v2.0.50727\csc.exe"
    echo Compilateur C# .NET 2.0 trouve
) else (
    echo ERREUR: Compilateur C# non trouve
    echo Veuillez installer .NET Framework
    pause
    exit /b 1
)

echo.
echo Compilation manuelle des fichiers sources...
echo.

REM Créer les dossiers de sortie
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

REM Compiler l'application
echo Compilation en cours...
%CSC_PATH% /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    Program.cs ^
    Properties\AssemblyInfo.cs ^
    Models\Article.cs ^
    Models\InventoryItem.cs ^
    Data\DatabaseHelper.cs ^
    Data\InventoryContext.cs ^
    Services\BarcodeService.cs ^
    Services\InventoryService.cs ^
    Forms\MainForm.cs ^
    Forms\MainForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE
echo ========================================
echo.
echo Fichier genere: bin\Release\InventoryApp.exe
echo.
echo ATTENTION: Cette version ne supporte pas SQL Server CE
echo Pour une version complete, utilisez Visual Studio ou installez
echo les outils de developpement Windows CE.
echo.
echo Pour tester sur PC Windows:
echo 1. Copier InventoryApp.exe sur votre bureau
echo 2. Lancer l'application
echo 3. Tester les fonctionnalites de base
echo.

REM Copier le fichier vers le dossier de distribution
if not exist "dist" mkdir "dist"
if not exist "dist\Simple" mkdir "dist\Simple"
copy "bin\Release\InventoryApp.exe" "dist\Simple\"

echo Fichier copie vers: dist\Simple\InventoryApp.exe
echo.
pause
