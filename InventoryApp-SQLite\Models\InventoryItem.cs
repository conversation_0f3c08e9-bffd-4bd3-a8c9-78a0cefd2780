using System;

namespace InventoryApp.Models
{
    /// <summary>
    /// Modèle représentant un élément d'inventaire lors d'un comptage
    /// </summary>
    public class InventoryItem
    {
        /// <summary>
        /// Identifiant unique de l'élément d'inventaire
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Identifiant de l'article associé
        /// </summary>
        public int ArticleId { get; set; }

        /// <summary>
        /// Code-barres de l'article
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// Nom de l'article
        /// </summary>
        public string ArticleName { get; set; }

        /// <summary>
        /// Quantité comptée lors de l'inventaire
        /// </summary>
        public int CountedQuantity { get; set; }

        /// <summary>
        /// Quantité théorique (stock système)
        /// </summary>
        public int ExpectedQuantity { get; set; }

        /// <summary>
        /// Écart entre quantité comptée et théorique
        /// </summary>
        public int Variance
        {
            get { return CountedQuantity - ExpectedQuantity; }
        }

        /// <summary>
        /// Emplacement où l'inventaire a été effectué
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Date et heure du comptage
        /// </summary>
        public DateTime CountDate { get; set; }

        /// <summary>
        /// Utilisateur ayant effectué le comptage
        /// </summary>
        public string CountedBy { get; set; }

        /// <summary>
        /// Commentaires sur l'inventaire
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Indique si l'inventaire a été validé
        /// </summary>
        public bool IsValidated { get; set; }

        /// <summary>
        /// Date de validation
        /// </summary>
        public DateTime? ValidatedDate { get; set; }

        /// <summary>
        /// Utilisateur ayant validé l'inventaire
        /// </summary>
        public string ValidatedBy { get; set; }

        /// <summary>
        /// Session d'inventaire (pour regrouper plusieurs comptages)
        /// </summary>
        public string InventorySession { get; set; }

        /// <summary>
        /// Constructeur par défaut
        /// </summary>
        public InventoryItem()
        {
            CountDate = DateTime.Now;
            IsValidated = false;
            CountedQuantity = 0;
            ExpectedQuantity = 0;
        }

        /// <summary>
        /// Constructeur avec paramètres
        /// </summary>
        /// <param name="articleId">ID de l'article</param>
        /// <param name="barcode">Code-barres</param>
        /// <param name="articleName">Nom de l'article</param>
        /// <param name="expectedQuantity">Quantité théorique</param>
        public InventoryItem(int articleId, string barcode, string articleName, int expectedQuantity) : this()
        {
            ArticleId = articleId;
            Barcode = barcode;
            ArticleName = articleName;
            ExpectedQuantity = expectedQuantity;
        }

        /// <summary>
        /// Vérifie s'il y a un écart significatif
        /// </summary>
        /// <param name="tolerancePercent">Pourcentage de tolérance</param>
        /// <returns>True s'il y a un écart significatif</returns>
        public bool HasSignificantVariance(double tolerancePercent = 5.0)
        {
            if (ExpectedQuantity == 0)
                return CountedQuantity != 0;

            double variancePercent = Math.Abs(Variance) * 100.0 / ExpectedQuantity;
            return variancePercent > tolerancePercent;
        }

        /// <summary>
        /// Calcule le pourcentage d'écart
        /// </summary>
        /// <returns>Pourcentage d'écart</returns>
        public double GetVariancePercentage()
        {
            if (ExpectedQuantity == 0)
                return CountedQuantity == 0 ? 0 : 100;

            return (Variance * 100.0) / ExpectedQuantity;
        }

        /// <summary>
        /// Valide l'inventaire
        /// </summary>
        /// <param name="validatedBy">Utilisateur validant</param>
        public void Validate(string validatedBy)
        {
            IsValidated = true;
            ValidatedDate = DateTime.Now;
            ValidatedBy = validatedBy;
        }

        /// <summary>
        /// Annule la validation
        /// </summary>
        public void CancelValidation()
        {
            IsValidated = false;
            ValidatedDate = null;
            ValidatedBy = null;
        }

        /// <summary>
        /// Représentation textuelle de l'élément d'inventaire
        /// </summary>
        /// <returns>Chaîne descriptive</returns>
        public override string ToString()
        {
            string status = IsValidated ? "Validé" : "En attente";
            return string.Format("{0} - {1}: {2}/{3} ({4:+#;-#;0}) - {5}", 
                Barcode, ArticleName, CountedQuantity, ExpectedQuantity, Variance, status);
        }
    }
}
