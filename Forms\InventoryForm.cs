using System;
using System.Collections.Generic;
using System.Windows.Forms;
using InventoryApp.Models;
using InventoryApp.Services;
using InventoryApp.Utils;

namespace InventoryApp.Forms
{
    /// <summary>
    /// Formulaire d'inventaire complet
    /// </summary>
    public partial class InventoryForm : Form
    {
#if USE_XML_DATABASE
        private readonly InventoryServiceSimple _inventoryService;
#else
        private readonly InventoryService _inventoryService;
#endif
        private readonly BarcodeService _barcodeService;
        private string _currentSession;
        private List<InventoryItem> _inventoryItems;

        /// <summary>
        /// Constructeur du formulaire d'inventaire
        /// </summary>
        public InventoryForm()
        {
            InitializeComponent();

#if USE_XML_DATABASE
            _inventoryService = new InventoryServiceSimple();
#else
            _inventoryService = new InventoryService();
#endif
            _barcodeService = new BarcodeService();
            _inventoryItems = new List<InventoryItem>();

            InitializeInventory();
        }

        /// <summary>
        /// Initialise l'inventaire
        /// </summary>
        private void InitializeInventory()
        {
            // Créer une nouvelle session d'inventaire
            _currentSession = "INV_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
            lblSession.Text = _currentSession;

            // Activer le scanner
            _barcodeService.EnableScanner();
            _barcodeService.BarcodeScanned += OnBarcodeScanned;

            // Initialiser la liste
            UpdateInventoryList();
        }

        /// <summary>
        /// Gestionnaire pour la lecture d'un code-barres
        /// </summary>
        private void OnBarcodeScanned(object sender, BarcodeScannedEventArgs e)
        {
            ProcessBarcode(e.Barcode);
        }

        /// <summary>
        /// Traite un code-barres scanné
        /// </summary>
        private void ProcessBarcode(string barcode)
        {
            try
            {
                // Rechercher l'article
                Article article = _inventoryService.GetArticleByBarcode(barcode);
                if (article == null)
                {
                    ShowError("Article non trouvé pour le code-barres: " + barcode);
                    return;
                }

                // Afficher les informations de l'article
                lblArticleName.Text = article.Name;
                lblExpectedQty.Text = article.StockQuantity.ToString();
                txtCountedQty.Text = "";
                txtCountedQty.Focus();

                // Stocker l'article actuel
                lblArticleName.Tag = article;
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du traitement du code-barres: " + ex.Message);
            }
        }

        /// <summary>
        /// Valide un comptage d'inventaire
        /// </summary>
        private void btnValidateCount_Click(object sender, EventArgs e)
        {
            try
            {
                Article article = lblArticleName.Tag as Article;
                if (article == null)
                {
                    ShowError("Aucun article sélectionné");
                    return;
                }

                int countedQty;
                if (!int.TryParse(txtCountedQty.Text, out countedQty) || countedQty < 0)
                {
                    ShowError("Quantité comptée invalide");
                    txtCountedQty.Focus();
                    return;
                }

                // Créer l'élément d'inventaire
                InventoryItem inventoryItem = new InventoryItem
                {
                    ArticleId = article.Id,
                    Barcode = article.Barcode,
                    ArticleName = article.Name,
                    CountedQuantity = countedQty,
                    ExpectedQuantity = article.StockQuantity,
                    Location = txtLocation.Text.Trim(),
                    CountedBy = txtCountedBy.Text.Trim(),
                    Comments = txtComments.Text.Trim(),
                    InventorySession = _currentSession,
                    CountDate = DateTime.Now
                };

                // Ajouter à la liste
                _inventoryItems.Add(inventoryItem);

                // Mettre à jour l'affichage
                UpdateInventoryList();
                ClearCurrentItem();

                ShowInfo("Comptage enregistré pour: " + article.Name);
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la validation: " + ex.Message);
            }
        }

        /// <summary>
        /// Met à jour la liste d'inventaire
        /// </summary>
        private void UpdateInventoryList()
        {
            lstInventory.Items.Clear();

            foreach (InventoryItem item in _inventoryItems)
            {
                string status = item.CountedQuantity == item.ExpectedQuantity ? "OK" : "ÉCART";
                string listItem = string.Format("{0} - Attendu:{1} Compté:{2} [{3}]",
                    item.ArticleName, item.ExpectedQuantity, item.CountedQuantity, status);
                lstInventory.Items.Add(listItem);
            }

            lblTotalItems.Text = _inventoryItems.Count.ToString();
        }

        /// <summary>
        /// Vide les champs de l'article actuel
        /// </summary>
        private void ClearCurrentItem()
        {
            lblArticleName.Text = "-";
            lblArticleName.Tag = null;
            lblExpectedQty.Text = "-";
            txtCountedQty.Text = "";
            txtComments.Text = "";
        }

        /// <summary>
        /// Finalise l'inventaire
        /// </summary>
        private void btnFinalize_Click(object sender, EventArgs e)
        {
            if (_inventoryItems.Count == 0)
            {
                ShowError("Aucun élément d'inventaire à finaliser");
                return;
            }

            if (MessageBox.Show("Voulez-vous finaliser cet inventaire ?", "Confirmation",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            {
                try
                {
                    // Ici, on pourrait sauvegarder dans la base de données
                    // Pour la version XML simplifiée, on affiche juste un résumé

                    ShowInventorySummary();
                }
                catch (Exception ex)
                {
                    ShowError("Erreur lors de la finalisation: " + ex.Message);
                }
            }
        }

        /// <summary>
        /// Affiche le résumé de l'inventaire
        /// </summary>
        private void ShowInventorySummary()
        {
            int totalItems = _inventoryItems.Count;
            int itemsWithDifferences = 0;
            decimal totalValueDifference = 0;

            foreach (InventoryItem item in _inventoryItems)
            {
                if (item.CountedQuantity != item.ExpectedQuantity)
                {
                    itemsWithDifferences++;

                    // Calculer la différence de valeur (approximative)
                    Article article = _inventoryService.GetArticleByBarcode(item.Barcode);
                    if (article != null)
                    {
                        int qtyDifference = item.CountedQuantity - item.ExpectedQuantity;
                        totalValueDifference += qtyDifference * article.UnitPrice;
                    }
                }
            }

            string summary = string.Format(
                "RÉSUMÉ DE L'INVENTAIRE\n" +
                "======================\n\n" +
                "Session: {0}\n" +
                "Date: {1}\n\n" +
                "Articles comptés: {2}\n" +
                "Articles avec écarts: {3}\n" +
                "Différence de valeur: {4}\n\n" +
                "Inventaire finalisé avec succès !",
                _currentSession,
                DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                totalItems,
                itemsWithDifferences,
                CurrencyHelper.FormatCurrency(totalValueDifference)
            );

            MessageBox.Show(summary, "Inventaire Finalisé",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1);

            // Réinitialiser pour un nouvel inventaire
            _inventoryItems.Clear();
            _currentSession = "INV_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
            lblSession.Text = _currentSession;
            UpdateInventoryList();
            ClearCurrentItem();
        }

        /// <summary>
        /// Fermer le formulaire
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            if (_barcodeService != null)
            {
                _barcodeService.DisableScanner();
            }
            this.Close();
        }

        /// <summary>
        /// Saisie manuelle d'un code-barres
        /// </summary>
        private void btnManualEntry_Click(object sender, EventArgs e)
        {
            string barcode = Microsoft.VisualBasic.Interaction.InputBox(
                "Saisissez le code-barres:", "Saisie manuelle", "");

            if (!string.IsNullOrEmpty(barcode))
            {
                ProcessBarcode(barcode.Trim());
            }
        }

        #region Méthodes utilitaires

        /// <summary>
        /// Affiche un message d'information
        /// </summary>
        private void ShowInfo(string message)
        {
            MessageBox.Show(message, "Information",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1);
        }

        /// <summary>
        /// Affiche un message d'erreur
        /// </summary>
        private void ShowError(string message)
        {
            MessageBox.Show(message, "Erreur",
                MessageBoxButtons.OK, MessageBoxIcon.Exclamation,
                MessageBoxDefaultButton.Button1);
        }

        #endregion
    }
}
