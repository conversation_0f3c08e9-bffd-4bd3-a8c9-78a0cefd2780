{"files.associations": {"*.cs": "csharp", "*.csproj": "xml", "*.sln": "text", "*.resx": "xml", "*.config": "xml"}, "files.exclude": {"**/bin": true, "**/obj": true, "**/*.user": true, "**/*.suo": true, "**/dist": false}, "search.exclude": {"**/bin": true, "**/obj": true, "**/dist": true}, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.encoding": "utf8", "files.eol": "\r\n", "csharp.format.enable": true, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true}