@echo off
REM Script de compilation utilisant MSBuild de Visual Studio 2022

echo ========================================
echo  BUILD VS2022 - INVENTAIRE MC2100
echo ========================================
echo.

REM Rechercher MSBuild de Visual Studio 2022
set MSBUILD_PATH=""

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve: Visual Studio 2022 Community
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve: Visual Studio 2022 Professional
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo MSBuild trouve: Visual Studio 2022 Enterprise
) else (
    echo ERREUR: MSBuild de Visual Studio 2022 non trouve
    echo.
    echo SOLUTION: Ouvrir "Developer Command Prompt for VS 2022"
    echo et executer ce script depuis cette invite.
    echo.
    pause
    exit /b 1
)

echo.

REM Créer un fichier projet temporaire
echo Creation du fichier projet...

echo ^<?xml version="1.0" encoding="utf-8"?^> > InventoryApp-Temp.csproj
echo ^<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003"^> >> InventoryApp-Temp.csproj
echo   ^<PropertyGroup^> >> InventoryApp-Temp.csproj
echo     ^<Configuration Condition=" '$(Configuration)' == '' "^>Release^</Configuration^> >> InventoryApp-Temp.csproj
echo     ^<Platform Condition=" '$(Platform)' == '' "^>AnyCPU^</Platform^> >> InventoryApp-Temp.csproj
echo     ^<OutputType^>WinExe^</OutputType^> >> InventoryApp-Temp.csproj
echo     ^<AssemblyName^>InventoryApp^</AssemblyName^> >> InventoryApp-Temp.csproj
echo     ^<TargetFrameworkVersion^>v4.8^</TargetFrameworkVersion^> >> InventoryApp-Temp.csproj
echo     ^<OutputPath^>bin\Release\^</OutputPath^> >> InventoryApp-Temp.csproj
echo     ^<DefineConstants^>USE_XML_DATABASE^</DefineConstants^> >> InventoryApp-Temp.csproj
echo   ^</PropertyGroup^> >> InventoryApp-Temp.csproj
echo   ^<ItemGroup^> >> InventoryApp-Temp.csproj
echo     ^<Reference Include="System" /^> >> InventoryApp-Temp.csproj
echo     ^<Reference Include="System.Data" /^> >> InventoryApp-Temp.csproj
echo     ^<Reference Include="System.Drawing" /^> >> InventoryApp-Temp.csproj
echo     ^<Reference Include="System.Windows.Forms" /^> >> InventoryApp-Temp.csproj
echo     ^<Reference Include="System.Xml" /^> >> InventoryApp-Temp.csproj
echo   ^</ItemGroup^> >> InventoryApp-Temp.csproj
echo   ^<ItemGroup^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Program.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Properties\AssemblyInfo.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Models\Article.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Models\InventoryItem.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Data\DatabaseHelper-Simple.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Services\BarcodeService.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Services\InventoryService-Simple.cs" /^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\MainForm.cs"^> >> InventoryApp-Temp.csproj
echo       ^<SubType^>Form^</SubType^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\MainForm.Designer.cs"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>MainForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\ArticleForm.cs"^> >> InventoryApp-Temp.csproj
echo       ^<SubType^>Form^</SubType^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\ArticleForm.Designer.cs"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>ArticleForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\InventoryForm.cs"^> >> InventoryApp-Temp.csproj
echo       ^<SubType^>Form^</SubType^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\InventoryForm.Designer.cs"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>InventoryForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\ReportsForm.cs"^> >> InventoryApp-Temp.csproj
echo       ^<SubType^>Form^</SubType^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo     ^<Compile Include="Forms\ReportsForm.Designer.cs"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>ReportsForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</Compile^> >> InventoryApp-Temp.csproj
echo   ^</ItemGroup^> >> InventoryApp-Temp.csproj
echo   ^<ItemGroup^> >> InventoryApp-Temp.csproj
echo     ^<EmbeddedResource Include="Forms\MainForm.resx"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>MainForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</EmbeddedResource^> >> InventoryApp-Temp.csproj
echo     ^<EmbeddedResource Include="Forms\ArticleForm.resx"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>ArticleForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</EmbeddedResource^> >> InventoryApp-Temp.csproj
echo     ^<EmbeddedResource Include="Forms\InventoryForm.resx"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>InventoryForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</EmbeddedResource^> >> InventoryApp-Temp.csproj
echo     ^<EmbeddedResource Include="Forms\ReportsForm.resx"^> >> InventoryApp-Temp.csproj
echo       ^<DependentUpon^>ReportsForm.cs^</DependentUpon^> >> InventoryApp-Temp.csproj
echo     ^</EmbeddedResource^> >> InventoryApp-Temp.csproj
echo   ^</ItemGroup^> >> InventoryApp-Temp.csproj
echo   ^<Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" /^> >> InventoryApp-Temp.csproj
echo ^</Project^> >> InventoryApp-Temp.csproj

echo Fichier projet cree: InventoryApp-Temp.csproj
echo.

echo Compilation avec MSBuild...
%MSBUILD_PATH% InventoryApp-Temp.csproj /p:Configuration=Release /p:Platform=AnyCPU /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERREUR: Compilation echouee avec MSBuild
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo DEPLOIEMENT SUR MC2100:
    echo 1. Copier bin\Release\InventoryApp.exe vers le terminal
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Inventaire MC2100" "bin\Release\InventoryApp.exe"
    )
) else (
    echo ERREUR: Fichier executable non genere
)

REM Nettoyer le fichier projet temporaire
del InventoryApp-Temp.csproj >nul 2>&1

echo.
echo Compilation terminee !
pause
