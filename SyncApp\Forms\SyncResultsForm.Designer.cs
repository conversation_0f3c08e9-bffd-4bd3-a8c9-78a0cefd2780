using System;
using System.Drawing;
using System.Windows.Forms;

namespace SyncApp.Forms
{
    partial class SyncResultsForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new System.Windows.Forms.Panel();
            this.lblTitle = new System.Windows.Forms.Label();
            
            this.pnlStats = new System.Windows.Forms.Panel();
            this.lblTotalItems = new System.Windows.Forms.Label();
            this.lblNewItems = new System.Windows.Forms.Label();
            this.lblModifiedItems = new System.Windows.Forms.Label();
            this.lblSynchronizedItems = new System.Windows.Forms.Label();
            this.lblConflictItems = new System.Windows.Forms.Label();
            this.lblErrorItems = new System.Windows.Forms.Label();
            
            this.pnlFilters = new System.Windows.Forms.Panel();
            this.lblStatusFilter = new System.Windows.Forms.Label();
            this.cmbStatusFilter = new System.Windows.Forms.ComboBox();
            this.lblSearch = new System.Windows.Forms.Label();
            this.txtSearch = new System.Windows.Forms.TextBox();
            
            this.pnlGrid = new System.Windows.Forms.Panel();
            this.dgvResults = new System.Windows.Forms.DataGridView();
            this.colBarcode = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCategory = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colPrice = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colStock = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSource = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colStatus = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colComments = new System.Windows.Forms.DataGridViewTextBoxColumn();
            
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnExport = new System.Windows.Forms.Button();
            this.btnClose = new System.Windows.Forms.Button();
            
            this.pnlHeader.SuspendLayout();
            this.pnlStats.SuspendLayout();
            this.pnlFilters.SuspendLayout();
            this.pnlGrid.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvResults)).BeginInit();
            this.pnlButtons.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // pnlHeader
            // 
            this.pnlHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(48)))));
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlHeader.Location = new System.Drawing.Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new System.Drawing.Size(1000, 60);
            this.pnlHeader.TabIndex = 0;
            
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(20, 18);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(280, 25);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "📊 Résultats de Synchronisation";
            
            // 
            // pnlStats
            // 
            this.pnlStats.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(248)))), ((int)(((byte)(249)))), ((int)(((byte)(250)))));
            this.pnlStats.Controls.Add(this.lblErrorItems);
            this.pnlStats.Controls.Add(this.lblConflictItems);
            this.pnlStats.Controls.Add(this.lblSynchronizedItems);
            this.pnlStats.Controls.Add(this.lblModifiedItems);
            this.pnlStats.Controls.Add(this.lblNewItems);
            this.pnlStats.Controls.Add(this.lblTotalItems);
            this.pnlStats.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlStats.Location = new System.Drawing.Point(0, 60);
            this.pnlStats.Name = "pnlStats";
            this.pnlStats.Padding = new System.Windows.Forms.Padding(20);
            this.pnlStats.Size = new System.Drawing.Size(1000, 80);
            this.pnlStats.TabIndex = 1;
            
            // 
            // lblTotalItems
            // 
            this.lblTotalItems.AutoSize = true;
            this.lblTotalItems.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold);
            this.lblTotalItems.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.lblTotalItems.Location = new System.Drawing.Point(23, 23);
            this.lblTotalItems.Name = "lblTotalItems";
            this.lblTotalItems.Size = new System.Drawing.Size(120, 20);
            this.lblTotalItems.TabIndex = 0;
            this.lblTotalItems.Text = "Total: 0 articles";
            
            // 
            // lblNewItems
            // 
            this.lblNewItems.AutoSize = true;
            this.lblNewItems.ForeColor = System.Drawing.Color.Blue;
            this.lblNewItems.Location = new System.Drawing.Point(13, 33);
            this.lblNewItems.Name = "lblNewItems";
            this.lblNewItems.Size = new System.Drawing.Size(70, 15);
            this.lblNewItems.TabIndex = 1;
            this.lblNewItems.Text = "Nouveaux: 0";
            
            // 
            // lblModifiedItems
            // 
            this.lblModifiedItems.AutoSize = true;
            this.lblModifiedItems.ForeColor = System.Drawing.Color.Orange;
            this.lblModifiedItems.Location = new System.Drawing.Point(120, 33);
            this.lblModifiedItems.Name = "lblModifiedItems";
            this.lblModifiedItems.Size = new System.Drawing.Size(65, 15);
            this.lblModifiedItems.TabIndex = 2;
            this.lblModifiedItems.Text = "Modifiés: 0";
            
            // 
            // lblSynchronizedItems
            // 
            this.lblSynchronizedItems.AutoSize = true;
            this.lblSynchronizedItems.ForeColor = System.Drawing.Color.Green;
            this.lblSynchronizedItems.Location = new System.Drawing.Point(220, 33);
            this.lblSynchronizedItems.Name = "lblSynchronizedItems";
            this.lblSynchronizedItems.Size = new System.Drawing.Size(85, 15);
            this.lblSynchronizedItems.TabIndex = 3;
            this.lblSynchronizedItems.Text = "Synchronisés: 0";
            
            // 
            // lblConflictItems
            // 
            this.lblConflictItems.AutoSize = true;
            this.lblConflictItems.ForeColor = System.Drawing.Color.Red;
            this.lblConflictItems.Location = new System.Drawing.Point(340, 33);
            this.lblConflictItems.Name = "lblConflictItems";
            this.lblConflictItems.Size = new System.Drawing.Size(60, 15);
            this.lblConflictItems.TabIndex = 4;
            this.lblConflictItems.Text = "Conflits: 0";
            
            // 
            // lblErrorItems
            // 
            this.lblErrorItems.AutoSize = true;
            this.lblErrorItems.ForeColor = System.Drawing.Color.Red;
            this.lblErrorItems.Location = new System.Drawing.Point(430, 33);
            this.lblErrorItems.Name = "lblErrorItems";
            this.lblErrorItems.Size = new System.Drawing.Size(55, 15);
            this.lblErrorItems.TabIndex = 5;
            this.lblErrorItems.Text = "Erreurs: 0";
            
            // 
            // pnlFilters
            // 
            this.pnlFilters.BackColor = System.Drawing.Color.White;
            this.pnlFilters.Controls.Add(this.txtSearch);
            this.pnlFilters.Controls.Add(this.lblSearch);
            this.pnlFilters.Controls.Add(this.cmbStatusFilter);
            this.pnlFilters.Controls.Add(this.lblStatusFilter);
            this.pnlFilters.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlFilters.Location = new System.Drawing.Point(0, 140);
            this.pnlFilters.Name = "pnlFilters";
            this.pnlFilters.Padding = new System.Windows.Forms.Padding(20);
            this.pnlFilters.Size = new System.Drawing.Size(1000, 60);
            this.pnlFilters.TabIndex = 2;
            
            // 
            // lblStatusFilter
            // 
            this.lblStatusFilter.AutoSize = true;
            this.lblStatusFilter.Location = new System.Drawing.Point(13, 18);
            this.lblStatusFilter.Name = "lblStatusFilter";
            this.lblStatusFilter.Size = new System.Drawing.Size(80, 15);
            this.lblStatusFilter.TabIndex = 0;
            this.lblStatusFilter.Text = "Filtrer par statut:";
            
            // 
            // cmbStatusFilter
            // 
            this.cmbStatusFilter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbStatusFilter.Location = new System.Drawing.Point(100, 15);
            this.cmbStatusFilter.Name = "cmbStatusFilter";
            this.cmbStatusFilter.Size = new System.Drawing.Size(150, 23);
            this.cmbStatusFilter.TabIndex = 1;
            this.cmbStatusFilter.SelectedIndexChanged += new System.EventHandler(this.cmbStatusFilter_SelectedIndexChanged);
            
            // 
            // lblSearch
            // 
            this.lblSearch.AutoSize = true;
            this.lblSearch.Location = new System.Drawing.Point(280, 18);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new System.Drawing.Size(60, 15);
            this.lblSearch.TabIndex = 2;
            this.lblSearch.Text = "Rechercher:";
            
            // 
            // txtSearch
            // 
            this.txtSearch.Location = new System.Drawing.Point(350, 15);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.Size = new System.Drawing.Size(200, 23);
            this.txtSearch.TabIndex = 3;
            this.txtSearch.TextChanged += new System.EventHandler(this.txtSearch_TextChanged);
            
            // 
            // pnlGrid
            // 
            this.pnlGrid.Controls.Add(this.dgvResults);
            this.pnlGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlGrid.Location = new System.Drawing.Point(0, 160);
            this.pnlGrid.Name = "pnlGrid";
            this.pnlGrid.Padding = new System.Windows.Forms.Padding(10);
            this.pnlGrid.Size = new System.Drawing.Size(1000, 340);
            this.pnlGrid.TabIndex = 3;
            
            // 
            // dgvResults
            // 
            this.dgvResults.AllowUserToAddRows = false;
            this.dgvResults.AllowUserToDeleteRows = false;
            this.dgvResults.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvResults.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvResults.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colBarcode,
            this.colName,
            this.colCategory,
            this.colPrice,
            this.colStock,
            this.colSource,
            this.colStatus,
            this.colComments});
            this.dgvResults.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvResults.Location = new System.Drawing.Point(10, 10);
            this.dgvResults.Name = "dgvResults";
            this.dgvResults.ReadOnly = true;
            this.dgvResults.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvResults.Size = new System.Drawing.Size(980, 320);
            this.dgvResults.TabIndex = 0;
            this.dgvResults.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvResults_CellDoubleClick);
            
            // 
            // colBarcode
            // 
            this.colBarcode.HeaderText = "Code-barres";
            this.colBarcode.Name = "colBarcode";
            this.colBarcode.ReadOnly = true;
            
            // 
            // colName
            // 
            this.colName.HeaderText = "Nom";
            this.colName.Name = "colName";
            this.colName.ReadOnly = true;
            
            // 
            // colCategory
            // 
            this.colCategory.HeaderText = "Catégorie";
            this.colCategory.Name = "colCategory";
            this.colCategory.ReadOnly = true;
            
            // 
            // colPrice
            // 
            this.colPrice.HeaderText = "Prix";
            this.colPrice.Name = "colPrice";
            this.colPrice.ReadOnly = true;
            
            // 
            // colStock
            // 
            this.colStock.HeaderText = "Stock";
            this.colStock.Name = "colStock";
            this.colStock.ReadOnly = true;
            
            // 
            // colSource
            // 
            this.colSource.HeaderText = "Source";
            this.colSource.Name = "colSource";
            this.colSource.ReadOnly = true;
            
            // 
            // colStatus
            // 
            this.colStatus.HeaderText = "Statut";
            this.colStatus.Name = "colStatus";
            this.colStatus.ReadOnly = true;
            
            // 
            // colComments
            // 
            this.colComments.HeaderText = "Commentaires";
            this.colComments.Name = "colComments";
            this.colComments.ReadOnly = true;
            
            // 
            // pnlButtons
            // 
            this.pnlButtons.Controls.Add(this.btnClose);
            this.pnlButtons.Controls.Add(this.btnExport);
            this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlButtons.Location = new System.Drawing.Point(0, 500);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Padding = new System.Windows.Forms.Padding(10);
            this.pnlButtons.Size = new System.Drawing.Size(1000, 50);
            this.pnlButtons.TabIndex = 4;
            
            // 
            // btnExport
            // 
            this.btnExport.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.btnExport.Location = new System.Drawing.Point(13, 13);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(120, 24);
            this.btnExport.TabIndex = 0;
            this.btnExport.Text = "📄 Exporter CSV";
            this.btnExport.UseVisualStyleBackColor = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            
            // 
            // btnClose
            // 
            this.btnClose.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClose.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.btnClose.Location = new System.Drawing.Point(877, 13);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(100, 24);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "❌ Fermer";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            
            // 
            // SyncResultsForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Controls.Add(this.pnlGrid);
            this.Controls.Add(this.pnlButtons);
            this.Controls.Add(this.pnlFilters);
            this.Controls.Add(this.pnlStats);
            this.Controls.Add(this.pnlHeader);
            this.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.Name = "SyncResultsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Résultats de Synchronisation";
            
            this.pnlHeader.ResumeLayout(false);
            this.pnlHeader.PerformLayout();
            this.pnlStats.ResumeLayout(false);
            this.pnlStats.PerformLayout();
            this.pnlFilters.ResumeLayout(false);
            this.pnlFilters.PerformLayout();
            this.pnlGrid.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvResults)).EndInit();
            this.pnlButtons.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.Panel pnlHeader;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Panel pnlStats;
        private System.Windows.Forms.Label lblTotalItems;
        private System.Windows.Forms.Label lblNewItems;
        private System.Windows.Forms.Label lblModifiedItems;
        private System.Windows.Forms.Label lblSynchronizedItems;
        private System.Windows.Forms.Label lblConflictItems;
        private System.Windows.Forms.Label lblErrorItems;
        private System.Windows.Forms.Panel pnlFilters;
        private System.Windows.Forms.Label lblStatusFilter;
        private System.Windows.Forms.ComboBox cmbStatusFilter;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Panel pnlGrid;
        private System.Windows.Forms.DataGridView dgvResults;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBarcode;
        private System.Windows.Forms.DataGridViewTextBoxColumn colName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCategory;
        private System.Windows.Forms.DataGridViewTextBoxColumn colPrice;
        private System.Windows.Forms.DataGridViewTextBoxColumn colStock;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSource;
        private System.Windows.Forms.DataGridViewTextBoxColumn colStatus;
        private System.Windows.Forms.DataGridViewTextBoxColumn colComments;
        private System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnClose;
    }
}



