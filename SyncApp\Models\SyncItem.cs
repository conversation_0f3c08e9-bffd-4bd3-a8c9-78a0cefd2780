using System;

namespace SyncApp.Models
{
    /// <summary>
    /// Élément de synchronisation représentant un article d'inventaire
    /// </summary>
    public class SyncItem
    {
        /// <summary>
        /// ID unique de l'article
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Code-barres de l'article
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// Nom de l'article
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Description de l'article
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Catégorie de l'article
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Prix unitaire en Dinar Tunisien
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Quantité en stock
        /// </summary>
        public int StockQuantity { get; set; }

        /// <summary>
        /// Stock minimum
        /// </summary>
        public int MinimumStock { get; set; }

        /// <summary>
        /// Unité de mesure
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// Emplacement de stockage
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Date de création
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date de dernière modification
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// Source des données (Terminal, Laptop, etc.)
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Statut de synchronisation
        /// </summary>
        public SyncStatus Status { get; set; }

        /// <summary>
        /// Commentaires de synchronisation
        /// </summary>
        public string SyncComments { get; set; }

        /// <summary>
        /// Constructeur par défaut
        /// </summary>
        public SyncItem()
        {
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            Status = SyncStatus.Pending;
            Unit = "pièce";
        }

        /// <summary>
        /// Retourne une représentation textuelle de l'article
        /// </summary>
        public override string ToString()
        {
            return string.Format("{0} - {1} ({2} DT)", Barcode, Name, UnitPrice.ToString("0.000"));
        }
    }

    /// <summary>
    /// Statut de synchronisation d'un élément
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// En attente de synchronisation
        /// </summary>
        Pending,

        /// <summary>
        /// Synchronisé avec succès
        /// </summary>
        Synchronized,

        /// <summary>
        /// Conflit détecté
        /// </summary>
        Conflict,

        /// <summary>
        /// Erreur de synchronisation
        /// </summary>
        Error,

        /// <summary>
        /// Nouveau (à créer)
        /// </summary>
        New,

        /// <summary>
        /// Modifié (à mettre à jour)
        /// </summary>
        Modified,

        /// <summary>
        /// Supprimé (à supprimer)
        /// </summary>
        Deleted
    }
}
