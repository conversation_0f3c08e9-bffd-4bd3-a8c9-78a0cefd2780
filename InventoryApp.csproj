<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>InventoryApp</RootNamespace>
    <AssemblyName>InventoryApp</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{4D628B5B-2FBC-4AA6-8C16-197242AEB884};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <PlatformFamilyName>WindowsCE</PlatformFamilyName>
    <PlatformID>E2BECB1F-8C8C-41ba-B736-9BE7D946A398</PlatformID>
    <OSVersion>6.0</OSVersion>
    <DeployDirSuffix>InventoryApp</DeployDirSuffix>
    <TargetFrameworkProfile />
    <FormFactorID>
    </FormFactorID>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="mscorlib" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SqlServerCe" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ArticleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ArticleForm.Designer.cs">
      <DependentUpon>ArticleForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InventoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InventoryForm.Designer.cs">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.Designer.cs">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\DatabaseHelper.cs" />
    <Compile Include="Data\InventoryContext.cs" />
    <Compile Include="Models\Article.cs" />
    <Compile Include="Models\InventoryItem.cs" />
    <Compile Include="Services\BarcodeService.cs" />
    <Compile Include="Services\InventoryService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ArticleForm.resx">
      <DependentUpon>ArticleForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\InventoryForm.resx">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReportsForm.resx">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\Images\" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CompactFramework.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}">
        <HostingProcess disable="1" />
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>
