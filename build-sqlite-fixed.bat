@echo off
REM Script de compilation SQLite corrige

echo ========================================
echo  BUILD SQLITE - INVENTAIRE MC2100
echo  Version SQLite avec Dinar Tunisien
echo ========================================
echo.

REM Test de la commande csc
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers ce dossier
    echo 3. Executer ce script
    echo.
    pause
    exit /b 1
)

echo Commande csc disponible !
echo.

REM Vérifier que SQLite est installé
if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo.
    echo SOLUTION:
    echo 1. Executer: install-sqlite.bat
    echo 2. Telecharger SQLite depuis:
    echo    https://system.data.sqlite.org/downloads.html
    echo 3. Copier System.Data.SQLite.dll dans lib\
    echo.
    pause
    exit /b 1
)

echo [OK] System.Data.SQLite.dll trouve
for %%I in ("lib\System.Data.SQLite.dll") do echo Taille: %%~zI octets
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation version SQLite en cours...
echo.

REM Compiler avec SQLite - COMMANDE SUR UNE SEULE LIGNE
csc /target:winexe /out:bin\Release\InventoryApp-SQLite.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite.cs Services\BarcodeService.cs Services\InventoryService-SQLite.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez que tous les fichiers sont presents.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION SQLITE REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp-SQLite.exe" (
    echo Fichier genere: bin\Release\InventoryApp-SQLite.exe
    
    for %%I in ("bin\Release\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets
    
    REM Copier la DLL SQLite nécessaire
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie dans bin\Release\
    
    echo.
    echo APPLICATION SQLITE COMPLETE AVEC:
    echo =================================
    echo • Base de donnees SQLite locale (InventoryDB.sqlite)
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete et fonctionnelle
    echo • Module inventaire simplifie mais operationnel
    echo • Rapports detailles (4 types complets)
    echo • Scanner codes-barres integre
    echo • Performance optimisee pour laptop
    echo • Compatible aussi avec MC2100
    echo.
    
    echo MODULES FONCTIONNELS:
    echo ====================
    echo 1. MENU PRINCIPAL: Statistiques en temps reel
    echo 2. GESTION ARTICLES: CRUD complet avec SQLite
    echo    - Creation, modification, suppression
    echo    - Recherche rapide par nom/code-barres
    echo    - Prix en Dinar Tunisien
    echo    - Categories et emplacements
    echo.
    echo 3. MODULE INVENTAIRE: Sessions d'inventaire
    echo    - Demarrage de sessions avec horodatage
    echo    - Scanner integre pour comptage
    echo    - Validation et commentaires
    echo.
    echo 4. RAPPORTS SQLITE: 4 types de rapports
    echo    - Rapport stock global avec valeurs DT
    echo    - Articles en rupture de stock
    echo    - Articles en stock bas
    echo    - Rapport par categorie avec statistiques
    echo.
    
    echo BASE DE DONNEES SQLITE:
    echo =======================
    echo • Fichier: InventoryDB.sqlite (cree automatiquement)
    echo • Emplacement: dossier de l'application
    echo • Sauvegarde: copier le fichier .sqlite
    echo • Performance: ultra-rapide en local
    echo • Fiabilite: transactions ACID
    echo.
    
    echo DEPLOIEMENT:
    echo ============
    echo LAPTOP (recommande):
    echo 1. Copier bin\Release\InventoryApp-SQLite.exe
    echo 2. Copier bin\Release\System.Data.SQLite.dll
    echo 3. Lancer l'application
    echo 4. Le fichier InventoryDB.sqlite se cree automatiquement
    echo.
    echo MC2100 (si compatible):
    echo 1. Verifier compatibilite SQLite sur Windows CE
    echo 2. Copier les memes fichiers
    echo 3. Tester le fonctionnement
    echo.
    
    echo Voulez-vous tester l'application SQLite maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement de l'application SQLite...
        start "Inventaire SQLite - Version Complete" "bin\Release\InventoryApp-SQLite.exe"
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo ===========================
        echo 1. Creation d'articles avec prix en DT
        echo 2. Recherche et modification d'articles
        echo 3. Generation des 4 types de rapports
        echo 4. Verification du fichier InventoryDB.sqlite
        echo.
        echo Le fichier de base sera cree dans:
        echo %CD%\bin\Release\InventoryDB.sqlite
        echo.
        echo Vous pouvez ouvrir ce fichier avec:
        echo • DB Browser for SQLite
        echo • SQLite Expert
        echo • Ou tout autre outil SQLite
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  APPLICATION SQLITE PRETE !
echo ========================================
echo.
echo Votre application d'inventaire avec SQLite est maintenant
echo complete et prete pour votre laptop !
echo.
echo AVANTAGES DE CETTE VERSION:
echo • Base de donnees professionnelle
echo • Performance optimale en local
echo • Sauvegarde simple (fichier .sqlite)
echo • Requetes SQL completes
echo • Devise tunisienne integree
echo.
echo Compilation terminee avec succes !
pause
