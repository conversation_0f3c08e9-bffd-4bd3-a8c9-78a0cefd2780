@echo off
REM Compilation progressive pour identifier les problemes

echo ========================================
echo  COMPILATION PROGRESSIVE SQLITE
echo ========================================
echo.

REM Vérifications préalables
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur non disponible
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    pause
    exit /b 1
)

echo [OK] Compilateur et SQLite disponibles
echo.

REM Créer le dossier de sortie
if not exist "bin" mkdir "bin"
if not exist "bin\Progressive" mkdir "bin\Progressive"

echo ETAPE 1: Compilation des modeles...
csc /target:library /out:bin\Progressive\Models.dll Models\Article.cs Models\InventoryItem.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Modeles - Compilation echouee
    goto :error
)
echo [OK] Modeles compiles

echo.
echo ETAPE 2: Compilation des utilitaires...
csc /target:library /out:bin\Progressive\Utils.dll /reference:System.dll Utils\CurrencyHelper.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Utilitaires - Compilation echouee
    goto :error
)
echo [OK] Utilitaires compiles

echo.
echo ETAPE 3: Compilation des services de base...
csc /target:library /out:bin\Progressive\Services.dll /reference:System.dll Services\BarcodeService.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Services de base - Compilation echouee
    goto :error
)
echo [OK] Services de base compiles

echo.
echo ETAPE 4: Compilation du helper SQLite...
csc /target:library /out:bin\Progressive\Database.dll /reference:System.dll /reference:System.Data.dll /reference:lib\System.Data.SQLite.dll /reference:bin\Progressive\Models.dll Data\DatabaseHelper-SQLite-Simple.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Helper SQLite - Compilation echouee
    goto :error
)
echo [OK] Helper SQLite compile

echo.
echo ETAPE 5: Compilation du service SQLite...
csc /target:library /out:bin\Progressive\InventoryService.dll /reference:System.dll /reference:bin\Progressive\Models.dll /reference:bin\Progressive\Database.dll Services\InventoryService-SQLite-Simple.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Service SQLite - Compilation echouee
    goto :error
)
echo [OK] Service SQLite compile

echo.
echo ETAPE 6: Compilation des formulaires...
csc /target:library /out:bin\Progressive\Forms.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:bin\Progressive\Models.dll /reference:bin\Progressive\InventoryService.dll /reference:bin\Progressive\Utils.dll /reference:bin\Progressive\Services.dll /define:USE_SQLITE_DATABASE Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Formulaires - Compilation echouee
    goto :error
)
echo [OK] Formulaires compiles

echo.
echo ETAPE 7: Compilation de l'application finale...
csc /target:winexe /out:bin\Progressive\InventoryApp-SQLite.exe /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:bin\Progressive\Models.dll /reference:bin\Progressive\Database.dll /reference:bin\Progressive\InventoryService.dll /reference:bin\Progressive\Utils.dll /reference:bin\Progressive\Services.dll /reference:bin\Progressive\Forms.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE Program.cs Properties\AssemblyInfo.cs
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Application finale - Compilation echouee
    goto :error
)
echo [OK] Application finale compilee

echo.
echo ========================================
echo  COMPILATION PROGRESSIVE REUSSIE !
echo ========================================
echo.

REM Copier les DLL nécessaires
copy "lib\System.Data.SQLite.dll" "bin\Progressive\" >nul
copy "bin\Progressive\*.dll" "bin\Progressive\" >nul

echo Fichier genere: bin\Progressive\InventoryApp-SQLite.exe
for %%I in ("bin\Progressive\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets

echo.
echo MODULES COMPILES SEPAREMENT:
echo ============================
echo • Models.dll - Modeles de donnees
echo • Utils.dll - Utilitaires (devise DT)
echo • Services.dll - Services de base
echo • Database.dll - Helper SQLite
echo • InventoryService.dll - Service metier
echo • Forms.dll - Interface utilisateur
echo • InventoryApp-SQLite.exe - Application principale
echo.

echo Voulez-vous tester l'application ? (O/N)
set /p TEST_APP=
if /i "%TEST_APP%"=="O" (
    echo Lancement de l'application...
    start "Inventaire SQLite - Progressive" "bin\Progressive\InventoryApp-SQLite.exe"
)

echo.
echo Si cette compilation fonctionne, le probleme venait
echo de la ligne de commande trop longue dans les autres scripts.
echo.
goto :end

:error
echo.
echo ========================================
echo  ERREUR DE COMPILATION
echo ========================================
echo.
echo La compilation a echoue a une etape specifique.
echo Verifiez les erreurs affichees ci-dessus.
echo.
echo SOLUTIONS:
echo 1. Corriger les erreurs de syntaxe dans les fichiers
echo 2. Verifier les references manquantes
echo 3. S'assurer que tous les fichiers existent
echo.

:end
echo.
pause
