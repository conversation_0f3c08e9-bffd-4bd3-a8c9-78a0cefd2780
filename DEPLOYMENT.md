# Guide de Déploiement - Application Inventaire Zebra MC2100

## Prérequis

### Environnement de développement
- **Visual Studio 2008/2010** avec support Windows CE
- **.NET Compact Framework 3.5 SDK**
- **Windows CE Platform SDK**
- **ActiveSync ou Windows Mobile Device Center** pour le déploiement

### Terminal Zebra MC2100
- **OS**: Windows CE 6.0
- **RAM**: 128 MB minimum
- **.NET Compact Framework 3.5** installé
- **SQL Server Compact Edition** runtime

## Compilation

### 1. Configuration du projet
```
Configuration: Release
Platform: Windows CE
Target Framework: .NET Compact Framework 3.5
```

### 2. Compilation
1. Ouvrir `InventoryApp.sln` dans Visual Studio
2. Sélectionner la configuration "Release|Windows CE"
3. Compiler le projet (Build → Build Solution)
4. Vérifier qu'aucune erreur n'est présente

### 3. Fichiers générés
Après compilation, les fichiers suivants sont créés dans `bin\Release\` :
- `InventoryApp.exe` - Application principale
- `InventoryApp.exe.config` - Configuration (si présent)
- Dépendances .NET Compact Framework

## Déploiement sur Zebra MC2100

### Méthode 1: Déploiement direct via Visual Studio
1. Connecter le MC2100 via ActiveSync/WMDC
2. Dans Visual Studio: Debug → Start Without Debugging
3. L'application sera automatiquement déployée et lancée

### Méthode 2: Déploiement manuel
1. Copier les fichiers compilés vers le terminal :
   ```
   \Program Files\InventoryApp\
   ├── InventoryApp.exe
   ├── InventoryApp.exe.config (si présent)
   └── InventoryDB.sdf (créé automatiquement)
   ```

2. Créer un raccourci sur le bureau ou dans le menu Démarrer

### Méthode 3: Package CAB
1. Créer un projet de déploiement CAB dans Visual Studio
2. Ajouter les fichiers de l'application
3. Compiler le package CAB
4. Installer le CAB sur le terminal

## Configuration

### Base de données
- La base de données SQLite CE est créée automatiquement au premier lancement
- Emplacement: `\Program Files\InventoryApp\InventoryDB.sdf`
- Aucune configuration manuelle requise

### Scanner de codes-barres
- Le scanner est activé automatiquement
- Configuration par défaut compatible MC2100
- Aucun driver supplémentaire requis

## Structure des dossiers sur le terminal

```
\Program Files\InventoryApp\
├── InventoryApp.exe           # Application principale
├── InventoryDB.sdf           # Base de données (créée automatiquement)
├── Logs\                     # Logs d'erreur (optionnel)
└── Backup\                   # Sauvegardes (optionnel)
```

## Vérification du déploiement

### 1. Test de lancement
- Lancer l'application depuis le menu ou raccourci
- Vérifier que l'interface s'affiche correctement
- Contrôler que la base de données est créée

### 2. Test du scanner
- Scanner un code-barres de test
- Vérifier que le code apparaît dans l'interface
- Tester la recherche d'articles

### 3. Test des fonctionnalités
- Créer un article de test
- Effectuer un inventaire simple
- Vérifier la sauvegarde des données

## Dépannage

### Problèmes courants

#### Application ne démarre pas
- Vérifier que .NET CF 3.5 est installé
- Contrôler les permissions d'écriture
- Vérifier l'espace disque disponible

#### Scanner ne fonctionne pas
- Redémarrer l'application
- Vérifier les paramètres du scanner dans Windows CE
- Tester avec un autre code-barres

#### Base de données inaccessible
- Vérifier les permissions du dossier
- Contrôler que SQL CE est installé
- Redémarrer le terminal si nécessaire

### Logs d'erreur
Les erreurs sont affichées via MessageBox. Pour un déploiement en production, 
considérer l'ajout d'un système de logs dans un fichier.

## Mise à jour

### Procédure de mise à jour
1. Fermer l'application existante
2. Sauvegarder la base de données (`InventoryDB.sdf`)
3. Remplacer `InventoryApp.exe` par la nouvelle version
4. Relancer l'application
5. Vérifier la compatibilité des données

### Sauvegarde des données
```
Avant mise à jour:
1. Copier InventoryDB.sdf vers un dossier de sauvegarde
2. Exporter les données si nécessaire
3. Noter la version actuelle
```

## Performance et optimisation

### Recommandations
- Nettoyer régulièrement la base de données
- Limiter le nombre d'articles affichés simultanément
- Effectuer des sauvegardes périodiques
- Redémarrer l'application quotidiennement

### Limites techniques
- **Articles maximum recommandé**: 10,000
- **Mémoire RAM**: 128 MB (64 MB utilisables)
- **Stockage**: Minimum 50 MB libres
- **Performance scanner**: ~2-3 scans/seconde

## Support et maintenance

### Contacts techniques
- Développeur: [Votre nom/contact]
- Documentation: README.md
- Version: 1.0.0

### Maintenance préventive
- Sauvegarde hebdomadaire des données
- Vérification mensuelle de l'espace disque
- Mise à jour trimestrielle si nécessaire
- Formation utilisateurs semestrielle
