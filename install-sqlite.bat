@echo off
REM Script pour installer SQLite et System.Data.SQLite

echo ========================================
echo  INSTALLATION SQLITE POUR .NET
echo ========================================
echo.

echo Ce script va installer SQLite pour votre application .NET
echo.

echo ETAPE 1: Verification de l'environnement
echo =========================================
echo.

REM Vérifier si on est dans le bon dossier
if not exist "Program.cs" (
    echo ERREUR: Vous n'etes pas dans le dossier du projet
    echo Naviguez vers: cd "C:\Users\<USER>\Desktop\Inventaire"
    pause
    exit /b 1
)

echo [OK] Dossier du projet trouve
echo.

echo ETAPE 2: Creation du dossier lib
echo =================================
if not exist "lib" mkdir "lib"
echo [OK] Dossier lib cree
echo.

echo ETAPE 3: Instructions pour telecharger SQLite
echo ==============================================
echo.
echo TELECHARGEMENT MANUEL REQUIS:
echo.
echo 1. Aller sur: https://system.data.sqlite.org/index.html/doc/trunk/www/downloads.wiki
echo.
echo 2. Telecharger "Precompiled Binaries for 32-bit Windows (.NET Framework 4.6)"
echo    Fichier: sqlite-netFx46-binary-Win32-2015-*********.zip
echo.
echo 3. OU telecharger "Precompiled Binaries for 64-bit Windows (.NET Framework 4.6)"
echo    Fichier: sqlite-netFx46-binary-x64-2015-*********.zip
echo.
echo 4. Extraire le fichier ZIP
echo.
echo 5. Copier ces fichiers dans le dossier lib\ :
echo    - System.Data.SQLite.dll
echo    - System.Data.SQLite.xml (optionnel)
echo.

echo ETAPE 4: Alternative - Installation via NuGet (si disponible)
echo =============================================================
echo.
echo Si vous avez NuGet Package Manager:
echo 1. Ouvrir Visual Studio 2022
echo 2. Ouvrir votre projet
echo 3. Tools > NuGet Package Manager > Package Manager Console
echo 4. Taper: Install-Package System.Data.SQLite
echo.

echo ETAPE 5: Verification de l'installation
echo ========================================
echo.
if exist "lib\System.Data.SQLite.dll" (
    echo [OK] System.Data.SQLite.dll trouve dans lib\
    
    REM Obtenir la taille du fichier
    for %%I in ("lib\System.Data.SQLite.dll") do echo Taille: %%~zI octets
    
    echo.
    echo SQLite est pret pour la compilation !
    echo.
    echo PROCHAINES ETAPES:
    echo 1. Executer: build-sqlite.bat
    echo 2. Tester l'application avec la base SQLite
    echo.
) else (
    echo [MANQUE] System.Data.SQLite.dll non trouve
    echo.
    echo ACTIONS REQUISES:
    echo 1. Telecharger SQLite depuis le lien ci-dessus
    echo 2. Copier System.Data.SQLite.dll dans le dossier lib\
    echo 3. Relancer ce script pour verification
    echo.
)

echo ETAPE 6: Creation du script de compilation SQLite
echo ==================================================
echo.

REM Créer le script de compilation pour SQLite
echo @echo off > build-sqlite.bat
echo REM Script de compilation avec SQLite >> build-sqlite.bat
echo. >> build-sqlite.bat
echo echo Compilation avec base SQLite... >> build-sqlite.bat
echo. >> build-sqlite.bat
echo csc /target:winexe ^^ >> build-sqlite.bat
echo     /out:bin\Release\InventoryApp-SQLite.exe ^^ >> build-sqlite.bat
echo     /reference:System.dll ^^ >> build-sqlite.bat
echo     /reference:System.Data.dll ^^ >> build-sqlite.bat
echo     /reference:System.Drawing.dll ^^ >> build-sqlite.bat
echo     /reference:System.Windows.Forms.dll ^^ >> build-sqlite.bat
echo     /reference:System.Xml.dll ^^ >> build-sqlite.bat
echo     /reference:Microsoft.VisualBasic.dll ^^ >> build-sqlite.bat
echo     /reference:lib\System.Data.SQLite.dll ^^ >> build-sqlite.bat
echo     /define:USE_SQLITE_DATABASE ^^ >> build-sqlite.bat
echo     /nowarn:0168,0219 ^^ >> build-sqlite.bat
echo     Program.cs ^^ >> build-sqlite.bat
echo     "Properties\AssemblyInfo.cs" ^^ >> build-sqlite.bat
echo     "Models\Article.cs" ^^ >> build-sqlite.bat
echo     "Models\InventoryItem.cs" ^^ >> build-sqlite.bat
echo     "Data\DatabaseHelper-SQLite.cs" ^^ >> build-sqlite.bat
echo     "Services\BarcodeService.cs" ^^ >> build-sqlite.bat
echo     "Services\InventoryService-SQLite.cs" ^^ >> build-sqlite.bat
echo     "Utils\CurrencyHelper.cs" ^^ >> build-sqlite.bat
echo     "Forms\MainForm.cs" ^^ >> build-sqlite.bat
echo     "Forms\MainForm.Designer.cs" ^^ >> build-sqlite.bat
echo     "Forms\ArticleForm.cs" ^^ >> build-sqlite.bat
echo     "Forms\ArticleForm.Designer.cs" ^^ >> build-sqlite.bat
echo     "Forms\InventoryForm-Simple.cs" ^^ >> build-sqlite.bat
echo     "Forms\ReportsForm.cs" ^^ >> build-sqlite.bat
echo     "Forms\ReportsForm.Designer.cs" >> build-sqlite.bat
echo. >> build-sqlite.bat
echo if %%ERRORLEVEL%% equ 0 ^( >> build-sqlite.bat
echo     echo [SUCCES] Application SQLite compilee ! >> build-sqlite.bat
echo     echo Fichier: bin\Release\InventoryApp-SQLite.exe >> build-sqlite.bat
echo     echo. >> build-sqlite.bat
echo     echo IMPORTANT: Copier aussi lib\System.Data.SQLite.dll >> build-sqlite.bat
echo     echo dans le meme dossier que l'executable ! >> build-sqlite.bat
echo     echo. >> build-sqlite.bat
echo     if exist "bin\Release\InventoryApp-SQLite.exe" ^( >> build-sqlite.bat
echo         copy "lib\System.Data.SQLite.dll" "bin\Release\" >> build-sqlite.bat
echo         echo [OK] System.Data.SQLite.dll copie >> build-sqlite.bat
echo         echo. >> build-sqlite.bat
echo         echo Voulez-vous tester l'application ? ^(O/N^) >> build-sqlite.bat
echo         set /p TEST_APP= >> build-sqlite.bat
echo         if /i "%%TEST_APP%%"=="O" ^( >> build-sqlite.bat
echo             start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe" >> build-sqlite.bat
echo         ^) >> build-sqlite.bat
echo     ^) >> build-sqlite.bat
echo ^) else ^( >> build-sqlite.bat
echo     echo [ERREUR] Compilation echouee >> build-sqlite.bat
echo ^) >> build-sqlite.bat
echo. >> build-sqlite.bat
echo pause >> build-sqlite.bat

echo [OK] Script build-sqlite.bat cree
echo.

echo ========================================
echo  RESUME DE L'INSTALLATION
echo ========================================
echo.
echo STATUT ACTUEL:
if exist "lib\System.Data.SQLite.dll" (
    echo [OK] SQLite pret pour compilation
    echo.
    echo PROCHAINE ETAPE:
    echo Dans Developer Command Prompt for VS 2022:
    echo build-sqlite.bat
) else (
    echo [MANQUE] SQLite non installe
    echo.
    echo ACTIONS REQUISES:
    echo 1. Telecharger SQLite depuis:
    echo    https://system.data.sqlite.org/downloads.html
    echo 2. Copier System.Data.SQLite.dll dans lib\
    echo 3. Executer build-sqlite.bat
)

echo.
echo AVANTAGES DE SQLITE:
echo • Base de donnees locale (fichier .sqlite)
echo • Pas de serveur requis
echo • Performances excellentes
echo • Transactions ACID
echo • Compatible avec votre laptop et MC2100
echo.

pause
