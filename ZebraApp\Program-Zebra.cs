using System;
using System.Windows.Forms;
using ZebraApp.Forms;

namespace ZebraApp
{
    /// <summary>
    /// Application d'inventaire pour Zebra MC2100
    /// Compatible .NET Compact Framework 3.5
    /// </summary>
    static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application Zebra
        /// </summary>
        [MTAThread]
        static void Main()
        {
            try
            {
                // Lancer le formulaire principal optimisé pour Zebra
                Application.Run(new ZebraMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    "Erreur de démarrage:\n" + ex.Message,
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Hand,
                    MessageBoxDefaultButton.Button1);
            }
        }
    }
}
