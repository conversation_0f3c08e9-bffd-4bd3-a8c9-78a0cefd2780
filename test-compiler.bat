@echo off
REM Test simple du compilateur C#

echo ========================================
echo  TEST DU COMPILATEUR C#
echo ========================================
echo.

REM Rechercher le compilateur
set CSC_PATH=""

if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
    set CSC_PATH="%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    echo [TROUVE] Visual Studio 2022 Community
    echo Chemin: %CSC_PATH%
    goto :test_compiler
)

if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo [TROUVE] .NET Framework 4.0 (64-bit)
    echo Chemin: %CSC_PATH%
    goto :test_compiler
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo [TROUVE] .NET Framework 4.0
    echo Chemin: %CSC_PATH%
    goto :test_compiler
)

echo [ERREUR] Aucun compilateur trouve
goto :end

:test_compiler
echo.
echo Test du compilateur...

REM Créer un fichier de test simple
echo using System; > test.cs
echo class Test { >> test.cs
echo     static void Main() { >> test.cs
echo         Console.WriteLine("Compilateur OK !"); >> test.cs
echo     } >> test.cs
echo } >> test.cs

echo Fichier de test cree: test.cs
echo.

REM Tester la compilation
echo Compilation du test...
%CSC_PATH% /out:test.exe test.cs

if %ERRORLEVEL% equ 0 (
    echo [SUCCES] Compilation reussie !
    echo.
    
    echo Test d'execution...
    test.exe
    
    if %ERRORLEVEL% equ 0 (
        echo [SUCCES] Execution reussie !
        echo.
        echo ========================================
        echo  COMPILATEUR FONCTIONNEL !
        echo ========================================
        echo.
        echo Vous pouvez maintenant utiliser:
        echo - build-complete.bat
        echo - build-quick.bat
        echo.
    ) else (
        echo [ERREUR] Execution echouee
    )
    
    REM Nettoyer
    del test.exe >nul 2>&1
) else (
    echo [ERREUR] Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Le compilateur ne fonctionne pas correctement.
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Executer ce test depuis cette invite
)

REM Nettoyer
del test.cs >nul 2>&1

:end
echo.
pause
