@echo off
REM Compilation avec mode debug et verbose pour identifier le probleme

echo ========================================
echo  BUILD DEBUG VERBOSE
echo ========================================
echo.

csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande csc non disponible
    pause
    exit /b 1
)

if not exist "bin" mkdir "bin"
if not exist "bin\Debug" mkdir "bin\Debug"

echo Compilation avec mode debug et verbose...
echo.

REM Compilation avec plus d'informations de debug
csc /target:winexe ^
    /out:bin\Debug\InventoryApp.exe ^
    /debug+ ^
    /checked+ ^
    /warnaserror- ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /define:DEBUG ^
    /define:TRACE ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

echo.
echo Code de retour: %ERRORLEVEL%
echo.

if %ERRORLEVEL% equ 0 (
    echo ========================================
    echo  COMPILATION DEBUG REUSSIE !
    echo ========================================
    echo.
    echo Fichier genere: bin\Debug\InventoryApp.exe
    
    if exist "bin\Debug\InventoryApp.exe" (
        for %%I in ("bin\Debug\InventoryApp.exe") do echo Taille: %%~zI octets
        echo.
        echo Voulez-vous tester l'application ? (O/N)
        set /p TEST_APP=
        if /i "%TEST_APP%"=="O" (
            echo Lancement de l'application debug...
            start "Inventaire Debug" "bin\Debug\InventoryApp.exe"
        )
    )
) else (
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Le message d'erreur complet est affiche ci-dessus.
    echo.
    echo SOLUTIONS POSSIBLES:
    echo 1. Caractere special dans un fichier
    echo 2. Probleme d'encodage (UTF-8 vs ANSI)
    echo 3. Guillemets mal echappes
    echo 4. Caractere invisible (BOM, etc.)
    echo.
    echo DIAGNOSTIC:
    echo Executez compile-one-by-one.bat pour identifier
    echo le fichier problematique.
)

echo.
pause
