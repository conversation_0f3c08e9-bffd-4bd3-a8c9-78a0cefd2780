using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace InventoryApp.Services
{
    /// <summary>
    /// Service pour la gestion du scanner de codes-barres sur Zebra MC2100
    /// </summary>
    public class BarcodeService
    {
        #region Événements

        /// <summary>
        /// Événement déclenché lors de la lecture d'un code-barres
        /// </summary>
        public event EventHandler<BarcodeScannedEventArgs> BarcodeScanned;

        #endregion

        #region Propriétés

        /// <summary>
        /// Indique si le scanner est activé
        /// </summary>
        public bool IsEnabled { get; private set; }

        /// <summary>
        /// Dernier code-barres scanné
        /// </summary>
        public string LastScannedBarcode { get; private set; }

        #endregion

        #region Constructeur

        /// <summary>
        /// Constructeur du service de codes-barres
        /// </summary>
        public BarcodeService()
        {
            IsEnabled = false;
            LastScannedBarcode = string.Empty;
        }

        #endregion

        #region Méthodes publiques

        /// <summary>
        /// Active le scanner de codes-barres
        /// </summary>
        /// <returns>True si l'activation a réussi</returns>
        public bool EnableScanner()
        {
            try
            {
                // Pour Zebra MC2100, on utilise généralement les événements clavier
                // Le scanner envoie les données comme des frappes clavier suivies d'un Enter
                IsEnabled = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erreur lors de l'activation du scanner: " + ex.Message, 
                    "Erreur Scanner", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, 
                    MessageBoxDefaultButton.Button1);
                return false;
            }
        }

        /// <summary>
        /// Désactive le scanner de codes-barres
        /// </summary>
        /// <returns>True si la désactivation a réussi</returns>
        public bool DisableScanner()
        {
            try
            {
                IsEnabled = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erreur lors de la désactivation du scanner: " + ex.Message, 
                    "Erreur Scanner", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, 
                    MessageBoxDefaultButton.Button1);
                return false;
            }
        }

        /// <summary>
        /// Traite une entrée de code-barres (généralement appelée depuis un événement KeyPress)
        /// </summary>
        /// <param name="input">Données d'entrée</param>
        public void ProcessBarcodeInput(string input)
        {
            if (!IsEnabled || string.IsNullOrEmpty(input))
                return;

            // Nettoyer l'entrée (supprimer les caractères de contrôle)
            string cleanedInput = CleanBarcodeInput(input);
            
            if (!string.IsNullOrEmpty(cleanedInput))
            {
                LastScannedBarcode = cleanedInput;
                OnBarcodeScanned(new BarcodeScannedEventArgs(cleanedInput));
            }
        }

        /// <summary>
        /// Valide un code-barres
        /// </summary>
        /// <param name="barcode">Code-barres à valider</param>
        /// <returns>True si le code-barres est valide</returns>
        public bool ValidateBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return false;

            // Vérifications de base
            if (barcode.Length < 3 || barcode.Length > 50)
                return false;

            // Vérifier que le code-barres ne contient que des caractères autorisés
            foreach (char c in barcode)
            {
                if (!char.IsLetterOrDigit(c) && c != '-' && c != '_')
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Simule la lecture d'un code-barres (pour les tests)
        /// </summary>
        /// <param name="barcode">Code-barres à simuler</param>
        public void SimulateBarcodeScanned(string barcode)
        {
            if (ValidateBarcode(barcode))
            {
                ProcessBarcodeInput(barcode);
            }
        }

        #endregion

        #region Méthodes privées

        /// <summary>
        /// Nettoie l'entrée du code-barres
        /// </summary>
        /// <param name="input">Entrée brute</param>
        /// <returns>Entrée nettoyée</returns>
        private string CleanBarcodeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            StringBuilder cleaned = new StringBuilder();
            
            foreach (char c in input)
            {
                // Garder seulement les caractères imprimables
                if (c >= 32 && c <= 126)
                {
                    cleaned.Append(c);
                }
            }

            return cleaned.ToString().Trim();
        }

        /// <summary>
        /// Déclenche l'événement BarcodeScanned
        /// </summary>
        /// <param name="e">Arguments de l'événement</param>
        protected virtual void OnBarcodeScanned(BarcodeScannedEventArgs e)
        {
            BarcodeScanned?.Invoke(this, e);
        }

        #endregion

        #region Méthodes statiques utilitaires

        /// <summary>
        /// Génère un code-barres de test
        /// </summary>
        /// <returns>Code-barres de test</returns>
        public static string GenerateTestBarcode()
        {
            Random random = new Random();
            return "TEST" + random.Next(100000, 999999).ToString();
        }

        /// <summary>
        /// Vérifie si le périphérique supporte le scan de codes-barres
        /// </summary>
        /// <returns>True si supporté</returns>
        public static bool IsScannerSupported()
        {
            // Pour Zebra MC2100, on assume que le scanner est toujours disponible
            return true;
        }

        #endregion
    }

    /// <summary>
    /// Arguments d'événement pour la lecture d'un code-barres
    /// </summary>
    public class BarcodeScannedEventArgs : EventArgs
    {
        /// <summary>
        /// Code-barres scanné
        /// </summary>
        public string Barcode { get; private set; }

        /// <summary>
        /// Horodatage de la lecture
        /// </summary>
        public DateTime Timestamp { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="barcode">Code-barres scanné</param>
        public BarcodeScannedEventArgs(string barcode)
        {
            Barcode = barcode;
            Timestamp = DateTime.Now;
        }
    }
}
