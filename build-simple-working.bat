@echo off
REM Compilation simple qui fonctionne

echo ========================================
echo  BUILD SIMPLE SQLITE - VERSION WORKING
echo ========================================
echo.

REM Vérifications
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur non disponible
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    pause
    exit /b 1
)

echo [OK] Environnement pret
echo.

REM Créer dossier
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation en cours...
echo.

REM Créer un fichier de réponse pour éviter les lignes trop longues
echo /target:winexe > compile.rsp
echo /out:bin\Release\InventoryApp-SQLite.exe >> compile.rsp
echo /reference:System.dll >> compile.rsp
echo /reference:System.Data.dll >> compile.rsp
echo /reference:System.Drawing.dll >> compile.rsp
echo /reference:System.Windows.Forms.dll >> compile.rsp
echo /reference:System.Xml.dll >> compile.rsp
echo /reference:Microsoft.VisualBasic.dll >> compile.rsp
echo /reference:lib\System.Data.SQLite.dll >> compile.rsp
echo /define:USE_SQLITE_DATABASE >> compile.rsp
echo /nowarn:0168,0219 >> compile.rsp
echo Program.cs >> compile.rsp
echo Properties\AssemblyInfo.cs >> compile.rsp
echo Models\Article.cs >> compile.rsp
echo Models\InventoryItem.cs >> compile.rsp
echo Data\DatabaseHelper-SQLite-Simple.cs >> compile.rsp
echo Services\BarcodeService.cs >> compile.rsp
echo Services\InventoryService-SQLite-Simple.cs >> compile.rsp
echo Utils\CurrencyHelper.cs >> compile.rsp
echo Forms\MainForm.cs >> compile.rsp
echo Forms\MainForm.Designer.cs >> compile.rsp
echo Forms\ArticleForm.cs >> compile.rsp
echo Forms\ArticleForm.Designer.cs >> compile.rsp
echo Forms\InventoryForm-Simple.cs >> compile.rsp
echo Forms\ReportsForm.cs >> compile.rsp
echo Forms\ReportsForm.Designer.cs >> compile.rsp

REM Compiler avec le fichier de réponse
csc @compile.rsp

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  COMPILATION REUSSIE !
    echo ========================================
    echo.
    
    REM Copier SQLite DLL
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier genere: bin\Release\InventoryApp-SQLite.exe
    for %%I in ("bin\Release\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION SQLITE COMPLETE AVEC:
    echo =================================
    echo • Base de donnees SQLite locale
    echo • Devise Dinar Tunisien (DT)
    echo • Gestion articles complete
    echo • Rapports detailles
    echo • Module inventaire
    echo • Scanner codes-barres
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement de l'application SQLite...
        start "Inventaire SQLite - Working" "bin\Release\InventoryApp-SQLite.exe"
        echo.
        echo TESTEZ:
        echo 1. Creation d'articles avec prix en DT
        echo 2. Recherche d'articles
        echo 3. Generation de rapports
        echo 4. Module inventaire
        echo.
        echo La base InventoryDB.sqlite sera creee automatiquement
        echo dans: bin\Release\InventoryDB.sqlite
    )
    
) else (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
)

REM Nettoyer
del compile.rsp >nul 2>&1

echo.
echo Compilation terminee.
pause
