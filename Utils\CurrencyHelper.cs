using System;
using System.Globalization;

namespace InventoryApp.Utils
{
    /// <summary>
    /// Helper pour la gestion de la devise Dinar Tunisien
    /// </summary>
    public static class CurrencyHelper
    {
        /// <summary>
        /// Symbole de la devise (Dinar Tunisien)
        /// </summary>
        public static readonly string CurrencySymbol = "DT";
        
        /// <summary>
        /// Format par défaut pour les montants
        /// </summary>
        public static readonly string DefaultFormat = "0.000";
        
        /// <summary>
        /// Culture tunisienne pour le formatage
        /// </summary>
        private static readonly CultureInfo TunisianCulture = new CultureInfo("ar-TN");
        
        /// <summary>
        /// Formate un montant en Dinar Tunisien
        /// </summary>
        /// <param name="amount">Montant à formater</param>
        /// <returns>Montant formaté avec la devise</returns>
        public static string FormatCurrency(decimal amount)
        {
            return string.Format("{0} {1}", amount.ToString(DefaultFormat), CurrencySymbol);
        }
        
        /// <summary>
        /// Formate un montant avec un format personnalisé
        /// </summary>
        /// <param name="amount">Montant à formater</param>
        /// <param name="format">Format personnalisé</param>
        /// <returns>Montant formaté</returns>
        public static string FormatCurrency(decimal amount, string format)
        {
            return string.Format("{0} {1}", amount.ToString(format), CurrencySymbol);
        }
        
        /// <summary>
        /// Parse une chaîne en montant décimal
        /// </summary>
        /// <param name="value">Valeur à parser</param>
        /// <returns>Montant décimal</returns>
        public static decimal ParseCurrency(string value)
        {
            if (string.IsNullOrEmpty(value))
                return 0;
                
            // Supprimer le symbole de devise s'il est présent
            string cleanValue = value.Replace(CurrencySymbol, "").Trim();
            
            decimal result;
            if (decimal.TryParse(cleanValue, NumberStyles.Currency, TunisianCulture, out result))
                return result;
                
            if (decimal.TryParse(cleanValue, out result))
                return result;
                
            return 0;
        }
        
        /// <summary>
        /// Valide qu'une chaîne représente un montant valide
        /// </summary>
        /// <param name="value">Valeur à valider</param>
        /// <returns>True si valide</returns>
        public static bool IsValidCurrency(string value)
        {
            if (string.IsNullOrEmpty(value))
                return false;
                
            string cleanValue = value.Replace(CurrencySymbol, "").Trim();
            decimal result;
            return decimal.TryParse(cleanValue, out result) && result >= 0;
        }
        
        /// <summary>
        /// Convertit un montant en millimes (plus petite unité)
        /// </summary>
        /// <param name="dinars">Montant en dinars</param>
        /// <returns>Montant en millimes</returns>
        public static int ToMillimes(decimal dinars)
        {
            return (int)(dinars * 1000);
        }
        
        /// <summary>
        /// Convertit des millimes en dinars
        /// </summary>
        /// <param name="millimes">Montant en millimes</param>
        /// <returns>Montant en dinars</returns>
        public static decimal FromMillimes(int millimes)
        {
            return millimes / 1000m;
        }
        
        /// <summary>
        /// Arrondit un montant selon les règles tunisiennes
        /// </summary>
        /// <param name="amount">Montant à arrondir</param>
        /// <returns>Montant arrondi</returns>
        public static decimal RoundAmount(decimal amount)
        {
            // Arrondir à 3 décimales (millimes)
            return Math.Round(amount, 3, MidpointRounding.AwayFromZero);
        }
        
        /// <summary>
        /// Calcule la TVA tunisienne (19%)
        /// </summary>
        /// <param name="amountHT">Montant hors taxes</param>
        /// <returns>Montant de la TVA</returns>
        public static decimal CalculateTVA(decimal amountHT)
        {
            return RoundAmount(amountHT * 0.19m);
        }
        
        /// <summary>
        /// Calcule le montant TTC
        /// </summary>
        /// <param name="amountHT">Montant hors taxes</param>
        /// <returns>Montant toutes taxes comprises</returns>
        public static decimal CalculateTTC(decimal amountHT)
        {
            return RoundAmount(amountHT * 1.19m);
        }
    }
}
