@echo off
REM Script de test pour SQLite

echo ========================================
echo  TEST DE L'INSTALLATION SQLITE
echo ========================================
echo.

echo Verification des fichiers requis...
echo.

REM Vérifier les fichiers principaux
if exist "Data\DatabaseHelper-SQLite.cs" (
    echo [OK] DatabaseHelper-SQLite.cs
) else (
    echo [MANQUE] Data\DatabaseHelper-SQLite.cs
    goto :error
)

if exist "Services\InventoryService-SQLite.cs" (
    echo [OK] InventoryService-SQLite.cs
) else (
    echo [MANQUE] Services\InventoryService-SQLite.cs
    goto :error
)

if exist "lib\System.Data.SQLite.dll" (
    echo [OK] System.Data.SQLite.dll
    for %%I in ("lib\System.Data.SQLite.dll") do echo      Taille: %%~zI octets
) else (
    echo [MANQUE] lib\System.Data.SQLite.dll
    echo.
    echo SOLUTION:
    echo 1. Executer: install-sqlite.bat
    echo 2. Telecharger SQLite depuis:
    echo    https://system.data.sqlite.org/downloads.html
    echo 3. Copier System.Data.SQLite.dll dans lib\
    goto :error
)

echo.
echo Verification de l'environnement de compilation...

csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Compilateur C# non disponible
    echo.
    echo SOLUTION:
    echo Ouvrir "Developer Command Prompt for VS 2022"
    goto :error
) else (
    echo [OK] Compilateur C# disponible
)

echo.
echo ========================================
echo  COMPILATION DE TEST
echo ========================================
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Test" mkdir "bin\Test"

echo Compilation de test en cours...

REM Compiler une version de test
csc /target:winexe ^
    /out:bin\Test\InventoryApp-SQLite-Test.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /reference:lib\System.Data.SQLite.dll ^
    /define:USE_SQLITE_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-SQLite.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-SQLite.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo [ERREUR] Compilation de test echouee
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez que tous les fichiers sont presents.
    goto :error
)

echo.
echo [SUCCES] Compilation de test reussie !
echo.

REM Copier la DLL SQLite
copy "lib\System.Data.SQLite.dll" "bin\Test\" >nul
echo [OK] System.Data.SQLite.dll copie

echo.
echo ========================================
echo  TEST DE L'APPLICATION
echo ========================================
echo.

if exist "bin\Test\InventoryApp-SQLite-Test.exe" (
    echo Fichier genere: bin\Test\InventoryApp-SQLite-Test.exe
    for %%I in ("bin\Test\InventoryApp-SQLite-Test.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION SQLITE PRETE !
    echo.
    echo FONCTIONNALITES:
    echo • Base de donnees SQLite locale
    echo • Fichier: InventoryDB.sqlite
    echo • Devise Dinar Tunisien (DT)
    echo • Tous les modules fonctionnels
    echo • Compatible laptop et MC2100
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo Lancement de l'application SQLite...
        start "Inventaire SQLite - Test" "bin\Test\InventoryApp-SQLite-Test.exe"
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo 1. Creation d'articles avec prix en DT
        echo 2. Recherche et modification d'articles
        echo 3. Rapports avec donnees SQLite
        echo 4. Verification du fichier InventoryDB.sqlite
        echo.
        echo Le fichier de base sera cree automatiquement
        echo dans le dossier de l'application.
    )
    
    echo.
    echo PROCHAINES ETAPES:
    echo 1. Si le test fonctionne, executer: build-sqlite.bat
    echo 2. Deployer sur votre laptop avec SQLite
    echo 3. Copier aussi System.Data.SQLite.dll avec l'exe
    
) else (
    echo [ERREUR] Fichier executable non genere
    goto :error
)

echo.
echo ========================================
echo  TEST SQLITE TERMINE AVEC SUCCES !
echo ========================================
echo.
echo Votre application est maintenant compatible SQLite !
echo.
goto :end

:error
echo.
echo ========================================
echo  ERREUR LORS DU TEST
echo ========================================
echo.
echo Verifiez les points suivants:
echo 1. Tous les fichiers .cs sont presents
echo 2. System.Data.SQLite.dll est dans lib\
echo 3. Vous etes dans Developer Command Prompt for VS 2022
echo 4. Vous etes dans le bon dossier du projet
echo.
echo Pour installer SQLite: install-sqlite.bat
echo.

:end
REM Nettoyer les fichiers de test
rmdir /s /q "bin\Test" >nul 2>&1

pause
