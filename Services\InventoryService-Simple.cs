using System;
using System.Collections.Generic;
using InventoryApp.Data;
using InventoryApp.Models;

namespace InventoryApp.Services
{
    /// <summary>
    /// Service métier simplifié pour la gestion de l'inventaire (version XML)
    /// </summary>
    public class InventoryServiceSimple
    {
        /// <summary>
        /// Constructeur du service d'inventaire
        /// </summary>
        public InventoryServiceSimple()
        {
        }

        #region Gestion des articles

        /// <summary>
        /// Ajoute un nouvel article
        /// </summary>
        /// <param name="article">Article à ajouter</param>
        /// <returns>ID de l'article créé</returns>
        public int CreateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (string.IsNullOrEmpty(article.Barcode))
                throw new ArgumentException("Le code-barres est obligatoire");

            if (string.IsNullOrEmpty(article.Name))
                throw new ArgumentException("Le nom de l'article est obligatoire");

            // Vérifier que le code-barres n'existe pas déjà
            if (GetArticleByBarcode(article.Barcode) != null)
                throw new InvalidOperationException("Un article avec ce code-barres existe déjà");

            // Convertir l'article en dictionnaire
            Dictionary<string, object> articleData = new Dictionary<string, object>
            {
                {"Barcode", article.Barcode},
                {"Name", article.Name},
                {"Description", article.Description ?? ""},
                {"Category", article.Category ?? ""},
                {"UnitPrice", article.UnitPrice},
                {"StockQuantity", article.StockQuantity},
                {"MinimumStock", article.MinimumStock},
                {"Unit", article.Unit ?? "pièce"},
                {"Location", article.Location ?? ""}
            };

            return DatabaseHelperSimple.AddArticle(articleData);
        }

        /// <summary>
        /// Met à jour un article existant
        /// </summary>
        /// <param name="article">Article à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateArticle(Article article)
        {
            if (article == null)
                throw new ArgumentNullException("article");

            if (article.Id <= 0)
                throw new ArgumentException("ID d'article invalide");

            // Convertir l'article en dictionnaire
            Dictionary<string, object> articleData = new Dictionary<string, object>
            {
                {"Barcode", article.Barcode},
                {"Name", article.Name},
                {"Description", article.Description ?? ""},
                {"Category", article.Category ?? ""},
                {"UnitPrice", article.UnitPrice},
                {"StockQuantity", article.StockQuantity},
                {"MinimumStock", article.MinimumStock},
                {"Unit", article.Unit ?? "pièce"},
                {"Location", article.Location ?? ""}
            };

            return DatabaseHelperSimple.UpdateArticle(article.Id, articleData);
        }

        /// <summary>
        /// Supprime un article
        /// </summary>
        /// <param name="articleId">ID de l'article à supprimer</param>
        /// <returns>True si la suppression a réussi</returns>
        public bool DeleteArticle(int articleId)
        {
            if (articleId <= 0)
                throw new ArgumentException("ID d'article invalide");

            return DatabaseHelperSimple.DeleteArticle(articleId);
        }

        /// <summary>
        /// Récupère un article par son code-barres
        /// </summary>
        /// <param name="barcode">Code-barres</param>
        /// <returns>Article ou null si non trouvé</returns>
        public Article GetArticleByBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return null;

            Dictionary<string, object> data = DatabaseHelperSimple.GetArticleByBarcode(barcode);
            return data != null ? DictionaryToArticle(data) : null;
        }

        /// <summary>
        /// Recherche des articles
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des articles correspondants</returns>
        public List<Article> SearchArticles(string searchTerm)
        {
            List<Dictionary<string, object>> dataList;
            
            if (string.IsNullOrEmpty(searchTerm))
                dataList = DatabaseHelperSimple.GetAllArticles();
            else
                dataList = DatabaseHelperSimple.SearchArticles(searchTerm);

            List<Article> articles = new List<Article>();
            foreach (var data in dataList)
            {
                articles.Add(DictionaryToArticle(data));
            }

            return articles;
        }

        /// <summary>
        /// Récupère tous les articles
        /// </summary>
        /// <returns>Liste de tous les articles</returns>
        public List<Article> GetAllArticles()
        {
            List<Dictionary<string, object>> dataList = DatabaseHelperSimple.GetAllArticles();
            List<Article> articles = new List<Article>();
            
            foreach (var data in dataList)
            {
                articles.Add(DictionaryToArticle(data));
            }

            return articles;
        }

        #endregion

        #region Gestion des stocks

        /// <summary>
        /// Met à jour le stock d'un article
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="newQuantity">Nouvelle quantité</param>
        /// <returns>True si la mise à jour a réussi</returns>
        public bool UpdateStock(string barcode, int newQuantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            Dictionary<string, object> updateData = new Dictionary<string, object>
            {
                {"StockQuantity", newQuantity}
            };

            return DatabaseHelperSimple.UpdateArticle(article.Id, updateData);
        }

        /// <summary>
        /// Ajoute une quantité au stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à ajouter</param>
        /// <returns>True si l'ajout a réussi</returns>
        public bool AddToStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            int newQuantity = article.StockQuantity + quantity;
            return UpdateStock(barcode, newQuantity);
        }

        /// <summary>
        /// Retire une quantité du stock
        /// </summary>
        /// <param name="barcode">Code-barres de l'article</param>
        /// <param name="quantity">Quantité à retirer</param>
        /// <returns>True si le retrait a réussi</returns>
        public bool RemoveFromStock(string barcode, int quantity)
        {
            Article article = GetArticleByBarcode(barcode);
            if (article == null)
                return false;

            if (article.StockQuantity < quantity)
                return false; // Stock insuffisant

            int newQuantity = article.StockQuantity - quantity;
            return UpdateStock(barcode, newQuantity);
        }

        /// <summary>
        /// Récupère les articles en rupture de stock
        /// </summary>
        /// <returns>Liste des articles en rupture</returns>
        public List<Article> GetOutOfStockArticles()
        {
            List<Article> allArticles = GetAllArticles();
            List<Article> outOfStock = new List<Article>();

            foreach (Article article in allArticles)
            {
                if (article.StockQuantity <= 0)
                {
                    outOfStock.Add(article);
                }
            }

            return outOfStock;
        }

        /// <summary>
        /// Récupère les articles en dessous du seuil minimum
        /// </summary>
        /// <returns>Liste des articles en dessous du seuil</returns>
        public List<Article> GetLowStockArticles()
        {
            List<Article> allArticles = GetAllArticles();
            List<Article> lowStock = new List<Article>();

            foreach (Article article in allArticles)
            {
                if (article.StockQuantity <= article.MinimumStock)
                {
                    lowStock.Add(article);
                }
            }

            return lowStock;
        }

        #endregion

        #region Statistiques

        /// <summary>
        /// Récupère le nombre total d'articles
        /// </summary>
        /// <returns>Nombre d'articles</returns>
        public int GetTotalArticlesCount()
        {
            return DatabaseHelperSimple.GetTotalArticlesCount();
        }

        /// <summary>
        /// Récupère la valeur totale du stock
        /// </summary>
        /// <returns>Valeur totale</returns>
        public decimal GetTotalStockValue()
        {
            List<Article> articles = GetAllArticles();
            decimal totalValue = 0;

            foreach (Article article in articles)
            {
                totalValue += article.StockQuantity * article.UnitPrice;
            }

            return totalValue;
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Convertit un dictionnaire en objet Article
        /// </summary>
        /// <param name="data">Données du dictionnaire</param>
        /// <returns>Article</returns>
        private Article DictionaryToArticle(Dictionary<string, object> data)
        {
            Article article = new Article();

            if (data.ContainsKey("Id"))
                article.Id = Convert.ToInt32(data["Id"]);

            if (data.ContainsKey("Barcode"))
                article.Barcode = data["Barcode"].ToString();

            if (data.ContainsKey("Name"))
                article.Name = data["Name"].ToString();

            if (data.ContainsKey("Description"))
                article.Description = data["Description"].ToString();

            if (data.ContainsKey("Category"))
                article.Category = data["Category"].ToString();

            if (data.ContainsKey("UnitPrice"))
            {
                decimal unitPrice;
                decimal.TryParse(data["UnitPrice"].ToString(), out unitPrice);
                article.UnitPrice = unitPrice;
            }

            if (data.ContainsKey("StockQuantity"))
            {
                int stockQuantity;
                int.TryParse(data["StockQuantity"].ToString(), out stockQuantity);
                article.StockQuantity = stockQuantity;
            }

            if (data.ContainsKey("MinimumStock"))
            {
                int minimumStock;
                int.TryParse(data["MinimumStock"].ToString(), out minimumStock);
                article.MinimumStock = minimumStock;
            }

            if (data.ContainsKey("Unit"))
                article.Unit = data["Unit"].ToString();

            if (data.ContainsKey("Location"))
                article.Location = data["Location"].ToString();

            if (data.ContainsKey("CreatedDate"))
            {
                DateTime createdDate;
                DateTime.TryParse(data["CreatedDate"].ToString(), out createdDate);
                article.CreatedDate = createdDate;
            }

            if (data.ContainsKey("LastModified"))
            {
                DateTime lastModified;
                DateTime.TryParse(data["LastModified"].ToString(), out lastModified);
                article.LastModified = lastModified;
            }

            if (data.ContainsKey("IsActive"))
            {
                bool isActive;
                bool.TryParse(data["IsActive"].ToString(), out isActive);
                article.IsActive = isActive;
            }

            return article;
        }

        #endregion
    }
}
