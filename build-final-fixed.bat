@echo off
REM Script de compilation final corrige - Version XML avec InventoryFormSimple

echo ========================================
echo  BUILD FINAL CORRIGE - INVENTAIRE MC2100
echo  Version XML avec tous les modules
echo ========================================
echo.

REM Test de la commande csc
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Naviguer vers ce dossier
    echo 3. Executer ce script
    echo.
    pause
    exit /b 1
)

echo Commande csc disponible !
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation version finale en cours...
echo.

REM Compiler avec TOUS les bons fichiers
csc /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm-Simple.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo.
    echo FICHIERS UTILISES:
    echo • MainForm utilise InventoryFormSimple
    echo • InventoryForm-Simple.cs (version simplifiee)
    echo • ReportsForm complet avec 4 rapports
    echo • DatabaseHelper-Simple.cs (version XML)
    echo • CurrencyHelper.cs (Dinar Tunisien)
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION FINALE REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION COMPLETE AVEC:
    echo =============================
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete et fonctionnelle
    echo • Module inventaire simplifie mais operationnel
    echo • Rapports detailles (4 types complets)
    echo • Scanner codes-barres integre
    echo • Base de donnees XML (aucune dependance)
    echo • Interface optimisee pour Zebra MC2100
    echo.
    
    echo MODULES FONCTIONNELS:
    echo ====================
    echo 1. MENU PRINCIPAL: Vue d'ensemble avec statistiques
    echo 2. GESTION ARTICLES: Creation, modification, suppression
    echo    - Recherche par nom, code-barres, description
    echo    - Prix en Dinar Tunisien avec format 0.000 DT
    echo    - Gestion categories et emplacements
    echo    - Alertes stock minimum
    echo.
    echo 3. MODULE INVENTAIRE: Session d'inventaire
    echo    - Demarrage de sessions avec horodatage
    echo    - Instructions pour utilisation scanner
    echo    - Interface simplifiee mais complete
    echo.
    echo 4. RAPPORTS DETAILLES: 4 types de rapports
    echo    - Rapport stock global avec valeurs en DT
    echo    - Articles en rupture de stock
    echo    - Articles en stock bas (sous seuil)
    echo    - Rapport par categorie avec statistiques
    echo.
    echo DEPLOIEMENT SUR MC2100:
    echo =======================
    echo 1. Copier bin\Release\InventoryApp.exe vers le terminal
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo 4. Les donnees seront stockees dans InventoryDB.xml
    echo 5. Scanner integre pret a l'emploi
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application complete...
        start "Inventaire MC2100 - Version Finale" "bin\Release\InventoryApp.exe"
        echo.
        echo TESTEZ CES FONCTIONNALITES:
        echo ===========================
        echo 1. Menu principal: Voir les statistiques
        echo 2. Gestion Articles: Creer un article avec prix en DT
        echo 3. Inventaire: Demarrer une session d'inventaire
        echo 4. Rapports: Generer les 4 types de rapports
        echo.
        echo L'application utilise la devise tunisienne (DT)
        echo et fonctionne avec une base de donnees XML.
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo ========================================
echo  APPLICATION FINALE PRETE !
echo ========================================
echo.
echo Votre application d'inventaire pour Zebra MC2100
echo est maintenant complete avec:
echo.
echo • Devise Dinar Tunisien (DT)
echo • Tous les modules fonctionnels
echo • Base de donnees XML autonome
echo • Interface optimisee MC2100
echo • Scanner codes-barres integre
echo.
echo Prete pour le deploiement sur votre terminal !
echo.
echo Compilation terminee avec succes !
pause
