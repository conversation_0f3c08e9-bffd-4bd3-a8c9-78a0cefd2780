using System;
using System.Windows.Forms;
using SyncApp.Forms;

namespace SyncApp
{
    /// <summary>
    /// Application de synchronisation d'inventaires Zebra MC2100
    /// </summary>
    static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application de synchronisation
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // Lancer le formulaire principal de synchronisation
                Application.Run(new SyncMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    "Erreur lors du démarrage de l'application de synchronisation:\n\n" + ex.Message,
                    "Erreur de démarrage",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
