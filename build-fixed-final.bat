@echo off
REM Compilation finale corrigee avec bonnes references

echo ========================================
echo  BUILD FINAL CORRIGE - SQLITE
echo ========================================
echo.

REM Vérifications
csc /? >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Compilateur non disponible
    echo Ouvrir "Developer Command Prompt for VS 2022"
    pause
    exit /b 1
)

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo Telecharger depuis: https://system.data.sqlite.org/downloads.html
    pause
    exit /b 1
)

echo [OK] Environnement pret
echo.

REM Créer dossier
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Creation du fichier de reponse...

REM Créer un fichier de réponse complet avec tous les fichiers
echo /target:winexe > compile_final.rsp
echo /out:bin\Release\InventoryApp-SQLite.exe >> compile_final.rsp
echo /reference:System.dll >> compile_final.rsp
echo /reference:System.Data.dll >> compile_final.rsp
echo /reference:System.Drawing.dll >> compile_final.rsp
echo /reference:System.Windows.Forms.dll >> compile_final.rsp
echo /reference:System.Xml.dll >> compile_final.rsp
echo /reference:Microsoft.VisualBasic.dll >> compile_final.rsp
echo /reference:lib\System.Data.SQLite.dll >> compile_final.rsp
echo /define:USE_SQLITE_DATABASE >> compile_final.rsp
echo /nowarn:0168,0219 >> compile_final.rsp

REM Ajouter tous les fichiers dans le bon ordre
echo Program.cs >> compile_final.rsp
echo Properties\AssemblyInfo.cs >> compile_final.rsp

REM Modèles en premier
echo Models\Article.cs >> compile_final.rsp
echo Models\InventoryItem.cs >> compile_final.rsp

REM Utilitaires
echo Utils\CurrencyHelper.cs >> compile_final.rsp

REM Services (BarcodeService en premier car il contient BarcodeScannedEventArgs)
echo Services\BarcodeService.cs >> compile_final.rsp

REM Base de données
echo Data\DatabaseHelper-SQLite-Simple.cs >> compile_final.rsp

REM Service d'inventaire
echo Services\InventoryService-SQLite-Simple.cs >> compile_final.rsp

REM Formulaires (après tous les services)
echo Forms\MainForm.cs >> compile_final.rsp
echo Forms\MainForm.Designer.cs >> compile_final.rsp
echo Forms\ArticleForm.cs >> compile_final.rsp
echo Forms\ArticleForm.Designer.cs >> compile_final.rsp
echo Forms\InventoryForm-Simple.cs >> compile_final.rsp
echo Forms\ReportsForm.cs >> compile_final.rsp
echo Forms\ReportsForm.Designer.cs >> compile_final.rsp

echo.
echo Compilation avec fichier de reponse...
echo.

REM Compiler avec le fichier de réponse
csc @compile_final.rsp

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  COMPILATION FINALE REUSSIE !
    echo ========================================
    echo.
    
    REM Copier SQLite DLL
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo [OK] System.Data.SQLite.dll copie
    
    echo.
    echo Fichier genere: bin\Release\InventoryApp-SQLite.exe
    for %%I in ("bin\Release\InventoryApp-SQLite.exe") do echo Taille: %%~zI octets
    
    echo.
    echo ========================================
    echo  APPLICATION SQLITE COMPLETE !
    echo ========================================
    echo.
    echo FONCTIONNALITES IMPLEMENTEES:
    echo =============================
    echo • Base de donnees SQLite locale (InventoryDB.sqlite)
    echo • Compatible .NET Framework 4.0
    echo • Devise Dinar Tunisien (DT) format 0.000
    echo • Gestion articles complete (CRUD)
    echo • Recherche rapide par nom/code-barres
    echo • Rapports detailles (4 types)
    echo • Module inventaire simplifie
    echo • Scanner codes-barres integre avec evenements
    echo • Statistiques en temps reel
    echo • Performance optimisee pour laptop
    echo.
    
    echo MODULES CORRIGES:
    echo ================
    echo • BarcodeService avec BarcodeScannedEventArgs
    echo • DatabaseHelper-SQLite-Simple compatible .NET 4.0
    echo • InventoryService-SQLite-Simple complet
    echo • Tous les formulaires avec bonnes references
    echo • CurrencyHelper pour Dinar Tunisien
    echo.
    
    echo BASE DE DONNEES:
    echo ===============
    echo • Fichier: InventoryDB.sqlite (cree automatiquement)
    echo • Emplacement: bin\Release\InventoryDB.sqlite
    echo • Structure: Table Articles optimisee
    echo • Sauvegarde: Copier le fichier .sqlite
    echo • Outils: DB Browser for SQLite, SQLiteStudio
    echo.
    
    echo Voulez-vous tester l'application maintenant ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo.
        echo ========================================
        echo  LANCEMENT DE L'APPLICATION
        echo ========================================
        echo.
        start "Inventaire SQLite - Version Finale" "bin\Release\InventoryApp-SQLite.exe"
        echo.
        echo APPLICATION LANCEE !
        echo.
        echo GUIDE DE TEST COMPLET:
        echo ======================
        echo.
        echo 1. MENU PRINCIPAL:
        echo    • Voir les statistiques en temps reel
        echo    • Tester le scanner de codes-barres
        echo    • Verifier les informations de base
        echo.
        echo 2. GESTION ARTICLES:
        echo    • Creer un nouvel article:
        echo      - Code-barres: 1234567890123
        echo      - Nom: Article Test SQLite
        echo      - Prix: 25.750 DT
        echo      - Stock: 15
        echo      - Stock minimum: 3
        echo    • Rechercher l'article cree
        echo    • Modifier l'article
        echo    • Tester la recherche rapide
        echo.
        echo 3. RAPPORTS SQLITE:
        echo    • Rapport Stock Global avec valeurs DT
        echo    • Articles en Rupture de Stock
        echo    • Articles en Stock Bas
        echo    • Rapport par Categorie avec statistiques
        echo.
        echo 4. INVENTAIRE:
        echo    • Demarrer une session d'inventaire
        echo    • Voir les instructions d'utilisation
        echo    • Tester le scanner integre
        echo.
        echo 5. SCANNER CODES-BARRES:
        echo    • Tester la saisie manuelle
        echo    • Verifier les evenements BarcodeScanned
        echo    • Tester la validation des codes
        echo.
        echo 6. BASE DE DONNEES SQLITE:
        echo    • Le fichier sera cree dans:
        echo      %CD%\bin\Release\InventoryDB.sqlite
        echo    • Telecharger DB Browser for SQLite
        echo    • Ouvrir le fichier pour voir les donnees
        echo    • Verifier la structure des tables
        echo.
        echo Si tout fonctionne, votre application SQLite
        echo est prete pour le deploiement !
    )
    
) else (
    echo.
    echo ========================================
    echo  ERREUR DE COMPILATION
    echo ========================================
    echo.
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
    echo.
    echo FICHIERS COMPILES DANS L'ORDRE:
    echo ===============================
    echo 1. Models (Article, InventoryItem)
    echo 2. Utils (CurrencyHelper)
    echo 3. Services (BarcodeService avec EventArgs)
    echo 4. Data (DatabaseHelper-SQLite-Simple)
    echo 5. Services (InventoryService-SQLite-Simple)
    echo 6. Forms (tous les formulaires)
    echo 7. Program (point d'entree)
    echo.
)

REM Nettoyer
del compile_final.rsp >nul 2>&1

echo.
echo ========================================
echo  COMPILATION TERMINEE
echo ========================================
echo.
echo Votre application d'inventaire SQLite avec devise
echo tunisienne est maintenant prete !
echo.
pause
