<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B2C3D4E5-F6G7-8901-BCDE-F23456789012}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>InventoryApp</RootNamespace>
    <AssemblyName>InventoryApp</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ArticleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ArticleForm.Designer.cs">
      <DependentUpon>ArticleForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InventoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InventoryForm.Designer.cs">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.Designer.cs">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\DatabaseHelper.cs" />
    <Compile Include="Data\InventoryContext.cs" />
    <Compile Include="Models\Article.cs" />
    <Compile Include="Models\InventoryItem.cs" />
    <Compile Include="Services\BarcodeService.cs" />
    <Compile Include="Services\InventoryService.cs" />
    <Compile Include="TestData\TestDataLoader.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ArticleForm.resx">
      <DependentUpon>ArticleForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\InventoryForm.resx">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ReportsForm.resx">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\Images\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
