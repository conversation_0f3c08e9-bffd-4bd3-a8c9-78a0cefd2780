@echo off
REM Script pour localiser et examiner la base SQLite

echo ========================================
echo  LOCALISATION BASE DE DONNEES SQLITE
echo ========================================
echo.

echo Recherche de la base de donnees SQLite...
echo.

REM Rechercher dans les emplacements probables
set DB_FOUND=0

echo EMPLACEMENTS VERIFIES:
echo ======================
echo.

REM 1. Dossier de compilation
if exist "bin\Release\InventoryDB.sqlite" (
    echo [TROUVE] bin\Release\InventoryDB.sqlite
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo          Taille: %%~zI octets
    for %%I in ("bin\Release\InventoryDB.sqlite") do echo          Cree: %%~tI
    set DB_PATH=bin\Release\InventoryDB.sqlite
    set DB_FOUND=1
) else (
    echo [ABSENT] bin\Release\InventoryDB.sqlite
)

echo.

REM 2. Dossier racine du projet
if exist "InventoryDB.sqlite" (
    echo [TROUVE] InventoryDB.sqlite (racine)
    for %%I in ("InventoryDB.sqlite") do echo          Taille: %%~zI octets
    for %%I in ("InventoryDB.sqlite") do echo          Cree: %%~tI
    if %DB_FOUND%==0 (
        set DB_PATH=InventoryDB.sqlite
        set DB_FOUND=1
    )
) else (
    echo [ABSENT] InventoryDB.sqlite (racine)
)

echo.

REM 3. Dossier Debug
if exist "bin\Debug\InventoryDB.sqlite" (
    echo [TROUVE] bin\Debug\InventoryDB.sqlite
    for %%I in ("bin\Debug\InventoryDB.sqlite") do echo          Taille: %%~zI octets
    for %%I in ("bin\Debug\InventoryDB.sqlite") do echo          Cree: %%~tI
    if %DB_FOUND%==0 (
        set DB_PATH=bin\Debug\InventoryDB.sqlite
        set DB_FOUND=1
    )
) else (
    echo [ABSENT] bin\Debug\InventoryDB.sqlite
)

echo.

if %DB_FOUND%==1 (
    echo ========================================
    echo  BASE DE DONNEES TROUVEE !
    echo ========================================
    echo.
    echo Fichier principal: %DB_PATH%
    echo.
    
    echo INFORMATIONS DETAILLEES:
    echo ========================
    for %%I in ("%DB_PATH%") do (
        echo Nom: %%~nxI
        echo Chemin complet: %%~fI
        echo Taille: %%~zI octets
        echo Date creation: %%~tI
        echo Attributs: %%~aI
    )
    
    echo.
    echo CONTENU DE LA BASE (si possible):
    echo =================================
    
    REM Essayer d'afficher des informations sur la base
    if exist "lib\System.Data.SQLite.dll" (
        echo Tentative de lecture de la base...
        echo.
        echo Pour examiner le contenu, utilisez:
        echo • DB Browser for SQLite (gratuit)
        echo • SQLiteStudio
        echo • SQLite Expert
        echo • Ou votre application d'inventaire
    ) else (
        echo System.Data.SQLite.dll non trouve
        echo Impossible de lire la base directement
    )
    
    echo.
    echo ACTIONS POSSIBLES:
    echo ==================
    echo 1. Ouvrir avec DB Browser for SQLite
    echo 2. Lancer l'application d'inventaire
    echo 3. Faire une sauvegarde
    echo 4. Examiner la structure
    echo.
    
    echo Voulez-vous ouvrir l'explorateur sur ce fichier ? (O/N)
    set /p OPEN_EXPLORER=
    if /i "%OPEN_EXPLORER%"=="O" (
        explorer /select,"%DB_PATH%"
    )
    
    echo.
    echo Voulez-vous lancer l'application d'inventaire ? (O/N)
    set /p LAUNCH_APP=
    if /i "%LAUNCH_APP%"=="O" (
        if exist "bin\Release\InventoryApp-SQLite.exe" (
            start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe"
            echo Application lancee !
        ) else (
            echo Application non trouvee. Compilez d'abord avec build-sqlite-final.bat
        )
    )
    
) else (
    echo ========================================
    echo  AUCUNE BASE DE DONNEES TROUVEE
    echo ========================================
    echo.
    echo La base SQLite n'existe pas encore.
    echo.
    echo COMMENT CREER LA BASE:
    echo =====================
    echo 1. Compiler l'application SQLite:
    echo    build-sqlite-final.bat
    echo.
    echo 2. Lancer l'application:
    echo    bin\Release\InventoryApp-SQLite.exe
    echo.
    echo 3. La base sera creee automatiquement au premier lancement
    echo.
    echo EMPLACEMENT FUTUR:
    echo ==================
    echo La base sera creee dans le meme dossier que l'executable:
    echo • Si compilation: bin\Release\InventoryDB.sqlite
    echo • Si deploiement: [Dossier app]\InventoryDB.sqlite
    echo.
)

echo.
echo ========================================
echo  SYNCHRONISATION AVEC TERMINAL MC2100
echo ========================================
echo.

echo IMPORTANT A SAVOIR:
echo ===================
echo • Base SQLite (laptop): Haute performance, requetes SQL
echo • Base XML (terminal): Compatible Windows CE, plus simple
echo • PAS de synchronisation automatique entre les deux
echo • Synchronisation manuelle requise
echo.

echo STRATEGIES DE SYNCHRONISATION:
echo ==============================
echo 1. LAPTOP PRINCIPAL:
echo    • Utiliser SQLite comme base principale
echo    • Exporter vers XML pour le terminal
echo    • Synchroniser periodiquement
echo.
echo 2. TERMINAL PRINCIPAL:
echo    • Utiliser XML comme base principale
echo    • Importer vers SQLite pour analyse
echo    • Synchroniser apres inventaires
echo.
echo 3. DOUBLE SAISIE:
echo    • Maintenir les deux bases separement
echo    • Saisir sur les deux systemes
echo    • Comparer periodiquement
echo.

echo Pour gerer la synchronisation, utilisez:
echo sync-databases.bat
echo.

pause
