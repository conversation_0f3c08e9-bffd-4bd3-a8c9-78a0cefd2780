using System;
using System.Collections.Generic;
using InventoryApp.Data;
using InventoryApp.Models;
using InventoryApp.Services;

namespace InventoryApp.TestData
{
    /// <summary>
    /// Utilitaire pour charger des données de test dans l'application
    /// </summary>
    public static class TestDataLoader
    {
        /// <summary>
        /// Charge un jeu de données de test complet
        /// </summary>
        /// <param name="clearExisting">Si true, supprime les données existantes</param>
        public static void LoadTestData(bool clearExisting = false)
        {
            try
            {
                InventoryService inventoryService = new InventoryService();
                
                if (clearExisting)
                {
                    ClearAllData();
                }
                
                // Charger les articles de test
                LoadTestArticles(inventoryService);
                
                // Charger les données d'inventaire de test
                LoadTestInventoryItems(inventoryService);
                
                Console.WriteLine("Données de test chargées avec succès");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Erreur lors du chargement des données de test: " + ex.Message);
                throw;
            }
        }
        
        /// <summary>
        /// Supprime toutes les données existantes
        /// </summary>
        private static void ClearAllData()
        {
            string clearInventoryItems = "DELETE FROM InventoryItems";
            string clearArticles = "DELETE FROM Articles";
            
            DatabaseHelper.ExecuteNonQuery(clearInventoryItems);
            DatabaseHelper.ExecuteNonQuery(clearArticles);
            
            Console.WriteLine("Données existantes supprimées");
        }
        
        /// <summary>
        /// Charge les articles de test
        /// </summary>
        private static void LoadTestArticles(InventoryService inventoryService)
        {
            List<Article> testArticles = GetTestArticles();
            
            foreach (Article article in testArticles)
            {
                try
                {
                    inventoryService.CreateArticle(article);
                    Console.WriteLine("Article créé: " + article.Name);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Erreur création article " + article.Name + ": " + ex.Message);
                }
            }
        }
        
        /// <summary>
        /// Charge les éléments d'inventaire de test
        /// </summary>
        private static void LoadTestInventoryItems(InventoryService inventoryService)
        {
            List<InventoryItem> testItems = GetTestInventoryItems();
            
            foreach (InventoryItem item in testItems)
            {
                try
                {
                    inventoryService.AddInventoryItem(item);
                    Console.WriteLine("Élément d'inventaire créé: " + item.ArticleName);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Erreur création inventaire " + item.ArticleName + ": " + ex.Message);
                }
            }
        }
        
        /// <summary>
        /// Retourne la liste des articles de test
        /// </summary>
        private static List<Article> GetTestArticles()
        {
            return new List<Article>
            {
                // Articles de bureau
                new Article("3760123456789", "Stylo Bic Bleu", "Bureau")
                {
                    Description = "Stylo à bille bleu standard",
                    UnitPrice = 0.50m,
                    StockQuantity = 150,
                    MinimumStock = 20,
                    Unit = "pièce",
                    Location = "A1-01"
                },
                new Article("3760123456790", "Cahier A4 200p", "Bureau")
                {
                    Description = "Cahier spirale A4 200 pages",
                    UnitPrice = 3.20m,
                    StockQuantity = 45,
                    MinimumStock = 10,
                    Unit = "pièce",
                    Location = "A1-02"
                },
                new Article("3760123456791", "Agrafeuse Standard", "Bureau")
                {
                    Description = "Agrafeuse de bureau standard",
                    UnitPrice = 12.50m,
                    StockQuantity = 8,
                    MinimumStock = 3,
                    Unit = "pièce",
                    Location = "A1-03"
                },
                new Article("3760123456792", "Agrafes 26/6", "Bureau")
                {
                    Description = "Boîte de 1000 agrafes 26/6",
                    UnitPrice = 2.80m,
                    StockQuantity = 25,
                    MinimumStock = 5,
                    Unit = "boîte",
                    Location = "A1-04"
                },
                new Article("3760123456793", "Classeur A4", "Bureau")
                {
                    Description = "Classeur à levier A4 dos 80mm",
                    UnitPrice = 4.90m,
                    StockQuantity = 30,
                    MinimumStock = 8,
                    Unit = "pièce",
                    Location = "A1-05"
                },
                
                // Articles informatiques
                new Article("3760123456794", "Souris USB", "Informatique")
                {
                    Description = "Souris optique USB filaire",
                    UnitPrice = 15.90m,
                    StockQuantity = 12,
                    MinimumStock = 3,
                    Unit = "pièce",
                    Location = "B2-01"
                },
                new Article("3760123456795", "Clavier USB", "Informatique")
                {
                    Description = "Clavier AZERTY USB filaire",
                    UnitPrice = 25.50m,
                    StockQuantity = 8,
                    MinimumStock = 2,
                    Unit = "pièce",
                    Location = "B2-02"
                },
                new Article("3760123456796", "Câble USB-A/B", "Informatique")
                {
                    Description = "Câble USB A vers B 2 mètres",
                    UnitPrice = 8.90m,
                    StockQuantity = 20,
                    MinimumStock = 5,
                    Unit = "pièce",
                    Location = "B2-03"
                },
                
                // Articles en rupture (pour tester les alertes)
                new Article("3760123456804", "Toner Imprimante", "Informatique")
                {
                    Description = "Cartouche toner noir HP",
                    UnitPrice = 89.90m,
                    StockQuantity = 0,
                    MinimumStock = 2,
                    Unit = "pièce",
                    Location = "B2-06"
                },
                new Article("3760123456805", "Papier A4 Ramette", "Bureau")
                {
                    Description = "Ramette 500 feuilles A4 80g",
                    UnitPrice = 4.50m,
                    StockQuantity = 0,
                    MinimumStock = 5,
                    Unit = "ramette",
                    Location = "A1-06"
                },
                
                // Articles avec stock bas
                new Article("3760123456806", "Marqueurs Couleur x4", "Bureau")
                {
                    Description = "Set 4 marqueurs couleurs",
                    UnitPrice = 6.90m,
                    StockQuantity = 2,
                    MinimumStock = 5,
                    Unit = "set",
                    Location = "A1-07"
                },
                new Article("3760123456807", "Pile AA x4", "Informatique")
                {
                    Description = "Pack 4 piles alcalines AA",
                    UnitPrice = 5.20m,
                    StockQuantity = 1,
                    MinimumStock = 3,
                    Unit = "pack",
                    Location = "B2-07"
                },
                
                // Articles avec prix élevé
                new Article("3760123456808", "Écran LCD 24\"", "Informatique")
                {
                    Description = "Moniteur LCD 24 pouces Full HD",
                    UnitPrice = 189.90m,
                    StockQuantity = 3,
                    MinimumStock = 1,
                    Unit = "pièce",
                    Location = "B2-08"
                },
                new Article("3760123456809", "Imprimante Laser", "Informatique")
                {
                    Description = "Imprimante laser monochrome",
                    UnitPrice = 159.90m,
                    StockQuantity = 2,
                    MinimumStock = 1,
                    Unit = "pièce",
                    Location = "B2-09"
                }
            };
        }
        
        /// <summary>
        /// Retourne la liste des éléments d'inventaire de test
        /// </summary>
        private static List<InventoryItem> GetTestInventoryItems()
        {
            return new List<InventoryItem>
            {
                new InventoryItem(1, "3760123456789", "Stylo Bic Bleu", 150)
                {
                    CountedQuantity = 148,
                    Location = "A1-01",
                    CountedBy = "Operateur1",
                    Comments = "Comptage OK",
                    IsValidated = true,
                    InventorySession = "SESSION_TEST_001"
                },
                new InventoryItem(2, "3760123456790", "Cahier A4 200p", 45)
                {
                    CountedQuantity = 45,
                    Location = "A1-02",
                    CountedBy = "Operateur1",
                    Comments = "Comptage OK",
                    IsValidated = true,
                    InventorySession = "SESSION_TEST_001"
                },
                new InventoryItem(3, "3760123456791", "Agrafeuse Standard", 8)
                {
                    CountedQuantity = 7,
                    Location = "A1-03",
                    CountedBy = "Operateur1",
                    Comments = "Manque 1 unité",
                    IsValidated = false,
                    InventorySession = "SESSION_TEST_001"
                },
                new InventoryItem(6, "3760123456794", "Souris USB", 12)
                {
                    CountedQuantity = 12,
                    Location = "B2-01",
                    CountedBy = "Operateur2",
                    Comments = "Comptage OK",
                    IsValidated = true,
                    InventorySession = "SESSION_TEST_002"
                },
                new InventoryItem(8, "3760123456796", "Câble USB-A/B", 20)
                {
                    CountedQuantity = 18,
                    Location = "B2-03",
                    CountedBy = "Operateur2",
                    Comments = "Manque 2 unités",
                    IsValidated = false,
                    InventorySession = "SESSION_TEST_002"
                }
            };
        }
        
        /// <summary>
        /// Génère des codes-barres de test aléatoires
        /// </summary>
        /// <param name="count">Nombre de codes à générer</param>
        /// <returns>Liste de codes-barres</returns>
        public static List<string> GenerateTestBarcodes(int count)
        {
            List<string> barcodes = new List<string>();
            Random random = new Random();
            
            for (int i = 0; i < count; i++)
            {
                string barcode = "TEST" + random.Next(100000, 999999).ToString();
                barcodes.Add(barcode);
            }
            
            return barcodes;
        }
        
        /// <summary>
        /// Crée des articles de test avec des codes-barres aléatoires
        /// </summary>
        /// <param name="count">Nombre d'articles à créer</param>
        public static void CreateRandomTestArticles(int count)
        {
            InventoryService inventoryService = new InventoryService();
            Random random = new Random();
            
            string[] categories = { "Test", "Bureau", "Informatique", "Nettoyage", "Cuisine" };
            string[] units = { "pièce", "lot", "boîte", "pack", "flacon" };
            
            for (int i = 0; i < count; i++)
            {
                Article article = new Article
                {
                    Barcode = "RANDOM" + random.Next(100000, 999999).ToString(),
                    Name = "Article Test " + (i + 1),
                    Description = "Article de test généré automatiquement",
                    Category = categories[random.Next(categories.Length)],
                    UnitPrice = (decimal)(random.NextDouble() * 100),
                    StockQuantity = random.Next(0, 100),
                    MinimumStock = random.Next(1, 10),
                    Unit = units[random.Next(units.Length)],
                    Location = "TEST-" + random.Next(1, 10).ToString("D2")
                };
                
                try
                {
                    inventoryService.CreateArticle(article);
                    Console.WriteLine("Article aléatoire créé: " + article.Name);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Erreur création article aléatoire: " + ex.Message);
                }
            }
        }
    }
}
