using System;
using System.Collections.Generic;

namespace SyncApp.Models
{
    /// <summary>
    /// Résultat d'une opération de synchronisation
    /// </summary>
    public class SyncResult
    {
        /// <summary>
        /// Indique si la synchronisation a réussi
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message de résultat
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Type de synchronisation effectuée
        /// </summary>
        public SyncType SyncType { get; set; }

        /// <summary>
        /// Nombre total d'éléments traités
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Nombre d'éléments synchronisés avec succès
        /// </summary>
        public int SuccessfulItems { get; set; }

        /// <summary>
        /// Nombre d'éléments en erreur
        /// </summary>
        public int ErrorItems { get; set; }

        /// <summary>
        /// Nombre de conflits détectés
        /// </summary>
        public int ConflictItems { get; set; }

        /// <summary>
        /// Nombre de nouveaux éléments
        /// </summary>
        public int NewItems { get; set; }

        /// <summary>
        /// Nombre d'éléments modifiés
        /// </summary>
        public int ModifiedItems { get; set; }

        /// <summary>
        /// Durée de la synchronisation
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Date et heure de début de la synchronisation
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Date et heure de fin de la synchronisation
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Liste des éléments synchronisés
        /// </summary>
        public List<SyncItem> Items { get; set; }

        /// <summary>
        /// Liste des erreurs rencontrées
        /// </summary>
        public List<string> Errors { get; set; }

        /// <summary>
        /// Liste des avertissements
        /// </summary>
        public List<string> Warnings { get; set; }

        /// <summary>
        /// Informations détaillées sur la synchronisation
        /// </summary>
        public string Details { get; set; }

        /// <summary>
        /// Constructeur par défaut
        /// </summary>
        public SyncResult()
        {
            Items = new List<SyncItem>();
            Errors = new List<string>();
            Warnings = new List<string>();
            StartTime = DateTime.Now;
            Success = false;
        }

        /// <summary>
        /// Constructeur avec type de synchronisation
        /// </summary>
        public SyncResult(SyncType syncType) : this()
        {
            SyncType = syncType;
        }

        /// <summary>
        /// Marque la synchronisation comme terminée
        /// </summary>
        public void Complete()
        {
            EndTime = DateTime.Now;
            Duration = EndTime - StartTime;
            
            // Calculer les statistiques
            TotalItems = Items.Count;
            SuccessfulItems = 0;
            ErrorItems = 0;
            ConflictItems = 0;
            NewItems = 0;
            ModifiedItems = 0;

            foreach (SyncItem item in Items)
            {
                switch (item.Status)
                {
                    case SyncStatus.Synchronized:
                        SuccessfulItems++;
                        break;
                    case SyncStatus.Error:
                        ErrorItems++;
                        break;
                    case SyncStatus.Conflict:
                        ConflictItems++;
                        break;
                    case SyncStatus.New:
                        NewItems++;
                        break;
                    case SyncStatus.Modified:
                        ModifiedItems++;
                        break;
                }
            }

            // Déterminer le succès global
            Success = ErrorItems == 0 && ConflictItems == 0;
            
            // Générer le message de résultat
            if (Success)
            {
                Message = string.Format("Synchronisation réussie: {0} éléments traités", TotalItems);
            }
            else
            {
                Message = string.Format("Synchronisation terminée avec {0} erreurs et {1} conflits", 
                    ErrorItems, ConflictItems);
            }
        }

        /// <summary>
        /// Ajoute une erreur au résultat
        /// </summary>
        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error))
            {
                Errors.Add(string.Format("[{0:HH:mm:ss}] {1}", DateTime.Now, error));
            }
        }

        /// <summary>
        /// Ajoute un avertissement au résultat
        /// </summary>
        public void AddWarning(string warning)
        {
            if (!string.IsNullOrEmpty(warning))
            {
                Warnings.Add(string.Format("[{0:HH:mm:ss}] {1}", DateTime.Now, warning));
            }
        }

        /// <summary>
        /// Obtient un résumé textuel du résultat
        /// </summary>
        public string GetSummary()
        {
            return string.Format(
                "RÉSUMÉ DE SYNCHRONISATION\n" +
                "========================\n" +
                "Type: {0}\n" +
                "Statut: {1}\n" +
                "Durée: {2:mm\\:ss}\n" +
                "Début: {3:dd/MM/yyyy HH:mm:ss}\n" +
                "Fin: {4:dd/MM/yyyy HH:mm:ss}\n\n" +
                "STATISTIQUES\n" +
                "============\n" +
                "Total: {5} éléments\n" +
                "Réussis: {6}\n" +
                "Nouveaux: {7}\n" +
                "Modifiés: {8}\n" +
                "Conflits: {9}\n" +
                "Erreurs: {10}\n\n" +
                "MESSAGE\n" +
                "=======\n" +
                "{11}",
                GetSyncTypeText(SyncType),
                Success ? "SUCCÈS" : "ÉCHEC",
                Duration,
                StartTime,
                EndTime,
                TotalItems,
                SuccessfulItems,
                NewItems,
                ModifiedItems,
                ConflictItems,
                ErrorItems,
                Message
            );
        }

        /// <summary>
        /// Obtient le texte descriptif du type de synchronisation
        /// </summary>
        private string GetSyncTypeText(SyncType syncType)
        {
            switch (syncType)
            {
                case SyncType.TerminalToLaptop:
                    return "Terminal → Laptop";
                case SyncType.LaptopToTerminal:
                    return "Laptop → Terminal";
                case SyncType.Bidirectional:
                    return "Bidirectionnelle";
                default:
                    return syncType.ToString();
            }
        }

        /// <summary>
        /// Retourne une représentation textuelle du résultat
        /// </summary>
        public override string ToString()
        {
            return string.Format("{0}: {1} ({2} éléments)", 
                GetSyncTypeText(SyncType), 
                Success ? "Succès" : "Échec", 
                TotalItems);
        }
    }

    /// <summary>
    /// Type de synchronisation
    /// </summary>
    public enum SyncType
    {
        /// <summary>
        /// Synchronisation du terminal vers le laptop
        /// </summary>
        TerminalToLaptop,

        /// <summary>
        /// Synchronisation du laptop vers le terminal
        /// </summary>
        LaptopToTerminal,

        /// <summary>
        /// Synchronisation bidirectionnelle
        /// </summary>
        Bidirectional
    }
}
