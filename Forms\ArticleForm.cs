using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using InventoryApp.Models;
using InventoryApp.Services;
using InventoryApp.Utils;

namespace InventoryApp.Forms
{
    /// <summary>
    /// Formulaire de gestion des articles
    /// </summary>
    public partial class ArticleForm : Form
    {
#if USE_SQLITE_DATABASE
        private readonly InventoryServiceSQLite _inventoryService;
#elif USE_XML_DATABASE
        private readonly InventoryServiceSimple _inventoryService;
#else
        private readonly InventoryService _inventoryService;
#endif
        private readonly BarcodeService _barcodeService;
        private List<Article> _articles;
        private Article _currentArticle;
        private bool _isEditing;

        /// <summary>
        /// Constructeur du formulaire de gestion des articles
        /// </summary>
        public ArticleForm()
        {
            InitializeComponent();

#if USE_SQLITE_DATABASE
            _inventoryService = new InventoryServiceSQLite();
#elif USE_XML_DATABASE
            _inventoryService = new InventoryServiceSimple();
#else
            _inventoryService = new InventoryService();
#endif
            _barcodeService = new BarcodeService();
            _articles = new List<Article>();
            _isEditing = false;
            
            InitializeServices();
            LoadArticles();
            SetupForm();
        }

        /// <summary>
        /// Initialise les services
        /// </summary>
        private void InitializeServices()
        {
            _barcodeService.EnableScanner();
            _barcodeService.BarcodeScanned += OnBarcodeScanned;
        }

        /// <summary>
        /// Configure le formulaire
        /// </summary>
        private void SetupForm()
        {
            // Configurer la liste des articles
            lstArticles.DisplayMember = "Name";
            lstArticles.ValueMember = "Id";
            
            // Vider les champs
            ClearFields();
            
            // Désactiver les boutons d'édition
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        /// <summary>
        /// Charge la liste des articles
        /// </summary>
        private void LoadArticles()
        {
            try
            {
                _articles = _inventoryService.GetAllArticles();
                lstArticles.DataSource = null;
                lstArticles.DataSource = _articles;
                
                lblTotalCount.Text = _articles.Count.ToString();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des articles: " + ex.Message);
            }
        }

        /// <summary>
        /// Vide tous les champs de saisie
        /// </summary>
        private void ClearFields()
        {
            txtBarcode.Text = "";
            txtName.Text = "";
            txtDescription.Text = "";
            txtCategory.Text = "";
            txtUnitPrice.Text = "0";
            txtStockQuantity.Text = "0";
            txtMinimumStock.Text = "0";
            txtUnit.Text = "pièce";
            txtLocation.Text = "";
            
            _currentArticle = null;
            _isEditing = false;
            
            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        /// <summary>
        /// Remplit les champs avec les données d'un article
        /// </summary>
        /// <param name="article">Article à afficher</param>
        private void FillFields(Article article)
        {
            if (article == null) return;
            
            txtBarcode.Text = article.Barcode;
            txtName.Text = article.Name;
            txtDescription.Text = article.Description;
            txtCategory.Text = article.Category;
            txtUnitPrice.Text = article.UnitPrice.ToString("F3");
            txtStockQuantity.Text = article.StockQuantity.ToString();
            txtMinimumStock.Text = article.MinimumStock.ToString();
            txtUnit.Text = article.Unit;
            txtLocation.Text = article.Location;
            
            _currentArticle = article;
            _isEditing = true;
            
            btnAdd.Enabled = false;
            btnUpdate.Enabled = true;
            btnDelete.Enabled = true;
        }

        /// <summary>
        /// Crée un article à partir des champs de saisie
        /// </summary>
        /// <returns>Article créé</returns>
        private Article CreateArticleFromFields()
        {
            Article article = new Article();
            
            article.Barcode = txtBarcode.Text.Trim();
            article.Name = txtName.Text.Trim();
            article.Description = txtDescription.Text.Trim();
            article.Category = txtCategory.Text.Trim();
            article.Unit = txtUnit.Text.Trim();
            article.Location = txtLocation.Text.Trim();
            
            // Conversion des valeurs numériques avec support devise tunisienne
            article.UnitPrice = CurrencyHelper.ParseCurrency(txtUnitPrice.Text);
            
            int stockQuantity;
            if (int.TryParse(txtStockQuantity.Text, out stockQuantity))
                article.StockQuantity = stockQuantity;
            
            int minimumStock;
            if (int.TryParse(txtMinimumStock.Text, out minimumStock))
                article.MinimumStock = minimumStock;
            
            if (_isEditing && _currentArticle != null)
            {
                article.Id = _currentArticle.Id;
                article.CreatedDate = _currentArticle.CreatedDate;
            }
            
            return article;
        }

        /// <summary>
        /// Valide les données saisies
        /// </summary>
        /// <returns>True si les données sont valides</returns>
        private bool ValidateFields()
        {
            if (string.IsNullOrEmpty(txtBarcode.Text.Trim()))
            {
                ShowError("Le code-barres est obligatoire");
                txtBarcode.Focus();
                return false;
            }
            
            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                ShowError("Le nom de l'article est obligatoire");
                txtName.Focus();
                return false;
            }
            
            if (!CurrencyHelper.IsValidCurrency(txtUnitPrice.Text))
            {
                ShowError("Le prix unitaire doit être un montant valide en DT");
                txtUnitPrice.Focus();
                return false;
            }
            
            int stockQuantity;
            if (!int.TryParse(txtStockQuantity.Text, out stockQuantity) || stockQuantity < 0)
            {
                ShowError("La quantité en stock doit être un nombre positif");
                txtStockQuantity.Focus();
                return false;
            }
            
            int minimumStock;
            if (!int.TryParse(txtMinimumStock.Text, out minimumStock) || minimumStock < 0)
            {
                ShowError("Le stock minimum doit être un nombre positif");
                txtMinimumStock.Focus();
                return false;
            }
            
            return true;
        }

        #region Gestionnaires d'événements

        /// <summary>
        /// Gestionnaire pour la lecture d'un code-barres
        /// </summary>
        private void OnBarcodeScanned(object sender, BarcodeScannedEventArgs e)
        {
            // Remplir le champ code-barres ou rechercher l'article
            if (txtBarcode.Focused)
            {
                txtBarcode.Text = e.Barcode;
            }
            else
            {
                // Rechercher l'article par code-barres
                Article article = _inventoryService.GetArticleByBarcode(e.Barcode);
                if (article != null)
                {
                    // Sélectionner l'article dans la liste
                    for (int i = 0; i < _articles.Count; i++)
                    {
                        if (_articles[i].Id == article.Id)
                        {
                            lstArticles.SelectedIndex = i;
                            break;
                        }
                    }
                }
                else
                {
                    // Nouvel article, remplir le code-barres
                    txtBarcode.Text = e.Barcode;
                    ClearFields();
                    txtBarcode.Text = e.Barcode;
                }
            }
        }

        /// <summary>
        /// Sélection d'un article dans la liste
        /// </summary>
        private void lstArticles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lstArticles.SelectedItem is Article selectedArticle)
            {
                FillFields(selectedArticle);
            }
        }

        /// <summary>
        /// Ajouter un nouvel article
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (!ValidateFields()) return;
            
            try
            {
                Article article = CreateArticleFromFields();
                int newId = _inventoryService.CreateArticle(article);
                
                ShowInfo("Article ajouté avec succès (ID: " + newId + ")");
                LoadArticles();
                ClearFields();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'ajout: " + ex.Message);
            }
        }

        /// <summary>
        /// Mettre à jour l'article sélectionné
        /// </summary>
        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!ValidateFields()) return;
            
            try
            {
                Article article = CreateArticleFromFields();
                bool success = _inventoryService.UpdateArticle(article);
                
                if (success)
                {
                    ShowInfo("Article mis à jour avec succès");
                    LoadArticles();
                    ClearFields();
                }
                else
                {
                    ShowError("Erreur lors de la mise à jour");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la mise à jour: " + ex.Message);
            }
        }

        /// <summary>
        /// Supprimer l'article sélectionné
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (_currentArticle == null) return;
            
            if (MessageBox.Show("Voulez-vous vraiment supprimer cet article ?", "Confirmation",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2) == DialogResult.Yes)
            {
                try
                {
                    bool success = _inventoryService.DeleteArticle(_currentArticle.Id);
                    
                    if (success)
                    {
                        ShowInfo("Article supprimé avec succès");
                        LoadArticles();
                        ClearFields();
                    }
                    else
                    {
                        ShowError("Erreur lors de la suppression");
                    }
                }
                catch (Exception ex)
                {
                    ShowError("Erreur lors de la suppression: " + ex.Message);
                }
            }
        }

        /// <summary>
        /// Nouveau article (vider les champs)
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        /// <summary>
        /// Rechercher des articles
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            string searchTerm = txtSearch.Text.Trim();
            
            try
            {
                if (string.IsNullOrEmpty(searchTerm))
                {
                    LoadArticles();
                }
                else
                {
                    _articles = _inventoryService.SearchArticles(searchTerm);
                    lstArticles.DataSource = null;
                    lstArticles.DataSource = _articles;
                    lblTotalCount.Text = _articles.Count.ToString();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la recherche: " + ex.Message);
            }
        }

        /// <summary>
        /// Fermer le formulaire
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// Fermeture du formulaire
        /// </summary>
        private void ArticleForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_barcodeService != null)
            {
                _barcodeService.DisableScanner();
            }
        }

        #endregion

        #region Méthodes utilitaires

        /// <summary>
        /// Affiche un message d'information
        /// </summary>
        private void ShowInfo(string message)
        {
            MessageBox.Show(message, "Information",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1);
        }

        /// <summary>
        /// Affiche un message d'erreur
        /// </summary>
        private void ShowError(string message)
        {
            MessageBox.Show(message, "Erreur",
                MessageBoxButtons.OK, MessageBoxIcon.Exclamation,
                MessageBoxDefaultButton.Button1);
        }

        #endregion
    }
}
