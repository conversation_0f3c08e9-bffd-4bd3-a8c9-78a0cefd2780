@echo off
REM Diagnostic pour identifier le fichier avec caractere inattendu

echo ========================================
echo  DIAGNOSTIC CARACTERE INATTENDU
echo ========================================
echo.

echo Test de compilation fichier par fichier...
echo.

REM Créer le dossier de test
if not exist "bin" mkdir "bin"
if not exist "bin\Diagnostic" mkdir "bin\Diagnostic"

echo 1. Test Program.cs...
csc /target:library /out:bin\Diagnostic\test1.dll Program.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Program.cs contient un caractere inattendu
    echo Compilation de Program.cs:
    csc /target:library /out:bin\Diagnostic\test1.dll Program.cs
    goto :end
) else (
    echo [OK] Program.cs
)

echo 2. Test Properties\AssemblyInfo.cs...
csc /target:library /out:bin\Diagnostic\test2.dll Properties\AssemblyInfo.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Properties\AssemblyInfo.cs contient un caractere inattendu
    echo Compilation de Properties\AssemblyInfo.cs:
    csc /target:library /out:bin\Diagnostic\test2.dll Properties\AssemblyInfo.cs
    goto :end
) else (
    echo [OK] Properties\AssemblyInfo.cs
)

echo 3. Test Models\Article.cs...
csc /target:library /out:bin\Diagnostic\test3.dll Models\Article.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Models\Article.cs contient un caractere inattendu
    echo Compilation de Models\Article.cs:
    csc /target:library /out:bin\Diagnostic\test3.dll Models\Article.cs
    goto :end
) else (
    echo [OK] Models\Article.cs
)

echo 4. Test Models\InventoryItem.cs...
csc /target:library /out:bin\Diagnostic\test4.dll Models\InventoryItem.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Models\InventoryItem.cs contient un caractere inattendu
    echo Compilation de Models\InventoryItem.cs:
    csc /target:library /out:bin\Diagnostic\test4.dll Models\InventoryItem.cs
    goto :end
) else (
    echo [OK] Models\InventoryItem.cs
)

echo 5. Test Utils\CurrencyHelper.cs...
csc /target:library /out:bin\Diagnostic\test5.dll /reference:System.dll Utils\CurrencyHelper.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Utils\CurrencyHelper.cs contient un caractere inattendu
    echo Compilation de Utils\CurrencyHelper.cs:
    csc /target:library /out:bin\Diagnostic\test5.dll /reference:System.dll Utils\CurrencyHelper.cs
    goto :end
) else (
    echo [OK] Utils\CurrencyHelper.cs
)

echo 6. Test Services\BarcodeService.cs...
csc /target:library /out:bin\Diagnostic\test6.dll /reference:System.dll Services\BarcodeService.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Services\BarcodeService.cs contient un caractere inattendu
    echo Compilation de Services\BarcodeService.cs:
    csc /target:library /out:bin\Diagnostic\test6.dll /reference:System.dll Services\BarcodeService.cs
    goto :end
) else (
    echo [OK] Services\BarcodeService.cs
)

echo 7. Test Data\DatabaseHelper-SQLite-Simple.cs...
csc /target:library /out:bin\Diagnostic\test7.dll /reference:System.dll /reference:System.Data.dll /reference:lib\System.Data.SQLite.dll Data\DatabaseHelper-SQLite-Simple.cs Models\Article.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Data\DatabaseHelper-SQLite-Simple.cs contient un caractere inattendu
    echo Compilation de Data\DatabaseHelper-SQLite-Simple.cs:
    csc /target:library /out:bin\Diagnostic\test7.dll /reference:System.dll /reference:System.Data.dll /reference:lib\System.Data.SQLite.dll Data\DatabaseHelper-SQLite-Simple.cs Models\Article.cs
    goto :end
) else (
    echo [OK] Data\DatabaseHelper-SQLite-Simple.cs
)

echo 8. Test Services\InventoryService-SQLite-Simple.cs...
csc /target:library /out:bin\Diagnostic\test8.dll /reference:System.dll Services\InventoryService-SQLite-Simple.cs Models\Article.cs Models\InventoryItem.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Services\InventoryService-SQLite-Simple.cs contient un caractere inattendu
    echo Compilation de Services\InventoryService-SQLite-Simple.cs:
    csc /target:library /out:bin\Diagnostic\test8.dll /reference:System.dll Services\InventoryService-SQLite-Simple.cs Models\Article.cs Models\InventoryItem.cs
    goto :end
) else (
    echo [OK] Services\InventoryService-SQLite-Simple.cs
)

echo 9. Test Forms\MainForm.cs...
csc /target:library /out:bin\Diagnostic\test9.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\MainForm.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\MainForm.cs contient un caractere inattendu
    echo Compilation de Forms\MainForm.cs:
    csc /target:library /out:bin\Diagnostic\test9.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\MainForm.cs
    goto :end
) else (
    echo [OK] Forms\MainForm.cs
)

echo 10. Test Forms\MainForm.Designer.cs...
csc /target:library /out:bin\Diagnostic\test10.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\MainForm.Designer.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\MainForm.Designer.cs contient un caractere inattendu
    echo Compilation de Forms\MainForm.Designer.cs:
    csc /target:library /out:bin\Diagnostic\test10.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\MainForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\MainForm.Designer.cs
)

echo 11. Test Forms\ArticleForm.cs...
csc /target:library /out:bin\Diagnostic\test11.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ArticleForm.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ArticleForm.cs contient un caractere inattendu
    echo Compilation de Forms\ArticleForm.cs:
    csc /target:library /out:bin\Diagnostic\test11.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ArticleForm.cs
    goto :end
) else (
    echo [OK] Forms\ArticleForm.cs
)

echo 12. Test Forms\ArticleForm.Designer.cs...
csc /target:library /out:bin\Diagnostic\test12.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ArticleForm.Designer.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ArticleForm.Designer.cs contient un caractere inattendu
    echo Compilation de Forms\ArticleForm.Designer.cs:
    csc /target:library /out:bin\Diagnostic\test12.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ArticleForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\ArticleForm.Designer.cs
)

echo 13. Test Forms\ReportsForm.cs...
csc /target:library /out:bin\Diagnostic\test13.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ReportsForm.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ReportsForm.cs contient un caractere inattendu
    echo Compilation de Forms\ReportsForm.cs:
    csc /target:library /out:bin\Diagnostic\test13.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ReportsForm.cs
    goto :end
) else (
    echo [OK] Forms\ReportsForm.cs
)

echo 14. Test Forms\ReportsForm.Designer.cs...
csc /target:library /out:bin\Diagnostic\test14.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ReportsForm.Designer.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\ReportsForm.Designer.cs contient un caractere inattendu
    echo Compilation de Forms\ReportsForm.Designer.cs:
    csc /target:library /out:bin\Diagnostic\test14.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\ReportsForm.Designer.cs
    goto :end
) else (
    echo [OK] Forms\ReportsForm.Designer.cs
)

echo 15. Test Forms\InventoryForm-Simple.cs...
csc /target:library /out:bin\Diagnostic\test15.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\InventoryForm-Simple.cs >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERREUR] Forms\InventoryForm-Simple.cs contient un caractere inattendu
    echo Compilation de Forms\InventoryForm-Simple.cs:
    csc /target:library /out:bin\Diagnostic\test15.dll /reference:System.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll Forms\InventoryForm-Simple.cs
    goto :end
) else (
    echo [OK] Forms\InventoryForm-Simple.cs
)

echo.
echo ========================================
echo  TOUS LES FICHIERS SONT OK !
echo ========================================
echo.
echo Aucun fichier ne contient de caractere inattendu.
echo Le probleme vient peut-etre de la ligne de commande.
echo.
echo SOLUTIONS:
echo 1. Utiliser une commande plus simple
echo 2. Compiler avec un script different
echo 3. Verifier l'encodage des fichiers
echo.

:end
REM Nettoyer
rmdir /s /q "bin\Diagnostic" >nul 2>&1

pause
