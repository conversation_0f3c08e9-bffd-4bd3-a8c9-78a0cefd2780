@echo off
REM Compilation ultra-simple sans caracteres speciaux

echo Build ultra-simple en cours...

if not exist "lib\System.Data.SQLite.dll" (
    echo ERREUR: System.Data.SQLite.dll manquant
    echo Telecharger depuis: https://system.data.sqlite.org/downloads.html
    pause
    exit /b 1
)

if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation...

csc /target:winexe /out:bin\Release\InventoryApp-SQLite.exe /reference:System.dll /reference:System.Data.dll /reference:System.Drawing.dll /reference:System.Windows.Forms.dll /reference:System.Xml.dll /reference:Microsoft.VisualBasic.dll /reference:lib\System.Data.SQLite.dll /define:USE_SQLITE_DATABASE /nowarn:0168,0219 Program.cs Properties\AssemblyInfo.cs Models\Article.cs Models\InventoryItem.cs Data\DatabaseHelper-SQLite-Simple.cs Services\BarcodeService.cs Services\InventoryService-SQLite-Simple.cs Utils\CurrencyHelper.cs Forms\MainForm.cs Forms\MainForm.Designer.cs Forms\ArticleForm.cs Forms\ArticleForm.Designer.cs Forms\InventoryForm-Simple.cs Forms\ReportsForm.cs Forms\ReportsForm.Designer.cs

if %ERRORLEVEL% equ 0 (
    echo SUCCES !
    copy "lib\System.Data.SQLite.dll" "bin\Release\" >nul
    echo Fichier: bin\Release\InventoryApp-SQLite.exe
    echo.
    echo Voulez-vous tester ? (O/N)
    set /p TEST=
    if /i "%TEST%"=="O" start "Inventaire SQLite" "bin\Release\InventoryApp-SQLite.exe"
) else (
    echo ERREUR de compilation
    echo Code: %ERRORLEVEL%
    echo.
    echo Executez diagnose-syntax-error.bat pour identifier le probleme
)

pause
