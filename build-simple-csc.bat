@echo off
REM Compilation simple avec csc directement (pour Developer Command Prompt)

echo ========================================
echo  BUILD SIMPLE CSC - INVENTAIRE MC2100
echo ========================================
echo.

echo Test de la commande csc...
csc /? >nul 2>&1

if %ERRORLEVEL% neq 0 (
    echo ERREUR: Commande 'csc' non disponible
    echo.
    echo SOLUTION:
    echo 1. Ouvrir "Developer Command Prompt for VS 2022"
    echo 2. Executer ce script depuis cette invite
    echo.
    echo La "Developer Command Prompt" configure automatiquement
    echo tous les outils de compilation.
    echo.
    pause
    exit /b 1
)

echo Commande csc disponible !
echo.

REM Créer les dossiers
if not exist "bin" mkdir "bin"
if not exist "bin\Release" mkdir "bin\Release"

echo Compilation en cours...
echo.

REM Compiler directement avec csc
csc /target:winexe ^
    /out:bin\Release\InventoryApp.exe ^
    /reference:System.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    /reference:Microsoft.VisualBasic.dll ^
    /define:USE_XML_DATABASE ^
    /nowarn:0168,0219 ^
    Program.cs ^
    "Properties\AssemblyInfo.cs" ^
    "Models\Article.cs" ^
    "Models\InventoryItem.cs" ^
    "Data\DatabaseHelper-Simple.cs" ^
    "Services\BarcodeService.cs" ^
    "Services\InventoryService-Simple.cs" ^
    "Utils\CurrencyHelper.cs" ^
    "Forms\MainForm.cs" ^
    "Forms\MainForm.Designer.cs" ^
    "Forms\ArticleForm.cs" ^
    "Forms\ArticleForm.Designer.cs" ^
    "Forms\InventoryForm.cs" ^
    "Forms\InventoryForm.Designer.cs" ^
    "Forms\ReportsForm.cs" ^
    "Forms\ReportsForm.Designer.cs"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERREUR: Compilation echouee
    echo Code d'erreur: %ERRORLEVEL%
    echo.
    echo Les erreurs de compilation sont affichees ci-dessus.
    echo Verifiez la syntaxe des fichiers .cs
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  COMPILATION REUSSIE !
echo ========================================
echo.

if exist "bin\Release\InventoryApp.exe" (
    echo Fichier genere: bin\Release\InventoryApp.exe
    
    for %%I in ("bin\Release\InventoryApp.exe") do echo Taille: %%~zI octets
    
    echo.
    echo APPLICATION COMPLETE AVEC:
    echo • Devise Dinar Tunisien (DT)
    echo • Gestion articles complete
    echo • Module inventaire fonctionnel
    echo • Rapports detailles
    echo • Scanner codes-barres
    echo.
    
    echo DEPLOIEMENT SUR MC2100:
    echo 1. Copier bin\Release\InventoryApp.exe vers le terminal
    echo 2. Placer dans \Program Files\InventoryApp\
    echo 3. Lancer l'application
    echo.
    
    echo Voulez-vous tester l'application ? (O/N)
    set /p TEST_APP=
    if /i "%TEST_APP%"=="O" (
        echo Lancement de l'application...
        start "Inventaire MC2100 - Complet" "bin\Release\InventoryApp.exe"
    )
) else (
    echo ERREUR: Fichier executable non genere
    pause
    exit /b 1
)

echo.
echo Compilation terminee avec succes !
pause
