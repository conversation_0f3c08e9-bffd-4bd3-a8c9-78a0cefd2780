<?xml version="1.0" encoding="utf-8"?> 
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003"> 
  <PropertyGroup> 
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration> 
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform> 
    <OutputType>WinExe</OutputType> 
    <AssemblyName>InventoryApp</AssemblyName> 
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion> 
    <OutputPath>bin\Release\</OutputPath> 
    <DefineConstants>USE_XML_DATABASE</DefineConstants> 
  </PropertyGroup> 
  <ItemGroup> 
    <Reference Include="System" /> 
    <Reference Include="System.Data" /> 
    <Reference Include="System.Drawing" /> 
    <Reference Include="System.Windows.Forms" /> 
    <Reference Include="System.Xml" /> 
  </ItemGroup> 
  <ItemGroup> 
    <Compile Include="Program.cs" /> 
    <Compile Include="Properties\AssemblyInfo.cs" /> 
    <Compile Include="Models\Article.cs" /> 
    <Compile Include="Models\InventoryItem.cs" /> 
    <Compile Include="Data\DatabaseHelper-Simple.cs" /> 
    <Compile Include="Services\BarcodeService.cs" /> 
    <Compile Include="Services\InventoryService-Simple.cs" /> 
    <Compile Include="Forms\MainForm.cs"> 
      <SubType>Form</SubType> 
    </Compile> 
    <Compile Include="Forms\MainForm.Designer.cs"> 
      <DependentUpon>MainForm.cs</DependentUpon> 
    </Compile> 
    <Compile Include="Forms\ArticleForm.cs"> 
      <SubType>Form</SubType> 
    </Compile> 
    <Compile Include="Forms\ArticleForm.Designer.cs"> 
      <DependentUpon>ArticleForm.cs</DependentUpon> 
    </Compile> 
    <Compile Include="Forms\InventoryForm.cs"> 
      <SubType>Form</SubType> 
    </Compile> 
    <Compile Include="Forms\InventoryForm.Designer.cs"> 
      <DependentUpon>InventoryForm.cs</DependentUpon> 
    </Compile> 
    <Compile Include="Forms\ReportsForm.cs"> 
      <SubType>Form</SubType> 
    </Compile> 
    <Compile Include="Forms\ReportsForm.Designer.cs"> 
      <DependentUpon>ReportsForm.cs</DependentUpon> 
    </Compile> 
  </ItemGroup> 
  <ItemGroup> 
    <EmbeddedResource Include="Forms\MainForm.resx"> 
      <DependentUpon>MainForm.cs</DependentUpon> 
    </EmbeddedResource> 
    <EmbeddedResource Include="Forms\ArticleForm.resx"> 
      <DependentUpon>ArticleForm.cs</DependentUpon> 
    </EmbeddedResource> 
    <EmbeddedResource Include="Forms\InventoryForm.resx"> 
      <DependentUpon>InventoryForm.cs</DependentUpon> 
    </EmbeddedResource> 
    <EmbeddedResource Include="Forms\ReportsForm.resx"> 
      <DependentUpon>ReportsForm.cs</DependentUpon> 
    </EmbeddedResource> 
  </ItemGroup> 
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" /> 
</Project> 
